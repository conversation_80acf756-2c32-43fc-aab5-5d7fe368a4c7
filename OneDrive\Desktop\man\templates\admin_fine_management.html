{% extends "base.html" %}

{% block title %}Fine Management - Admin{% endblock %}

{% block sidebar %}
<!-- Dashboard -->
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>

<!-- Management Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-info" data-bs-toggle="collapse" href="#managementSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-cogs me-2"></i>Management
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="managementSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_books') }}">
                <i class="fas fa-book me-2"></i>Manage Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_ebooks') }}">
                <i class="fas fa-tablet-alt me-2"></i>Manage E-Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_students') }}">
                <i class="fas fa-graduation-cap me-2"></i>Manage Students
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_librarians') }}">
                <i class="fas fa-user-tie me-2"></i>Manage Librarians
            </a>
        </div>
    </div>
</div>

<!-- Circulation Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-primary" data-bs-toggle="collapse" href="#circulationSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-exchange-alt me-2"></i>Circulation
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="circulationSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_circulation_counter') }}">
                <i class="fas fa-desktop me-2"></i>Counter Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_issue_history') }}">
                <i class="fas fa-history me-2"></i>Issue History
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bulk_operations') }}">
                <i class="fas fa-layer-group me-2"></i>Bulk Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_payment_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Payment Management
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_management') }}">
                <i class="fas fa-tools me-2"></i>Binding & Maintenance
            </a>
            <a class="nav-link py-1 active" href="{{ url_for('admin_fine_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Fine Management
            </a>
        </div>
    </div>
</div>

<!-- Reports Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-success" data-bs-toggle="collapse" href="#reportsSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-chart-bar me-2"></i>Reports
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="reportsSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_library_connection_report') }}">
                <i class="fas fa-wifi me-2"></i>Library Connection
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_access_register_report') }}">
                <i class="fas fa-sign-in-alt me-2"></i>Access Register
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bibliography_report') }}">
                <i class="fas fa-book-open me-2"></i>Bibliography
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_counter_report') }}">
                <i class="fas fa-calculator me-2"></i>Counter Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_statistics_report') }}">
                <i class="fas fa-chart-line me-2"></i>Statistics
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_report') }}">
                <i class="fas fa-tools me-2"></i>Binding Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_database_report') }}">
                <i class="fas fa-database me-2"></i>Database Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_member_report') }}">
                <i class="fas fa-users me-2"></i>Member Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_resource_report') }}">
                <i class="fas fa-archive me-2"></i>Resource Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_no_dues_report') }}">
                <i class="fas fa-check-circle me-2"></i>No Dues Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_qb_report') }}">
                <i class="fas fa-money-check-alt me-2"></i>QB Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_transfer_report') }}">
                <i class="fas fa-exchange-alt me-2"></i>Transfer Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_missing_report') }}">
                <i class="fas fa-exclamation-triangle me-2"></i>Missing Report
            </a>
        </div>
    </div>
</div>

<!-- System Administration -->
<div class="nav-item">
    <a class="nav-link fw-bold text-warning" data-bs-toggle="collapse" href="#systemSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-server me-2"></i>System
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="systemSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_settings') }}">
                <i class="fas fa-cog me-2"></i>System Settings
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-money-bill-wave me-3 text-warning"></i>Fine Management</h2>
        <p class="text-muted mb-0">Add, modify, and manage fines for library users</p>
    </div>
    <div>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addFineModal">
            <i class="fas fa-plus me-2"></i>Add Fine
        </button>
        <button class="btn btn-success ms-2" onclick="exportFines()">
            <i class="fas fa-file-excel me-2"></i>Export
        </button>
    </div>
</div>

<!-- Fine Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-danger">
                    <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                </div>
                <h4 class="text-danger" id="totalFines">₹0</h4>
                <p class="text-muted mb-0 small">Total Pending Fines</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-success">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                </div>
                <h4 class="text-success" id="collectedFines">₹0</h4>
                <p class="text-muted mb-0 small">Collected Today</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-warning">
                    <i class="fas fa-users fa-2x mb-2"></i>
                </div>
                <h4 class="text-warning" id="usersWithFines">0</h4>
                <p class="text-muted mb-0 small">Users with Fines</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-info">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                </div>
                <h4 class="text-info" id="overdueFines">0</h4>
                <p class="text-muted mb-0 small">Overdue Fines</p>
            </div>
        </div>
    </div>
</div>

<!-- Filter Section -->
<div class="card autolib-card mb-4">
    <div class="card-header bg-gradient-warning text-white">
        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Fine Filters</h5>
    </div>
    <div class="card-body">
        <form id="filterForm">
            <div class="row">
                <div class="col-md-3">
                    <label for="fineStatus" class="form-label">Status</label>
                    <select class="form-select" id="fineStatus" name="fineStatus">
                        <option value="">All Fines</option>
                        <option value="pending">Pending</option>
                        <option value="paid">Paid</option>
                        <option value="waived">Waived</option>
                        <option value="overdue">Overdue</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="fineType" class="form-label">Fine Type</label>
                    <select class="form-select" id="fineType" name="fineType">
                        <option value="">All Types</option>
                        <option value="overdue">Overdue Book</option>
                        <option value="damage">Book Damage</option>
                        <option value="lost">Lost Book</option>
                        <option value="late_return">Late Return</option>
                        <option value="other">Other</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="department" class="form-label">Department</label>
                    <select class="form-select" id="department" name="department">
                        <option value="">All Departments</option>
                        <option value="CSE">Computer Science</option>
                        <option value="IT">Information Technology</option>
                        <option value="ECE">Electronics & Communication</option>
                        <option value="EEE">Electrical & Electronics</option>
                        <option value="MECH">Mechanical</option>
                        <option value="CIVIL">Civil</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="searchUser" class="form-label">Search User</label>
                    <input type="text" class="form-control" id="searchUser" placeholder="Name or ID">
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button type="button" class="btn btn-warning" onclick="loadFines()">
                        <i class="fas fa-search me-2"></i>Search
                    </button>
                    <button type="button" class="btn btn-outline-secondary ms-2" onclick="resetFilters()">
                        <i class="fas fa-refresh me-2"></i>Reset
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Fines Table -->
<div class="card autolib-card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-table me-2"></i>Current Fines</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="finesTable">
                <thead class="table-dark">
                    <tr>
                        <th>Fine ID</th>
                        <th>User</th>
                        <th>Department</th>
                        <th>Fine Type</th>
                        <th>Amount (₹)</th>
                        <th>Issue Date</th>
                        <th>Due Date</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="9" class="text-center text-muted">
                            <i class="fas fa-info-circle me-2"></i>No fines found. All users are clear!
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Fine Modal -->
<div class="modal fade" id="addFineModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-white">
                <h5 class="modal-title"><i class="fas fa-plus me-2"></i>Add New Fine</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addFineForm">
                    <div class="row">
                        <div class="col-md-6">
                            <label for="userId" class="form-label">User ID *</label>
                            <input type="text" class="form-control" id="userId" name="userId" required>
                            <div class="form-text">Enter student/user ID</div>
                        </div>
                        <div class="col-md-6">
                            <label for="userName" class="form-label">User Name</label>
                            <input type="text" class="form-control" id="userName" name="userName" readonly>
                            <div class="form-text">Auto-filled from user ID</div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label for="fineTypeModal" class="form-label">Fine Type *</label>
                            <select class="form-select" id="fineTypeModal" name="fineTypeModal" required>
                                <option value="">Select Fine Type</option>
                                <option value="overdue">Overdue Book</option>
                                <option value="damage">Book Damage</option>
                                <option value="lost">Lost Book</option>
                                <option value="late_return">Late Return</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="fineAmount" class="form-label">Fine Amount (₹) *</label>
                            <input type="number" class="form-control" id="fineAmount" name="fineAmount" min="1" step="0.01" required>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label for="bookId" class="form-label">Book ID (if applicable)</label>
                            <input type="text" class="form-control" id="bookId" name="bookId">
                        </div>
                        <div class="col-md-6">
                            <label for="dueDate" class="form-label">Due Date *</label>
                            <input type="date" class="form-control" id="dueDate" name="dueDate" required>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <label for="fineReason" class="form-label">Reason/Description *</label>
                            <textarea class="form-control" id="fineReason" name="fineReason" rows="3" required></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" onclick="addFine()">
                    <i class="fas fa-plus me-2"></i>Add Fine
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    loadFines();
    
    // Auto-fill user name when user ID is entered
    $('#userId').on('blur', function() {
        const userId = $(this).val();
        if (userId) {
            // Simulate user lookup - replace with actual API call
            $('#userName').val('Sample User Name');
        }
    });
    
    // Set default due date (7 days from today)
    const defaultDueDate = new Date();
    defaultDueDate.setDate(defaultDueDate.getDate() + 7);
    $('#dueDate').val(defaultDueDate.toISOString().split('T')[0]);
});

function loadFines() {
    // Show loading
    $('#finesTable tbody').html('<tr><td colspan="9" class="text-center"><i class="fas fa-spinner fa-spin me-2"></i>Loading fines...</td></tr>');
    
    // Update statistics - all zeros for clean database
    $('#totalFines').text('₹0');
    $('#collectedFines').text('₹0');
    $('#usersWithFines').text('0');
    $('#overdueFines').text('0');
    
    // Simulate API call - replace with actual implementation
    setTimeout(() => {
        $('#finesTable tbody').html(`
            <tr>
                <td colspan="9" class="text-center text-success">
                    <i class="fas fa-check-circle me-2"></i>No fines found. All users are clear!
                </td>
            </tr>
        `);
    }, 1000);
}

function addFine() {
    const formData = {
        userId: $('#userId').val(),
        fineType: $('#fineTypeModal').val(),
        fineAmount: $('#fineAmount').val(),
        bookId: $('#bookId').val(),
        dueDate: $('#dueDate').val(),
        reason: $('#fineReason').val()
    };
    
    // Validate required fields
    if (!formData.userId || !formData.fineType || !formData.fineAmount || !formData.dueDate || !formData.reason) {
        alert('Please fill all required fields.');
        return;
    }
    
    // Simulate API call - replace with actual implementation
    alert('Fine added successfully!');
    $('#addFineModal').modal('hide');
    $('#addFineForm')[0].reset();
    loadFines();
}

function collectFine(fineId) {
    if (confirm('Mark this fine as collected?')) {
        alert('Fine collected successfully!');
        loadFines();
    }
}

function waiveFine(fineId) {
    if (confirm('Waive this fine? This action cannot be undone.')) {
        alert('Fine waived successfully!');
        loadFines();
    }
}

function editFine(fineId) {
    alert('Edit fine functionality would be implemented here.');
}

function resetFilters() {
    $('#filterForm')[0].reset();
    loadFines();
}

function exportFines() {
    alert('Exporting fines data...');
}
</script>
{% endblock %}
