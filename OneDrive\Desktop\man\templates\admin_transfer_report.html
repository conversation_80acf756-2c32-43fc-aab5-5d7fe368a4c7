{% extends "base.html" %}

{% block title %}Transfer Report - Admin{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>

<!-- Fixed Circulation Menu -->
<div class="nav-item">
    <a class="nav-link fw-bold text-primary" data-bs-toggle="collapse" href="#circulationSubmenu" role="button" aria-expanded="false">
        <i class="fas fa-exchange-alt me-2"></i>Circulation
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse" id="circulationSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_circulation_counter') }}">
                <i class="fas fa-desktop me-2"></i>Counter
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bulk_operations') }}">
                <i class="fas fa-layer-group me-2"></i>Bulk Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_payment_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Payment Management
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_management') }}">
                <i class="fas fa-tools me-2"></i>Binding & Maintenance
            </a>
        </div>
    </div>
</div>

<!-- Fixed Reports Menu -->
<div class="nav-item">
    <a class="nav-link fw-bold text-success" data-bs-toggle="collapse" href="#reportsSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-chart-bar me-2"></i>Reports
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="reportsSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_library_connection_report') }}">
                <i class="fas fa-wifi me-2"></i>Library Connection
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_access_register_report') }}">
                <i class="fas fa-sign-in-alt me-2"></i>Access Register
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bibliography_report') }}">
                <i class="fas fa-book-open me-2"></i>Bibliography
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_counter_report') }}">
                <i class="fas fa-calculator me-2"></i>Counter Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_statistics_report') }}">
                <i class="fas fa-chart-line me-2"></i>Statistics
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_report') }}">
                <i class="fas fa-tools me-2"></i>Binding Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_database_report') }}">
                <i class="fas fa-database me-2"></i>Database Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_member_report') }}">
                <i class="fas fa-users me-2"></i>Member Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_resource_report') }}">
                <i class="fas fa-archive me-2"></i>Resource Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_no_dues_report') }}">
                <i class="fas fa-check-circle me-2"></i>No Dues Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_qb_report') }}">
                <i class="fas fa-money-check-alt me-2"></i>QB Report
            </a>
            <a class="nav-link py-1 active" href="{{ url_for('admin_transfer_report') }}">
                <i class="fas fa-exchange-alt me-2"></i>Transfer Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_missing_report') }}">
                <i class="fas fa-exclamation-triangle me-2"></i>Missing Report
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-exchange-alt me-3 text-primary"></i>Transfer Report</h2>
        <p class="text-muted mb-0">Inter-library transfers, book movement tracking, and transfer history</p>
    </div>
    <div>
        <button class="btn btn-success" onclick="exportReport('excel')">
            <i class="fas fa-file-excel me-2"></i>Export Excel
        </button>
        <button class="btn btn-danger ms-2" onclick="exportReport('pdf')">
            <i class="fas fa-file-pdf me-2"></i>Export PDF
        </button>
    </div>
</div>

<!-- Filter Section -->
<div class="card autolib-card mb-4">
    <div class="card-header bg-gradient-primary text-white">
        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Transfer Filters</h5>
    </div>
    <div class="card-body">
        <form id="filterForm">
            <div class="row">
                <div class="col-md-3">
                    <label for="transferType" class="form-label">Transfer Type</label>
                    <select class="form-select" id="transferType" name="transferType">
                        <option value="">All Transfers</option>
                        <option value="inter_library">Inter-Library</option>
                        <option value="department">Department Transfer</option>
                        <option value="location">Location Change</option>
                        <option value="maintenance">Maintenance Transfer</option>
                        <option value="disposal">Disposal</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="transferStatus" class="form-label">Status</label>
                    <select class="form-select" id="transferStatus" name="transferStatus">
                        <option value="">All Status</option>
                        <option value="pending">Pending</option>
                        <option value="in_transit">In Transit</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                        <option value="rejected">Rejected</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="dateFrom" class="form-label">From Date</label>
                    <input type="date" class="form-control" id="dateFrom" name="dateFrom">
                </div>
                <div class="col-md-3">
                    <label for="dateTo" class="form-label">To Date</label>
                    <input type="date" class="form-control" id="dateTo" name="dateTo">
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-4">
                    <label for="sourceLocation" class="form-label">Source Location</label>
                    <input type="text" class="form-control" id="sourceLocation" placeholder="Enter source library/department">
                </div>
                <div class="col-md-4">
                    <label for="destinationLocation" class="form-label">Destination Location</label>
                    <input type="text" class="form-control" id="destinationLocation" placeholder="Enter destination library/department">
                </div>
                <div class="col-md-4">
                    <label for="searchTransfer" class="form-label">Search</label>
                    <input type="text" class="form-control" id="searchTransfer" placeholder="Transfer ID, book title, or access number">
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button type="button" class="btn btn-primary" onclick="generateReport()">
                        <i class="fas fa-search me-2"></i>Generate Report
                    </button>
                    <button type="button" class="btn btn-outline-secondary ms-2" onclick="resetFilters()">
                        <i class="fas fa-refresh me-2"></i>Reset
                    </button>
                    <button type="button" class="btn btn-info ms-2" onclick="newTransfer()">
                        <i class="fas fa-plus me-2"></i>New Transfer
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Transfer Statistics -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-primary">
                    <i class="fas fa-exchange-alt fa-2x mb-2"></i>
                </div>
                <h4 class="text-primary" id="totalTransfers">0</h4>
                <p class="text-muted mb-0 small">Total Transfers</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-warning">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                </div>
                <h4 class="text-warning" id="pendingTransfers">0</h4>
                <p class="text-muted mb-0 small">Pending</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-info">
                    <i class="fas fa-truck fa-2x mb-2"></i>
                </div>
                <h4 class="text-info" id="inTransitTransfers">0</h4>
                <p class="text-muted mb-0 small">In Transit</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-success">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                </div>
                <h4 class="text-success" id="completedTransfers">0</h4>
                <p class="text-muted mb-0 small">Completed</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-danger">
                    <i class="fas fa-times-circle fa-2x mb-2"></i>
                </div>
                <h4 class="text-danger" id="cancelledTransfers">0</h4>
                <p class="text-muted mb-0 small">Cancelled</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-dark">
                    <i class="fas fa-book fa-2x mb-2"></i>
                </div>
                <h4 class="text-dark" id="booksTransferred">0</h4>
                <p class="text-muted mb-0 small">Books Transferred</p>
            </div>
        </div>
    </div>
</div>

<!-- Transfer Tabs -->
<ul class="nav nav-tabs mb-4" id="transferTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="active-transfers-tab" data-bs-toggle="tab" data-bs-target="#active-transfers" type="button" role="tab">
            <i class="fas fa-clock me-2"></i>Active Transfers
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="transfer-history-tab" data-bs-toggle="tab" data-bs-target="#transfer-history" type="button" role="tab">
            <i class="fas fa-history me-2"></i>Transfer History
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="transfer-analytics-tab" data-bs-toggle="tab" data-bs-target="#transfer-analytics" type="button" role="tab">
            <i class="fas fa-chart-bar me-2"></i>Transfer Analytics
        </button>
    </li>
</ul>

<div class="tab-content" id="transferTabContent">
    <!-- Active Transfers Tab -->
    <div class="tab-pane fade show active" id="active-transfers" role="tabpanel">
        <div class="card autolib-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Active Transfer Requests</h5>
                <div>
                    <span class="badge bg-warning" id="pendingCount">0 Pending</span>
                    <span class="badge bg-info ms-2" id="transitCount">0 In Transit</span>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="activeTransfersTable">
                        <thead class="table-dark">
                            <tr>
                                <th>Transfer ID</th>
                                <th>Book Details</th>
                                <th>Type</th>
                                <th>From</th>
                                <th>To</th>
                                <th>Requested Date</th>
                                <th>Status</th>
                                <th>Priority</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="9" class="text-center text-muted">
                                    <i class="fas fa-info-circle me-2"></i>No active transfers found. All transfers are up to date.
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Transfer History Tab -->
    <div class="tab-pane fade" id="transfer-history" role="tabpanel">
        <div class="card autolib-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-history me-2"></i>Complete Transfer History</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="transferHistoryTable">
                        <thead class="table-dark">
                            <tr>
                                <th>Transfer ID</th>
                                <th>Book Details</th>
                                <th>Type</th>
                                <th>From</th>
                                <th>To</th>
                                <th>Request Date</th>
                                <th>Completion Date</th>
                                <th>Duration</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="9" class="text-center text-muted">
                                    <i class="fas fa-info-circle me-2"></i>No transfer history available.
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Transfer Analytics Tab -->
    <div class="tab-pane fade" id="transfer-analytics" role="tabpanel">
        <div class="row">
            <div class="col-md-6">
                <div class="card autolib-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Transfer Types Distribution</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="transferTypesChart" height="300"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card autolib-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Monthly Transfer Trends</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="transferTrendsChart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card autolib-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-stopwatch me-2"></i>Transfer Performance Metrics</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-primary" id="avgTransferTime">0 days</h4>
                                    <p class="text-muted">Average Transfer Time</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-success" id="successRate">0%</h4>
                                    <p class="text-muted">Success Rate</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-warning" id="avgDelay">0 days</h4>
                                    <p class="text-muted">Average Delay</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-info" id="mostActiveRoute">N/A</h4>
                                    <p class="text-muted">Most Active Route</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // Set default dates (last 30 days)
    const today = new Date();
    const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    $('#dateTo').val(today.toISOString().split('T')[0]);
    $('#dateFrom').val(monthAgo.toISOString().split('T')[0]);
    
    // Load initial data
    generateReport();
    
    // Initialize charts
    initializeTransferCharts();
});

function generateReport() {
    const filters = {
        transferType: $('#transferType').val(),
        transferStatus: $('#transferStatus').val(),
        dateFrom: $('#dateFrom').val(),
        dateTo: $('#dateTo').val(),
        sourceLocation: $('#sourceLocation').val(),
        destinationLocation: $('#destinationLocation').val(),
        searchTransfer: $('#searchTransfer').val()
    };
    
    // Show loading
    $('#activeTransfersTable tbody').html('<tr><td colspan="9" class="text-center"><i class="fas fa-spinner fa-spin me-2"></i>Loading transfer data...</td></tr>');
    
    // Simulate API call - replace with actual implementation
    setTimeout(() => {
        loadTransferData(filters);
    }, 1000);
}

function loadTransferData(filters) {
    // Since database is clean, show zero data
    const transferData = []; // Empty array for clean database
    
    // Update statistics - all zeros for clean database
    $('#totalTransfers').text('0');
    $('#pendingTransfers').text('0');
    $('#inTransitTransfers').text('0');
    $('#completedTransfers').text('0');
    $('#cancelledTransfers').text('0');
    $('#booksTransferred').text('0');
    $('#pendingCount').text('0 Pending');
    $('#transitCount').text('0 In Transit');
    
    // Update performance metrics
    $('#avgTransferTime').text('0 days');
    $('#successRate').text('100%');
    $('#avgDelay').text('0 days');
    $('#mostActiveRoute').text('N/A');
    
    // Update active transfers table
    if (transferData.length === 0) {
        $('#activeTransfersTable tbody').html(`
            <tr>
                <td colspan="9" class="text-center text-success">
                    <i class="fas fa-check-circle me-2"></i>No active transfers found. All transfers are up to date.
                </td>
            </tr>
        `);
        
        $('#transferHistoryTable tbody').html(`
            <tr>
                <td colspan="9" class="text-center text-muted">
                    <i class="fas fa-info-circle me-2"></i>No transfer history available.
                </td>
            </tr>
        `);
    }
    
    // Update charts with zero data
    updateTransferCharts([]);
}

function initializeTransferCharts() {
    // Transfer types chart
    const ctx1 = document.getElementById('transferTypesChart').getContext('2d');
    window.transferTypesChart = new Chart(ctx1, {
        type: 'doughnut',
        data: {
            labels: ['Inter-Library', 'Department', 'Location', 'Maintenance', 'Disposal'],
            datasets: [{
                data: [0, 0, 0, 0, 0],
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0',
                    '#9966FF'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    
    // Transfer trends chart
    const ctx2 = document.getElementById('transferTrendsChart').getContext('2d');
    window.transferTrendsChart = new Chart(ctx2, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'Transfers',
                data: [0, 0, 0, 0, 0, 0],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function updateTransferCharts(data) {
    if (window.transferTypesChart && data.length === 0) {
        window.transferTypesChart.data.datasets[0].data = [0, 0, 0, 0, 0];
        window.transferTypesChart.update();
    }
    
    if (window.transferTrendsChart && data.length === 0) {
        window.transferTrendsChart.data.datasets[0].data = [0, 0, 0, 0, 0, 0];
        window.transferTrendsChart.update();
    }
}

function resetFilters() {
    $('#filterForm')[0].reset();
    const today = new Date();
    const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    $('#dateTo').val(today.toISOString().split('T')[0]);
    $('#dateFrom').val(monthAgo.toISOString().split('T')[0]);
    
    generateReport();
}

function newTransfer() {
    alert('New transfer request form would be implemented here.');
}

function approveTransfer(transferId) {
    if (confirm(`Approve transfer ${transferId}?`)) {
        alert('Transfer approved successfully!');
        generateReport();
    }
}

function rejectTransfer(transferId) {
    if (confirm(`Reject transfer ${transferId}?`)) {
        alert('Transfer rejected successfully!');
        generateReport();
    }
}

function trackTransfer(transferId) {
    alert(`Tracking details for transfer ${transferId} would be shown here.`);
}

function exportReport(format) {
    alert(`Exporting Transfer Report as ${format.toUpperCase()}...`);
}
</script>
{% endblock %}
