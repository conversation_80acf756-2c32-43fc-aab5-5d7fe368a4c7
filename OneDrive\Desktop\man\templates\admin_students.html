{% extends "base.html" %}

{% block title %}Manage Students - Admin{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>
<a class="nav-link" href="{{ url_for('admin_books') }}">
    <i class="fas fa-book me-2"></i>Manage Books
</a>
<a class="nav-link" href="{{ url_for('admin_librarians') }}">
    <i class="fas fa-user-tie me-2"></i>Manage Librarians
</a>
<a class="nav-link active" href="{{ url_for('admin_students') }}">
    <i class="fas fa-graduation-cap me-2"></i>Manage Students
</a>
<a class="nav-link" href="{{ url_for('admin_bulk_users') }}">
    <i class="fas fa-upload me-2"></i>Bulk Create Users
</a>
<a class="nav-link" href="{{ url_for('admin_settings') }}">
    <i class="fas fa-cog me-2"></i>Library Settings
</a>
<a class="nav-link" href="{{ url_for('admin_issue_history') }}">
    <i class="fas fa-history me-2"></i>Issue History
</a>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-graduation-cap me-3"></i>Manage Students</h2>
    <div>
        <a href="{{ url_for('admin_bulk_users') }}" class="btn btn-info btn-custom me-2">
            <i class="fas fa-upload me-2"></i>Bulk Create Users
        </a>
        <a href="{{ url_for('admin_add_student') }}" class="btn btn-primary btn-custom">
            <i class="fas fa-plus me-2"></i>Add New Student
        </a>
    </div>
</div>

<!-- Search Bar -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="input-group">
            <input type="text" class="form-control" id="searchInput" placeholder="Search by name, roll number, username, or department..." onkeyup="searchStudents()">
            <button class="btn btn-outline-secondary" type="button" onclick="clearSearch()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>
    <div class="col-md-6">
        <div class="d-flex justify-content-end">
            <select class="form-select" id="departmentFilter" onchange="filterByDepartment()" style="max-width: 200px;">
                <option value="">All Departments</option>
                <option value="CSE">CSE</option>
                <option value="IT">IT</option>
                <option value="ECE">ECE</option>
                <option value="EEE">EEE</option>
                <option value="BME">BME</option>
                <option value="AERO">AERO</option>
                <option value="AIDS">AIDS</option>
                <option value="CSBS">CSBS</option>
                <option value="CIVIL">CIVIL</option>
                <option value="PCT">PCT</option>
            </select>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">Student Accounts</h5>
    </div>
    <div class="card-body">
        {% if students %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>User ID</th>
                        <th>Name</th>
                        <th>Username</th>
                        <th>Roll Number</th>
                        <th>Department</th>
                        <th>Designation</th>
                        <th>Validity Date</th>
                        <th>Active Issues</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="studentsTableBody">
                    {% for student in students %}
                    <tr class="student-row" 
                        data-name="{{ student.name|lower }}" 
                        data-roll="{{ student.roll_number|lower }}" 
                        data-username="{{ (student.username or student.email)|lower }}"
                        data-department="{{ (student.department or '')|lower }}">
                        <td><strong>{{ student.user_id or student.id }}</strong></td>
                        <td>{{ student.name }}</td>
                        <td><code>{{ student.username or student.email }}</code></td>
                        <td>{{ student.roll_number }}</td>
                        <td>
                            <span class="badge bg-info">{{ student.department or 'N/A' }}</span>
                        </td>
                        <td>
                            <span class="badge {% if student.designation == 'Staff' %}bg-warning{% else %}bg-primary{% endif %}">
                                {{ student.designation or 'Student' }}
                            </span>
                        </td>
                        <td>
                            {% if student.validity_date %}
                                {{ student.validity_date.strftime('%Y-%m-%d') }}
                            {% else %}
                                <span class="text-muted">N/A</span>
                            {% endif %}
                        </td>
                        <td>
                            {% set active_issues = student.issues|selectattr('return_date', 'none')|list|length %}
                            <span class="badge {% if active_issues > 0 %}bg-warning{% else %}bg-success{% endif %}">
                                {{ active_issues }}
                            </span>
                        </td>
                        <td>
                            <span class="badge bg-success">Active</span>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-info me-1" 
                                    onclick="viewStudentDetails({{ student.id }})" 
                                    title="View Details">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" 
                                    onclick="deleteStudent({{ student.id }}, '{{ student.name }}')" 
                                    title="Delete Student">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-graduation-cap fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No students found</h5>
            <p class="text-muted">Start by adding your first student account.</p>
            <a href="{{ url_for('admin_add_student') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Add First Student
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Student Statistics -->
<div class="row mt-4">
    <div class="col-md-4">
        <div class="card stat-card stat-card-primary">
            <div class="card-body text-center">
                <i class="fas fa-graduation-cap fa-2x text-primary mb-2"></i>
                <h4>{{ students|length }}</h4>
                <p class="text-muted mb-0">Total Students</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card stat-card stat-card-success">
            <div class="card-body text-center">
                <i class="fas fa-book-open fa-2x text-success mb-2"></i>
                <h4>
                    {% set active_issues = [] %}
                    {% for student in students %}
                        {% for issue in student.issues %}
                            {% if issue.return_date is none %}
                                {% set _ = active_issues.append(issue) %}
                            {% endif %}
                        {% endfor %}
                    {% endfor %}
                    {{ active_issues|length }}
                </h4>
                <p class="text-muted mb-0">Active Issues</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card stat-card stat-card-info">
            <div class="card-body text-center">
                <i class="fas fa-user-check fa-2x text-info mb-2"></i>
                <h4>{{ students|length }}</h4>
                <p class="text-muted mb-0">Active Students</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function searchStudents() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const rows = document.querySelectorAll('.student-row');
    let visibleCount = 0;
    
    rows.forEach(row => {
        const name = row.getAttribute('data-name');
        const roll = row.getAttribute('data-roll');
        const username = row.getAttribute('data-username');
        const department = row.getAttribute('data-department');
        
        if (name.includes(searchTerm) || 
            roll.includes(searchTerm) || 
            username.includes(searchTerm) || 
            department.includes(searchTerm)) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });
    
    updateSearchResults(visibleCount);
}

function filterByDepartment() {
    const selectedDept = document.getElementById('departmentFilter').value.toLowerCase();
    const rows = document.querySelectorAll('.student-row');
    let visibleCount = 0;
    
    rows.forEach(row => {
        const department = row.getAttribute('data-department');
        
        if (selectedDept === '' || department === selectedDept) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });
    
    // Clear search input when filtering by department
    document.getElementById('searchInput').value = '';
    updateSearchResults(visibleCount);
}

function clearSearch() {
    document.getElementById('searchInput').value = '';
    document.getElementById('departmentFilter').value = '';
    const rows = document.querySelectorAll('.student-row');
    rows.forEach(row => {
        row.style.display = '';
    });
    updateSearchResults(rows.length);
}

function updateSearchResults(count) {
    // You can add a results counter here if needed
    console.log(`Showing ${count} students`);
}

function deleteStudent(id, name) {
    if (confirm(`Are you sure you want to delete student "${name}"? This action cannot be undone.`)) {
        // Create a form and submit it
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = '/admin/delete_student/' + id;
        document.body.appendChild(form);
        form.submit();
    }
}

function viewStudentDetails(id) {
    // Redirect to student details page
    window.location.href = `/admin/student_details/${id}`;
}
</script>
{% endblock %}
