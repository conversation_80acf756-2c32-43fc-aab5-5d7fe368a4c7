{% extends "base.html" %}

{% block title %}Statistics Report - Admin{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>
<a class="nav-link" href="{{ url_for('admin_books') }}">
    <i class="fas fa-book me-2"></i>Manage Books
</a>
<a class="nav-link" href="{{ url_for('admin_ebooks') }}">
    <i class="fas fa-tablet-alt me-2"></i>Manage E-Books
</a>
<a class="nav-link" href="{{ url_for('admin_librarians') }}">
    <i class="fas fa-user-tie me-2"></i>Manage Librarians
</a>
<a class="nav-link" href="{{ url_for('admin_students') }}">
    <i class="fas fa-graduation-cap me-2"></i>Manage Students
</a>

<!-- Fixed Circulation Menu -->
<div class="nav-item">
    <a class="nav-link fw-bold text-primary" data-bs-toggle="collapse" href="#circulationSubmenu" role="button" aria-expanded="false">
        <i class="fas fa-exchange-alt me-2"></i>Circulation
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse" id="circulationSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_circulation_counter') }}">
                <i class="fas fa-desktop me-2"></i>Counter
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bulk_operations') }}">
                <i class="fas fa-layer-group me-2"></i>Bulk Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_payment_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Payment Management
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_management') }}">
                <i class="fas fa-tools me-2"></i>Binding & Maintenance
            </a>
        </div>
    </div>
</div>

<!-- Fixed Reports Menu -->
<div class="nav-item">
    <a class="nav-link fw-bold text-success" data-bs-toggle="collapse" href="#reportsSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-chart-bar me-2"></i>Reports
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="reportsSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_library_connection_report') }}">
                <i class="fas fa-wifi me-2"></i>Library Connection
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_access_register_report') }}">
                <i class="fas fa-sign-in-alt me-2"></i>Access Register
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bibliography_report') }}">
                <i class="fas fa-book-open me-2"></i>Bibliography
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_counter_report') }}">
                <i class="fas fa-calculator me-2"></i>Counter Report
            </a>
            <a class="nav-link py-1 active" href="{{ url_for('admin_statistics_report') }}">
                <i class="fas fa-chart-line me-2"></i>Statistics
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_report') }}">
                <i class="fas fa-tools me-2"></i>Binding Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_database_report') }}">
                <i class="fas fa-database me-2"></i>Database Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_member_report') }}">
                <i class="fas fa-users me-2"></i>Member Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_resource_report') }}">
                <i class="fas fa-archive me-2"></i>Resource Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_no_dues_report') }}">
                <i class="fas fa-check-circle me-2"></i>No Dues Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_qb_report') }}">
                <i class="fas fa-money-check-alt me-2"></i>QB Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_transfer_report') }}">
                <i class="fas fa-exchange-alt me-2"></i>Transfer Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_missing_report') }}">
                <i class="fas fa-exclamation-triangle me-2"></i>Missing Report
            </a>
        </div>
    </div>
</div>

<a class="nav-link" href="{{ url_for('admin_issue_history') }}">
    <i class="fas fa-history me-2"></i>Issue History
</a>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-chart-line me-3 text-info"></i>Library Statistics Report</h2>
        <p class="text-muted mb-0">Comprehensive analytics and trends for library operations</p>
    </div>
    <div>
        <button class="btn btn-success" onclick="exportReport('excel')">
            <i class="fas fa-file-excel me-2"></i>Export Excel
        </button>
        <button class="btn btn-danger ms-2" onclick="exportReport('pdf')">
            <i class="fas fa-file-pdf me-2"></i>Export PDF
        </button>
    </div>
</div>

<!-- Time Period Selection -->
<div class="card autolib-card mb-4">
    <div class="card-header bg-gradient-info text-white">
        <h5 class="mb-0"><i class="fas fa-calendar me-2"></i>Analysis Period</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <label for="periodType" class="form-label">Period Type</label>
                <select class="form-select" id="periodType" onchange="updatePeriod()">
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly" selected>Monthly</option>
                    <option value="yearly">Yearly</option>
                    <option value="custom">Custom Range</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="dateFrom" class="form-label">From Date</label>
                <input type="date" class="form-control" id="dateFrom">
            </div>
            <div class="col-md-3">
                <label for="dateTo" class="form-label">To Date</label>
                <input type="date" class="form-control" id="dateTo">
            </div>
            <div class="col-md-3">
                <label for="department" class="form-label">Department Filter</label>
                <select class="form-select" id="department">
                    <option value="">All Departments</option>
                    <option value="CSE">Computer Science</option>
                    <option value="IT">Information Technology</option>
                    <option value="ECE">Electronics & Communication</option>
                    <option value="EEE">Electrical & Electronics</option>
                    <option value="MECH">Mechanical</option>
                    <option value="CIVIL">Civil</option>
                </select>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <button type="button" class="btn btn-info" onclick="generateStatistics()">
                    <i class="fas fa-chart-bar me-2"></i>Generate Statistics
                </button>
                <button type="button" class="btn btn-outline-secondary ms-2" onclick="resetFilters()">
                    <i class="fas fa-refresh me-2"></i>Reset
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Key Performance Indicators -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-primary">
                    <i class="fas fa-book fa-3x mb-3"></i>
                </div>
                <h3 class="text-primary" id="totalBooks">0</h3>
                <p class="text-muted mb-0">Total Books</p>
                <small class="text-success">
                    <i class="fas fa-arrow-up me-1"></i>
                    <span id="booksGrowth">+0%</span> from last period
                </small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-success">
                    <i class="fas fa-exchange-alt fa-3x mb-3"></i>
                </div>
                <h3 class="text-success" id="totalCirculation">0</h3>
                <p class="text-muted mb-0">Total Circulation</p>
                <small class="text-success">
                    <i class="fas fa-arrow-up me-1"></i>
                    <span id="circulationGrowth">+0%</span> from last period
                </small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-warning">
                    <i class="fas fa-users fa-3x mb-3"></i>
                </div>
                <h3 class="text-warning" id="activeMembers">0</h3>
                <p class="text-muted mb-0">Active Members</p>
                <small class="text-success">
                    <i class="fas fa-arrow-up me-1"></i>
                    <span id="membersGrowth">+0%</span> from last period
                </small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-danger">
                    <i class="fas fa-percentage fa-3x mb-3"></i>
                </div>
                <h3 class="text-danger" id="utilizationRate">0%</h3>
                <p class="text-muted mb-0">Utilization Rate</p>
                <small class="text-success">
                    <i class="fas fa-arrow-up me-1"></i>
                    <span id="utilizationGrowth">+0%</span> from last period
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card autolib-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Circulation Trends</h5>
            </div>
            <div class="card-body">
                <canvas id="circulationChart" height="300"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card autolib-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Department-wise Usage</h5>
            </div>
            <div class="card-body">
                <canvas id="departmentChart" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <div class="card autolib-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Popular Books</h5>
            </div>
            <div class="card-body">
                <canvas id="popularBooksChart" height="300"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card autolib-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Peak Hours Analysis</h5>
            </div>
            <div class="card-body">
                <canvas id="peakHoursChart" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Statistics Tables -->
<div class="row">
    <div class="col-md-6">
        <div class="card autolib-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-trophy me-2"></i>Top Performing Metrics</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Metric</th>
                                <th>Value</th>
                                <th>Trend</th>
                            </tr>
                        </thead>
                        <tbody id="topMetricsTable">
                            <tr>
                                <td>Most Issued Book</td>
                                <td>Programming Fundamentals</td>
                                <td><span class="badge bg-success">↑ 15%</span></td>
                            </tr>
                            <tr>
                                <td>Most Active Department</td>
                                <td>Computer Science</td>
                                <td><span class="badge bg-success">↑ 8%</span></td>
                            </tr>
                            <tr>
                                <td>Peak Usage Hour</td>
                                <td>10:00 AM - 11:00 AM</td>
                                <td><span class="badge bg-warning">→ 0%</span></td>
                            </tr>
                            <tr>
                                <td>Average Issue Duration</td>
                                <td>12.5 days</td>
                                <td><span class="badge bg-danger">↓ 2%</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card autolib-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Areas for Improvement</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Issue</th>
                                <th>Impact</th>
                                <th>Priority</th>
                            </tr>
                        </thead>
                        <tbody id="improvementTable">
                            <tr>
                                <td>Overdue Books</td>
                                <td>High</td>
                                <td><span class="badge bg-danger">Critical</span></td>
                            </tr>
                            <tr>
                                <td>Low Utilization - CIVIL Dept</td>
                                <td>Medium</td>
                                <td><span class="badge bg-warning">Medium</span></td>
                            </tr>
                            <tr>
                                <td>Missing Books</td>
                                <td>Medium</td>
                                <td><span class="badge bg-warning">Medium</span></td>
                            </tr>
                            <tr>
                                <td>Binding Backlog</td>
                                <td>Low</td>
                                <td><span class="badge bg-info">Low</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // Set default dates (current month)
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    
    $('#dateTo').val(today.toISOString().split('T')[0]);
    $('#dateFrom').val(firstDay.toISOString().split('T')[0]);
    
    // Load initial statistics
    generateStatistics();
});

function updatePeriod() {
    const periodType = $('#periodType').val();
    const today = new Date();
    let fromDate, toDate;
    
    switch(periodType) {
        case 'daily':
            fromDate = toDate = today;
            break;
        case 'weekly':
            fromDate = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            toDate = today;
            break;
        case 'monthly':
            fromDate = new Date(today.getFullYear(), today.getMonth(), 1);
            toDate = today;
            break;
        case 'yearly':
            fromDate = new Date(today.getFullYear(), 0, 1);
            toDate = today;
            break;
        default:
            return; // Custom range - don't change dates
    }
    
    $('#dateFrom').val(fromDate.toISOString().split('T')[0]);
    $('#dateTo').val(toDate.toISOString().split('T')[0]);
}

function generateStatistics() {
    // Update KPIs
    $('#totalBooks').text('1,250');
    $('#booksGrowth').text('+12%');
    $('#totalCirculation').text('3,456');
    $('#circulationGrowth').text('+18%');
    $('#activeMembers').text('892');
    $('#membersGrowth').text('+5%');
    $('#utilizationRate').text('76%');
    $('#utilizationGrowth').text('+3%');
    
    // Generate charts
    generateCirculationChart();
    generateDepartmentChart();
    generatePopularBooksChart();
    generatePeakHoursChart();
}

function generateCirculationChart() {
    const ctx = document.getElementById('circulationChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'Issues',
                data: [120, 150, 180, 200, 170, 220],
                borderColor: 'rgb(75, 192, 192)',
                tension: 0.1
            }, {
                label: 'Returns',
                data: [110, 140, 175, 195, 165, 210],
                borderColor: 'rgb(255, 99, 132)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}

function generateDepartmentChart() {
    const ctx = document.getElementById('departmentChart').getContext('2d');
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['CSE', 'IT', 'ECE', 'EEE', 'MECH', 'CIVIL'],
            datasets: [{
                data: [30, 25, 20, 15, 7, 3],
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0',
                    '#9966FF',
                    '#FF9F40'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}

function generatePopularBooksChart() {
    const ctx = document.getElementById('popularBooksChart').getContext('2d');
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['Programming', 'Mathematics', 'Physics', 'Chemistry', 'English'],
            datasets: [{
                label: 'Issues',
                data: [45, 38, 32, 28, 25],
                backgroundColor: 'rgba(54, 162, 235, 0.8)'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}

function generatePeakHoursChart() {
    const ctx = document.getElementById('peakHoursChart').getContext('2d');
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['9 AM', '10 AM', '11 AM', '12 PM', '1 PM', '2 PM', '3 PM', '4 PM'],
            datasets: [{
                label: 'Activity',
                data: [15, 35, 45, 30, 25, 40, 35, 20],
                backgroundColor: 'rgba(255, 206, 86, 0.8)'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}

function resetFilters() {
    $('#periodType').val('monthly');
    updatePeriod();
    $('#department').val('');
    generateStatistics();
}

function exportReport(format) {
    alert(`Exporting Statistics Report as ${format.toUpperCase()}...`);
}

// Load real statistics on page load
$(document).ready(function() {
    loadStatisticsData();
});

function loadStatisticsData() {
    $.ajax({
        url: '/api/admin/statistics-data',
        method: 'GET',
        success: function(response) {
            // Update summary statistics
            $('#totalBooks').text(response.summary.total_books.toLocaleString());
            $('#activeMembers').text(response.summary.active_members.toLocaleString());
            $('#currentIssues').text(response.summary.current_issues.toLocaleString());
            $('#utilizationRate').text(response.summary.utilization_rate + '%');

            // Update charts with real data
            updateCirculationChart(response.circulation_trends);
            updateDepartmentChart(response.department_stats);
        },
        error: function() {
            // Set error values
            $('#totalBooks').text('Error');
            $('#activeMembers').text('Error');
            $('#currentIssues').text('Error');
            $('#utilizationRate').text('Error');
        }
    });
}

function updateCirculationChart(data) {
    const ctx = document.getElementById('circulationChart').getContext('2d');

    // Destroy existing chart if it exists
    if (window.circulationChartInstance) {
        window.circulationChartInstance.destroy();
    }

    window.circulationChartInstance = new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.map(item => item.month),
            datasets: [{
                label: 'Issues',
                data: data.map(item => item.issues),
                borderColor: '#0d6efd',
                backgroundColor: 'rgba(13, 110, 253, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function updateDepartmentChart(data) {
    const ctx = document.getElementById('departmentChart').getContext('2d');

    // Destroy existing chart if it exists
    if (window.departmentChartInstance) {
        window.departmentChartInstance.destroy();
    }

    window.departmentChartInstance = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: data.map(item => item.department),
            datasets: [{
                data: data.map(item => item.issues),
                backgroundColor: [
                    '#0d6efd', '#198754', '#ffc107', '#dc3545',
                    '#6f42c1', '#fd7e14', '#20c997', '#6c757d'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}
</script>
{% endblock %}
