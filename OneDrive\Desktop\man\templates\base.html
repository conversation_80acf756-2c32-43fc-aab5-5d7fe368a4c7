<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Library Management System{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: 700;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            border: none;
        }
        .btn-custom {
            border-radius: 8px;
            padding: 8px 16px;
        }
        .sidebar {
            min-height: calc(100vh - 56px);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin-bottom: 5px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.2);
            color: white;
        }
        .content-area {
            padding: 20px;
        }
        .stat-card {
            border-left: 4px solid;
            background: white;
        }
        .stat-card-primary { border-left-color: #0d6efd; }
        .stat-card-success { border-left-color: #198754; }
        .stat-card-warning { border-left-color: #ffc107; }
        .stat-card-danger { border-left-color: #dc3545; }
        .stat-card-info { border-left-color: #0dcaf0; }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-book-open me-2"></i>
                Library Management
            </a>

            <!-- Global Search -->
            {% if session.user_role %}
            <div class="d-flex flex-grow-1 mx-4">
                <div class="position-relative w-100" style="max-width: 500px;">
                    <input type="text" class="form-control" id="globalSearch"
                           placeholder="Search books, e-books, students..." autocomplete="off">
                    <div id="searchResults" class="position-absolute w-100 bg-white border rounded shadow-sm mt-1"
                         style="z-index: 1050; max-height: 400px; overflow-y: auto; display: none;"></div>
                </div>
            </div>
            {% endif %}

            <div class="navbar-nav ms-auto">
                {% if session.user_name %}
                    <span class="navbar-text me-3">
                        <i class="fas fa-user me-2"></i>{{ session.user_name }}
                    </span>
                    <a class="nav-link" href="{{ url_for('logout') }}">
                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                    </a>
                {% endif %}
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            {% if session.user_role %}
            <div class="col-md-3 col-lg-2 px-0">
                <div class="sidebar">
                    <nav class="nav flex-column p-3">
                        {% block sidebar %}{% endblock %}
                    </nav>
                </div>
            </div>
            {% endif %}

            <!-- Main Content -->
            <div class="{% if session.user_role %}col-md-9 col-lg-10{% else %}col-12{% endif %}">
                <div class="content-area">
                    {% with messages = get_flashed_messages() %}
                        {% if messages %}
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                {{ messages[0] }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endif %}
                    {% endwith %}

                    {% block content %}{% endblock %}
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Global Search JavaScript -->
    <script>
    $(document).ready(function() {
        let searchTimeout;

        // Global search functionality
        $('#globalSearch').on('input', function() {
            const query = $(this).val().trim();

            clearTimeout(searchTimeout);

            if (query.length >= 2) {
                searchTimeout = setTimeout(function() {
                    performGlobalSearch(query);
                }, 300);
            } else {
                $('#searchResults').hide();
            }
        });

        function performGlobalSearch(query) {
            $.get('/api/global_search', { q: query }, function(data) {
                const resultsContainer = $('#searchResults');
                resultsContainer.empty();

                if (data.length > 0) {
                    data.forEach(function(item) {
                        const resultItem = $(`
                            <div class="search-result-item p-3 border-bottom" style="cursor: pointer;">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <i class="${item.icon} text-primary"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="fw-bold text-dark">${item.title}</div>
                                        <div class="text-muted small">${item.subtitle}</div>
                                        <div class="text-muted small">
                                            <span class="badge bg-secondary">${item.category}</span>
                                            <span class="ms-2">Access: ${item.access_no}</span>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        ${item.available ?
                                            '<span class="badge bg-success">Available</span>' :
                                            '<span class="badge bg-danger">Not Available</span>'
                                        }
                                    </div>
                                </div>
                            </div>
                        `);

                        resultItem.hover(function() {
                            $(this).addClass('bg-light');
                        }, function() {
                            $(this).removeClass('bg-light');
                        });

                        resultItem.click(function() {
                            $('#globalSearch').val(item.title);
                            $('#searchResults').hide();
                            // You can add navigation logic here if needed
                        });

                        resultsContainer.append(resultItem);
                    });

                    resultsContainer.show();
                } else {
                    resultsContainer.html('<div class="p-3 text-muted text-center">No results found</div>').show();
                }
            }).fail(function() {
                $('#searchResults').html('<div class="p-3 text-danger text-center">Search error occurred</div>').show();
            });
        }

        // Hide search results when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('#globalSearch, #searchResults').length) {
                $('#searchResults').hide();
            }
        });

        // Clear search on escape
        $('#globalSearch').on('keydown', function(e) {
            if (e.key === 'Escape') {
                $(this).val('');
                $('#searchResults').hide();
            }
        });
    });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
