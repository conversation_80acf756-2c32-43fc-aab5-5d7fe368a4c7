<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Library Management System{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: 700;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            border: none;
        }
        .btn-custom {
            border-radius: 8px;
            padding: 8px 16px;
        }
        .sidebar {
            min-height: calc(100vh - 56px);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin-bottom: 5px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.2);
            color: white;
        }
        .content-area {
            padding: 20px;
        }
        .stat-card {
            border-left: 4px solid;
            background: white;
        }
        .stat-card-primary { border-left-color: #0d6efd; }
        .stat-card-success { border-left-color: #198754; }
        .stat-card-warning { border-left-color: #ffc107; }
        .stat-card-danger { border-left-color: #dc3545; }
        .stat-card-info { border-left-color: #0dcaf0; }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-book-open me-2"></i>
                Library Management
            </a>
            
            <div class="navbar-nav ms-auto">
                {% if session.user_name %}
                    <span class="navbar-text me-3">
                        <i class="fas fa-user me-2"></i>{{ session.user_name }}
                    </span>
                    <a class="nav-link" href="{{ url_for('logout') }}">
                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                    </a>
                {% endif %}
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            {% if session.user_role %}
            <div class="col-md-3 col-lg-2 px-0">
                <div class="sidebar">
                    <nav class="nav flex-column p-3">
                        {% block sidebar %}{% endblock %}
                    </nav>
                </div>
            </div>
            {% endif %}

            <!-- Main Content -->
            <div class="{% if session.user_role %}col-md-9 col-lg-10{% else %}col-12{% endif %}">
                <div class="content-area">
                    {% with messages = get_flashed_messages() %}
                        {% if messages %}
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                {{ messages[0] }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endif %}
                    {% endwith %}

                    {% block content %}{% endblock %}
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
