{% extends "base.html" %}

{% block title %}Bulk Creation Results - Admin{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>
<a class="nav-link" href="{{ url_for('admin_books') }}">
    <i class="fas fa-book me-2"></i>Manage Books
</a>
<a class="nav-link" href="{{ url_for('admin_librarians') }}">
    <i class="fas fa-user-tie me-2"></i>Manage Librarians
</a>
<a class="nav-link" href="{{ url_for('admin_students') }}">
    <i class="fas fa-graduation-cap me-2"></i>Manage Students
</a>
<a class="nav-link active" href="{{ url_for('admin_bulk_users') }}">
    <i class="fas fa-upload me-2"></i>Bulk Create Users
</a>
<a class="nav-link" href="{{ url_for('admin_issue_history') }}">
    <i class="fas fa-history me-2"></i>Issue History
</a>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-check-circle me-3"></i>Bulk Creation Results</h2>
    <div>
        {% if results.created_users %}
        <a href="{{ url_for('admin_download_credentials') }}" class="btn btn-success btn-custom me-2">
            <i class="fas fa-download me-2"></i>Download Credentials
        </a>
        {% endif %}
        <a href="{{ url_for('admin_bulk_users') }}" class="btn btn-primary btn-custom me-2">
            <i class="fas fa-upload me-2"></i>Create More Users
        </a>
        <a href="{{ url_for('admin_dashboard') }}" class="btn btn-secondary btn-custom">
            <i class="fas fa-home me-2"></i>Dashboard
        </a>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card stat-card stat-card-success">
            <div class="card-body text-center">
                <i class="fas fa-user-check fa-2x text-success mb-2"></i>
                <h3 class="text-success">{{ results.created_users|length }}</h3>
                <p class="text-muted mb-0">{{ results.user_type.title() }}(s) Created</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card stat-card stat-card-danger">
            <div class="card-body text-center">
                <i class="fas fa-exclamation-triangle fa-2x text-danger mb-2"></i>
                <h3 class="text-danger">{{ results.errors|length }}</h3>
                <p class="text-muted mb-0">Errors/Skipped</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card stat-card stat-card-info">
            <div class="card-body text-center">
                <i class="fas fa-file-excel fa-2x text-info mb-2"></i>
                <h3 class="text-info">{{ results.created_users|length + results.errors|length }}</h3>
                <p class="text-muted mb-0">Total Rows Processed</p>
            </div>
        </div>
    </div>
</div>

<!-- Successfully Created Users -->
{% if results.created_users %}
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="fas fa-users me-2 text-success"></i>Successfully Created {{ results.user_type.title() }}s</h5>
        <button class="btn btn-sm btn-outline-primary" onclick="downloadCredentials()">
            <i class="fas fa-download me-2"></i>Download Credentials
        </button>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="credentialsTable">
                <thead class="table-success">
                    <tr>
                        <th>Name</th>
                        <th>{{ 'User ID' if results.user_type == 'student' else 'ID' }}</th>
                        <th>Email</th>
                        <th>Generated Password</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in results.created_users %}
                    <tr>
                        <td><strong>{{ user.name }}</strong></td>
                        <td>{{ user.user_id }}</td>
                        <td>{{ user.email }}</td>
                        <td>
                            <div class="input-group">
                                <input type="password" class="form-control password-field" 
                                       value="{{ user.password }}" readonly>
                                <button class="btn btn-outline-secondary toggle-password" type="button">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary copy-credentials" 
                                    data-email="{{ user.email }}" 
                                    data-password="{{ user.password }}"
                                    title="Copy credentials to clipboard">
                                <i class="fas fa-copy"></i>
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <div class="alert alert-warning mt-3">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Important:</strong> Please save these credentials securely. Users will need this information to log in to the system.
            You can download the credentials as an Excel file for distribution.
        </div>
    </div>
</div>
{% endif %}

<!-- Errors Section -->
{% if results.errors %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-exclamation-circle me-2 text-danger"></i>Errors and Skipped Entries</h5>
    </div>
    <div class="card-body">
        <div class="alert alert-danger">
            <i class="fas fa-info-circle me-2"></i>
            The following entries could not be processed:
        </div>
        <ul class="list-group list-group-flush">
            {% for error in results.errors %}
            <li class="list-group-item">
                <i class="fas fa-times-circle text-danger me-2"></i>{{ error }}
            </li>
            {% endfor %}
        </ul>
    </div>
</div>
{% endif %}

<!-- No Results -->
{% if not results.created_users and not results.errors %}
<div class="card">
    <div class="card-body text-center py-5">
        <i class="fas fa-question-circle fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">No results to display</h5>
        <p class="text-muted">It seems no data was processed. Please try again.</p>
        <a href="{{ url_for('admin_bulk_users') }}" class="btn btn-primary">
            <i class="fas fa-upload me-2"></i>Try Again
        </a>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script>
$(document).ready(function() {
    // Toggle password visibility
    $('.toggle-password').on('click', function() {
        const passwordField = $(this).siblings('.password-field');
        const icon = $(this).find('i');
        
        if (passwordField.attr('type') === 'password') {
            passwordField.attr('type', 'text');
            icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            passwordField.attr('type', 'password');
            icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });
    
    // Copy credentials to clipboard
    $('.copy-credentials').on('click', function() {
        const email = $(this).data('email');
        const password = $(this).data('password');
        const credentials = `Email: ${email}\nPassword: ${password}`;
        
        navigator.clipboard.writeText(credentials).then(function() {
            // Show success feedback
            const button = event.target.closest('button');
            const originalHtml = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check text-success"></i>';
            
            setTimeout(function() {
                button.innerHTML = originalHtml;
            }, 2000);
        });
    });
});

function downloadCredentials() {
    // Prepare data for Excel download
    const data = [];
    
    // Add headers
    data.push(['Name', '{{ "Roll Number" if results.user_type == "student" else "ID" }}', 'Email', 'Password']);
    
    // Add user data
    {% for user in results.created_users %}
    data.push(['{{ user.name }}', '{{ user.user_id }}', '{{ user.email }}', '{{ user.password }}']);
    {% endfor %}
    
    // Create workbook and worksheet
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet(data);
    
    // Set column widths
    ws['!cols'] = [
        { width: 20 }, // Name
        { width: 15 }, // Roll Number/ID
        { width: 30 }, // Email
        { width: 20 }  // Password
    ];
    
    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(wb, ws, '{{ results.user_type.title() }} Credentials');
    
    // Download file
    const filename = `{{ results.user_type }}_credentials_${new Date().toISOString().split('T')[0]}.xlsx`;
    XLSX.writeFile(wb, filename);
}
</script>
{% endblock %}
