{% extends "base.html" %}

{% block title %}Department Management - Admin{% endblock %}

{% block sidebar %}
<!-- Dashboard -->
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>

<!-- Management Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-info" data-bs-toggle="collapse" href="#managementSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-cogs me-2"></i>Management
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="managementSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_books') }}">
                <i class="fas fa-book me-2"></i>Manage Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_ebooks') }}">
                <i class="fas fa-tablet-alt me-2"></i>Manage E-Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_students') }}">
                <i class="fas fa-graduation-cap me-2"></i>Manage Students
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_librarians') }}">
                <i class="fas fa-user-tie me-2"></i>Manage Librarians
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_colleges') }}">
                <i class="fas fa-university me-2"></i>Manage Colleges
            </a>
            <a class="nav-link py-1 active" href="{{ url_for('admin_departments') }}">
                <i class="fas fa-building me-2"></i>Manage Departments
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_news_clippings') }}">
                <i class="fas fa-newspaper me-2"></i>News Clippings
            </a>
        </div>
    </div>
</div>

<!-- Circulation Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-primary" data-bs-toggle="collapse" href="#circulationSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-exchange-alt me-2"></i>Circulation
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="circulationSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_circulation_counter') }}">
                <i class="fas fa-desktop me-2"></i>Counter Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_issue_history') }}">
                <i class="fas fa-history me-2"></i>Issue History
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bulk_operations') }}">
                <i class="fas fa-layer-group me-2"></i>Bulk Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_payment_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Payment Management
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_management') }}">
                <i class="fas fa-tools me-2"></i>Binding & Maintenance
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_fine_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Fine Management
            </a>
        </div>
    </div>
</div>

<!-- Reports Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-success" data-bs-toggle="collapse" href="#reportsSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-chart-bar me-2"></i>Reports
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="reportsSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_library_connection_report') }}">
                <i class="fas fa-wifi me-2"></i>Library Connection
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_access_register_report') }}">
                <i class="fas fa-sign-in-alt me-2"></i>Access Register
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bibliography_report') }}">
                <i class="fas fa-book-open me-2"></i>Bibliography
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_counter_report') }}">
                <i class="fas fa-calculator me-2"></i>Counter Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_statistics_report') }}">
                <i class="fas fa-chart-line me-2"></i>Statistics
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_report') }}">
                <i class="fas fa-tools me-2"></i>Binding Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_database_report') }}">
                <i class="fas fa-database me-2"></i>Database Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_member_report') }}">
                <i class="fas fa-users me-2"></i>Member Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_resource_report') }}">
                <i class="fas fa-archive me-2"></i>Resource Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_no_dues_report') }}">
                <i class="fas fa-check-circle me-2"></i>No Dues Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_qb_report') }}">
                <i class="fas fa-money-check-alt me-2"></i>QB Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_transfer_report') }}">
                <i class="fas fa-exchange-alt me-2"></i>Transfer Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_missing_report') }}">
                <i class="fas fa-exclamation-triangle me-2"></i>Missing Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_news_clipping_report') }}">
                <i class="fas fa-newspaper me-2"></i>News Clipping Report
            </a>
        </div>
    </div>
</div>

<!-- System Administration -->
<div class="nav-item">
    <a class="nav-link fw-bold text-warning" data-bs-toggle="collapse" href="#systemSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-server me-2"></i>System
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="systemSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_settings') }}">
                <i class="fas fa-cog me-2"></i>System Settings
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-building me-3 text-info"></i>Department Management</h2>
        <p class="text-muted mb-0">Manage departments under colleges</p>
    </div>
    <div>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDepartmentModal">
            <i class="fas fa-plus me-2"></i>Add Department
        </button>
        <a href="{{ url_for('admin_colleges') }}" class="btn btn-outline-info ms-2">
            <i class="fas fa-university me-2"></i>Manage Colleges
        </a>
        <button class="btn btn-success ms-2" onclick="exportDepartments()">
            <i class="fas fa-file-excel me-2"></i>Export
        </button>
    </div>
</div>

<!-- Filter Section -->
<div class="card autolib-card mb-4">
    <div class="card-header bg-gradient-info text-white">
        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filter Departments</h5>
    </div>
    <div class="card-body">
        <form id="filterForm" class="row g-3">
            <div class="col-md-6">
                <label for="collegeFilter" class="form-label">Filter by College</label>
                <select class="form-select" id="collegeFilter" name="collegeFilter" onchange="filterDepartments()">
                    <option value="">All Colleges</option>
                    {% for college in colleges %}
                        <option value="{{ college.id }}">{{ college.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-6">
                <label for="statusFilter" class="form-label">Filter by Status</label>
                <select class="form-select" id="statusFilter" name="statusFilter" onchange="filterDepartments()">
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
            </div>
        </form>
    </div>
</div>

<!-- Department Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-primary">
                    <i class="fas fa-building fa-2x mb-2"></i>
                </div>
                <h4 class="text-primary" id="totalDepartments">{{ departments|length }}</h4>
                <p class="text-muted mb-0 small">Total Departments</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-success">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                </div>
                <h4 class="text-success" id="activeDepartments">{{ departments|selectattr('is_active')|list|length }}</h4>
                <p class="text-muted mb-0 small">Active Departments</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-warning">
                    <i class="fas fa-university fa-2x mb-2"></i>
                </div>
                <h4 class="text-warning" id="totalColleges">{{ colleges|length }}</h4>
                <p class="text-muted mb-0 small">Total Colleges</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-info">
                    <i class="fas fa-users fa-2x mb-2"></i>
                </div>
                <h4 class="text-info" id="totalStudents">0</h4>
                <p class="text-muted mb-0 small">Total Students</p>
            </div>
        </div>
    </div>
</div>

<!-- Departments Table -->
<div class="card autolib-card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-table me-2"></i>Departments List</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="departmentsTable">
                <thead class="table-dark">
                    <tr>
                        <th>ID</th>
                        <th>Department Name</th>
                        <th>Code</th>
                        <th>College</th>
                        <th>Head of Department</th>
                        <th>Phone</th>
                        <th>Email</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% if departments %}
                        {% for department in departments %}
                        <tr data-college-id="{{ department.college_id }}" data-status="{{ 'active' if department.is_active else 'inactive' }}">
                            <td>{{ department.id }}</td>
                            <td>{{ department.name }}</td>
                            <td><span class="badge bg-primary">{{ department.code }}</span></td>
                            <td>{{ department.college.name }}</td>
                            <td>{{ department.head_of_department or 'N/A' }}</td>
                            <td>{{ department.phone or 'N/A' }}</td>
                            <td>{{ department.email or 'N/A' }}</td>
                            <td>
                                {% if department.is_active %}
                                    <span class="badge bg-success">Active</span>
                                {% else %}
                                    <span class="badge bg-danger">Inactive</span>
                                {% endif %}
                            </td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="editDepartment({{ department.id }})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-info" onclick="viewStudents({{ department.id }})">
                                    <i class="fas fa-users"></i>
                                </button>
                                {% if department.is_active %}
                                    <button class="btn btn-sm btn-outline-warning" onclick="toggleDepartmentStatus({{ department.id }}, false)">
                                        <i class="fas fa-pause"></i>
                                    </button>
                                {% else %}
                                    <button class="btn btn-sm btn-outline-success" onclick="toggleDepartmentStatus({{ department.id }}, true)">
                                        <i class="fas fa-play"></i>
                                    </button>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="9" class="text-center text-muted">
                                <i class="fas fa-info-circle me-2"></i>No departments found. Add your first department to get started!
                            </td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Department Modal -->
<div class="modal fade" id="addDepartmentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title"><i class="fas fa-plus me-2"></i>Add New Department</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addDepartmentForm">
                    <div class="row">
                        <div class="col-md-6">
                            <label for="departmentCollege" class="form-label">College *</label>
                            <select class="form-select" id="departmentCollege" name="departmentCollege" required>
                                <option value="">Select College</option>
                                {% for college in colleges %}
                                    <option value="{{ college.id }}">{{ college.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="departmentCode" class="form-label">Department Code *</label>
                            <input type="text" class="form-control" id="departmentCode" name="departmentCode" required>
                            <div class="form-text">Short code (e.g., CSE, IT, ECE)</div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <label for="departmentName" class="form-label">Department Name *</label>
                            <input type="text" class="form-control" id="departmentName" name="departmentName" required>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <label for="departmentHead" class="form-label">Head of Department</label>
                            <input type="text" class="form-control" id="departmentHead" name="departmentHead">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label for="departmentPhone" class="form-label">Phone</label>
                            <input type="tel" class="form-control" id="departmentPhone" name="departmentPhone">
                        </div>
                        <div class="col-md-6">
                            <label for="departmentEmail" class="form-label">Email</label>
                            <input type="email" class="form-control" id="departmentEmail" name="departmentEmail">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="addDepartment()">
                    <i class="fas fa-plus me-2"></i>Add Department
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    loadStatistics();
});

function loadStatistics() {
    // Update student counts
    // This would be replaced with actual API calls
    $('#totalStudents').text('0');
}

function filterDepartments() {
    const collegeFilter = $('#collegeFilter').val();
    const statusFilter = $('#statusFilter').val();
    
    $('#departmentsTable tbody tr').each(function() {
        const row = $(this);
        const collegeId = row.data('college-id');
        const status = row.data('status');
        
        let showRow = true;
        
        if (collegeFilter && collegeId != collegeFilter) {
            showRow = false;
        }
        
        if (statusFilter && status !== statusFilter) {
            showRow = false;
        }
        
        if (showRow) {
            row.show();
        } else {
            row.hide();
        }
    });
}

function addDepartment() {
    const formData = {
        college_id: $('#departmentCollege').val(),
        code: $('#departmentCode').val(),
        name: $('#departmentName').val(),
        head_of_department: $('#departmentHead').val(),
        phone: $('#departmentPhone').val(),
        email: $('#departmentEmail').val()
    };
    
    // Validate required fields
    if (!formData.college_id || !formData.code || !formData.name) {
        alert('Please fill all required fields.');
        return;
    }
    
    // Send API request
    $.ajax({
        url: '/api/departments',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            alert('Department added successfully!');
            $('#addDepartmentModal').modal('hide');
            $('#addDepartmentForm')[0].reset();
            location.reload();
        },
        error: function(xhr) {
            const error = xhr.responseJSON?.error || 'Error adding department';
            alert(error);
        }
    });
}

function editDepartment(departmentId) {
    alert('Edit department functionality would be implemented here.');
}

function viewStudents(departmentId) {
    window.location.href = `/admin/students?department_id=${departmentId}`;
}

function toggleDepartmentStatus(departmentId, status) {
    const action = status ? 'activate' : 'deactivate';
    if (confirm(`Are you sure you want to ${action} this department?`)) {
        alert(`Department ${action}d successfully!`);
        location.reload();
    }
}

function exportDepartments() {
    alert('Exporting departments data...');
}
</script>
{% endblock %}
