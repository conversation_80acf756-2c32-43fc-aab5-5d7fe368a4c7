{% extends "base.html" %}

{% block title %}Bulk Create Users - Admin{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>
<a class="nav-link" href="{{ url_for('admin_books') }}">
    <i class="fas fa-book me-2"></i>Manage Books
</a>
<a class="nav-link" href="{{ url_for('admin_librarians') }}">
    <i class="fas fa-user-tie me-2"></i>Manage Librarians
</a>
<a class="nav-link" href="{{ url_for('admin_students') }}">
    <i class="fas fa-graduation-cap me-2"></i>Manage Students
</a>
<a class="nav-link active" href="{{ url_for('admin_bulk_users') }}">
    <i class="fas fa-upload me-2"></i>Bulk Create Users
</a>
<a class="nav-link" href="{{ url_for('admin_issue_history') }}">
    <i class="fas fa-history me-2"></i>Issue History
</a>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-upload me-3"></i>Bulk Create Users</h2>
    <a href="{{ url_for('admin_dashboard') }}" class="btn btn-secondary btn-custom">
        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
    </a>
</div>

<!-- Instructions Card -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>How to Use Bulk User Creation</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6>📋 Step-by-Step Instructions:</h6>
                <ol>
                    <li>Download the Excel template for your user type</li>
                    <li>Fill in the required information</li>
                    <li>Upload the completed Excel file</li>
                    <li>Review the results and generated credentials</li>
                </ol>
            </div>
            <div class="col-md-6">
                <h6>🔑 Password Generation:</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-key me-2 text-primary"></i><strong>Format:</strong> user_id + name</li>
                    <li><i class="fas fa-user me-2 text-success"></i><strong>Example:</strong> For "John Doe" with user ID "STU001"</li>
                    <li><i class="fas fa-arrow-right me-2 text-info"></i><strong>Password:</strong> STU001johndoe</li>
                    <li><i class="fas fa-envelope me-2 text-warning"></i><strong>Email:</strong> Auto-generated if not provided</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Template Download Section -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-download me-2"></i>Download Templates</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">Download the Excel template for the type of users you want to create:</p>
                <div class="d-grid gap-2">
                    <a href="{{ url_for('admin_download_template', user_type='librarian') }}" 
                       class="btn btn-outline-primary btn-custom">
                        <i class="fas fa-user-tie me-2"></i>Download Librarian Template
                    </a>
                    <a href="{{ url_for('admin_download_template', user_type='student') }}" 
                       class="btn btn-outline-success btn-custom">
                        <i class="fas fa-graduation-cap me-2"></i>Download Student Template
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-upload me-2"></i>Upload Excel File</h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" id="bulkUploadForm">
                    <div class="mb-3">
                        <label for="user_type" class="form-label">
                            <i class="fas fa-users me-2"></i>User Type
                        </label>
                        <select class="form-select" id="user_type" name="user_type" required>
                            <option value="">Select User Type</option>
                            <option value="librarian">Librarian</option>
                            <option value="student">Student</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="file" class="form-label">
                            <i class="fas fa-file-excel me-2"></i>Excel File
                        </label>
                        <input type="file" class="form-control" id="file" name="file" 
                               accept=".xlsx,.xls" required>
                        <div class="form-text">Accepted formats: .xlsx, .xls (Max size: 16MB)</div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-custom">
                            <i class="fas fa-upload me-2"></i>Upload and Create Users
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Required Columns Information -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-table me-2"></i>Required Excel Columns</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6><i class="fas fa-user-tie me-2 text-primary"></i>For Librarians:</h6>
                <div class="table-responsive">
                    <table class="table table-sm table-bordered">
                        <thead class="table-dark">
                            <tr>
                                <th>Column Name</th>
                                <th>Required</th>
                                <th>Description</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>name</code></td>
                                <td><span class="badge bg-danger">Yes</span></td>
                                <td>Full name of the librarian</td>
                            </tr>
                            <tr>
                                <td><code>user_id</code></td>
                                <td><span class="badge bg-danger">Yes</span></td>
                                <td>Unique librarian ID (Staff ID)</td>
                            </tr>
                            <tr>
                                <td><code>email</code></td>
                                <td><span class="badge bg-warning">Optional</span></td>
                                <td>Email address (auto-generated if empty)</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <div class="col-md-6">
                <h6><i class="fas fa-graduation-cap me-2 text-success"></i>For Students:</h6>
                <div class="table-responsive">
                    <table class="table table-sm table-bordered">
                        <thead class="table-dark">
                            <tr>
                                <th>Column Name</th>
                                <th>Required</th>
                                <th>Description</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>name</code></td>
                                <td><span class="badge bg-danger">Yes</span></td>
                                <td>Full name of the student</td>
                            </tr>
                            <tr>
                                <td><code>user_id</code></td>
                                <td><span class="badge bg-danger">Yes</span></td>
                                <td>Unique student roll number</td>
                            </tr>
                            <tr>
                                <td><code>email</code></td>
                                <td><span class="badge bg-warning">Optional</span></td>
                                <td>Email address (auto-generated if empty)</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="alert alert-info mt-3">
            <i class="fas fa-lightbulb me-2"></i>
            <strong>Tips:</strong>
            <ul class="mb-0 mt-2">
                <li>If email is not provided, it will be auto-generated as: <code>cleanname + <EMAIL></code></li>
                <li>Passwords are automatically generated as: <code>userid + cleanname</code></li>
                <li>Duplicate emails or user IDs will be skipped with error messages</li>
                <li>The system will validate all data before creating users</li>
            </ul>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // File upload validation
    $('#file').on('change', function() {
        const file = this.files[0];
        if (file) {
            const fileSize = file.size / 1024 / 1024; // Convert to MB
            const fileName = file.name;
            const fileExtension = fileName.split('.').pop().toLowerCase();
            
            if (!['xlsx', 'xls'].includes(fileExtension)) {
                alert('Please select a valid Excel file (.xlsx or .xls)');
                this.value = '';
                return;
            }
            
            if (fileSize > 16) {
                alert('File size must be less than 16MB');
                this.value = '';
                return;
            }
        }
    });
    
    // Form submission handling
    $('#bulkUploadForm').on('submit', function() {
        const submitBtn = $(this).find('button[type="submit"]');
        submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>Processing...');
        submitBtn.prop('disabled', true);
    });
    
    // User type change handler
    $('#user_type').on('change', function() {
        const userType = $(this).val();
        const fileInput = $('#file');
        
        if (userType && fileInput.val()) {
            // Optional: You could add specific validation based on user type
        }
    });
});
</script>
{% endblock %}
