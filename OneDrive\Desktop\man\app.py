# Corrected: removed invalid placeholder
# Place all route decorators and functions after app = Flask(__name__)
from flask import Flask, render_template, request, redirect, url_for, session, flash, jsonify, send_file
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import text
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
from datetime import datetime, timedelta, date
from io import BytesIO
import os
import pandas as pd
import re
import threading
import atexit

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///library.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Create uploads directory if it doesn't exist
if not os.path.exists(app.config['UPLOAD_FOLDER']):
    os.makedirs(app.config['UPLOAD_FOLDER'])

db = SQLAlchemy(app)

# Database Models
class Admin(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password = db.Column(db.String(200), nullable=False)

class Librarian(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password = db.Column(db.String(200), nullable=False)

class Student(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.String(50), unique=True, nullable=False)
    username = db.Column(db.String(50), unique=True, nullable=False)
    name = db.Column(db.String(100), nullable=False)
    roll_number = db.Column(db.String(50), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password = db.Column(db.String(200), nullable=False)
    department = db.Column(db.String(10), nullable=False)  # CSE, IT, ECE, etc.
    college = db.Column(db.String(100), nullable=False, default='Engineering College')
    designation = db.Column(db.String(20), nullable=False, default='Student')  # Student or Staff
    course = db.Column(db.String(100), nullable=False)
    dob = db.Column(db.Date, nullable=False)
    current_year = db.Column(db.Integer, nullable=False)
    validity_date = db.Column(db.Date, nullable=False)
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)

class Book(db.Model):
    book_id = db.Column(db.Integer, primary_key=True)
    access_no = db.Column(db.String(50), unique=True, nullable=False)  # Access Number
    title = db.Column(db.String(200), nullable=False)
    author = db.Column(db.String(100), nullable=False)
    publisher = db.Column(db.String(150), nullable=False)
    subject = db.Column(db.String(100), nullable=False)
    department = db.Column(db.String(10), nullable=False)  # CSE, IT, ECE, etc.
    category = db.Column(db.String(100), nullable=False)
    location = db.Column(db.String(100), nullable=False)  # Location of the book
    copies = db.Column(db.Integer, nullable=False, default=1)  # Total copies
    quantity = db.Column(db.Integer, nullable=False, default=1)  # For backward compatibility
    available_count = db.Column(db.Integer, nullable=False, default=1)
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)

class EBook(db.Model):
    ebook_id = db.Column(db.Integer, primary_key=True)
    access_no = db.Column(db.String(50), unique=True, nullable=False)  # Access Number
    title = db.Column(db.String(200), nullable=False)
    author = db.Column(db.String(100), nullable=False)
    publisher = db.Column(db.String(150), nullable=False)
    subject = db.Column(db.String(100), nullable=False)
    department = db.Column(db.String(10), nullable=False)  # CSE, IT, ECE, etc.
    category = db.Column(db.String(100), nullable=False)
    file_format = db.Column(db.String(10), nullable=False)  # PDF, EPUB, etc.
    file_size = db.Column(db.String(20), nullable=True)  # File size in MB
    download_url = db.Column(db.String(500), nullable=True)  # URL or file path
    isbn = db.Column(db.String(20), nullable=True)  # ISBN number
    pages = db.Column(db.Integer, nullable=True)  # Number of pages
    language = db.Column(db.String(50), nullable=False, default='English')
    description = db.Column(db.Text, nullable=True)  # Book description
    is_active = db.Column(db.Boolean, nullable=False, default=True)  # Active status
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)

class Issue(db.Model):
    issue_id = db.Column(db.Integer, primary_key=True)
    book_id = db.Column(db.Integer, db.ForeignKey('book.book_id'), nullable=False)
    student_id = db.Column(db.Integer, db.ForeignKey('student.id'), nullable=False)
    issue_date = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    due_date = db.Column(db.DateTime, nullable=False)
    return_date = db.Column(db.DateTime, nullable=True)
    fine = db.Column(db.Float, default=0.0)
    
    book = db.relationship('Book', backref=db.backref('issues', lazy=True))
    student = db.relationship('Student', backref=db.backref('issues', lazy=True))

class LibrarySettings(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    setting_name = db.Column(db.String(50), unique=True, nullable=False)
    setting_value = db.Column(db.String(200), nullable=False)
    description = db.Column(db.String(500))
    updated_by = db.Column(db.String(50))
    updated_at = db.Column(db.DateTime, default=datetime.utcnow)

# Helper Functions
def migrate_database():
    """Migrate database to add new columns to existing Student and Book tables"""
    with app.app_context():
        try:
            # Migrate Student table
            try:
                db.session.execute(text('SELECT user_id FROM student LIMIT 1'))
                print("✅ Student table already has new columns")
            except:
                print("🔄 Adding new columns to Student table...")
                
                # Add missing columns to existing Student table
                db.session.execute(text('ALTER TABLE student ADD COLUMN user_id VARCHAR(50)'))
                db.session.execute(text('ALTER TABLE student ADD COLUMN username VARCHAR(50)'))
                db.session.execute(text('ALTER TABLE student ADD COLUMN department VARCHAR(10)'))
                db.session.execute(text('ALTER TABLE student ADD COLUMN college VARCHAR(100) DEFAULT "Engineering College"'))
                db.session.execute(text('ALTER TABLE student ADD COLUMN designation VARCHAR(20) DEFAULT "Student"'))
                db.session.execute(text('ALTER TABLE student ADD COLUMN course VARCHAR(100)'))
                db.session.execute(text('ALTER TABLE student ADD COLUMN dob DATE'))
                db.session.execute(text('ALTER TABLE student ADD COLUMN current_year INTEGER'))
                db.session.execute(text('ALTER TABLE student ADD COLUMN validity_date DATE'))
                db.session.execute(text('ALTER TABLE student ADD COLUMN created_at DATETIME DEFAULT CURRENT_TIMESTAMP'))
                
                db.session.commit()
                
                # Update existing student records with default values
                from datetime import date, timedelta
                default_validity = date.today() + timedelta(days=365)  # 1 year from now
                
                students = Student.query.all()
                for student in students:
                    if not student.user_id:
                        student.user_id = f"STU{student.id:03d}"
                    if not student.username:
                        student.username = student.email.split('@')[0] if student.email else f"student{student.id}"
                    if not student.department:
                        student.department = 'CSE'
                    if not student.college:
                        student.college = 'Engineering College'
                    if not student.designation:
                        student.designation = 'Student'
                    if not student.course:
                        student.course = 'B.Tech Computer Science'
                    if not student.dob:
                        student.dob = date(2000, 1, 1)  # Default DOB
                    if not student.current_year:
                        student.current_year = 2
                    if not student.validity_date:
                        student.validity_date = default_validity
                
                db.session.commit()
                print("✅ Student table migration completed")

            # Migrate Book table
            try:
                db.session.execute(text('SELECT access_no FROM book LIMIT 1'))
                print("✅ Book table already has new columns")
            except:
                print("🔄 Adding new columns to Book table...")
                
                # Add missing columns to existing Book table
                db.session.execute(text('ALTER TABLE book ADD COLUMN access_no VARCHAR(50)'))
                db.session.execute(text('ALTER TABLE book ADD COLUMN publisher VARCHAR(150)'))
                db.session.execute(text('ALTER TABLE book ADD COLUMN subject VARCHAR(100)'))
                db.session.execute(text('ALTER TABLE book ADD COLUMN department VARCHAR(10)'))
                db.session.execute(text('ALTER TABLE book ADD COLUMN location VARCHAR(100)'))
                db.session.execute(text('ALTER TABLE book ADD COLUMN copies INTEGER DEFAULT 1'))
                db.session.execute(text('ALTER TABLE book ADD COLUMN created_at DATETIME DEFAULT CURRENT_TIMESTAMP'))
                
                db.session.commit()
                
                # Update existing book records with default values
                books = Book.query.all()
                for book in books:
                    if not book.access_no:
                        book.access_no = f"ACC{book.book_id:05d}"
                    if not book.publisher:
                        book.publisher = 'Unknown Publisher'
                    if not book.subject:
                        book.subject = book.category  # Use category as subject initially
                    if not book.department:
                        book.department = 'CSE'
                    if not book.location:
                        book.location = 'Section A, Shelf 1'
                    if not book.copies:
                        book.copies = book.quantity
                
                db.session.commit()
                print("✅ Book table migration completed")
                
            # Initialize Library Settings
            try:
                existing_settings = LibrarySettings.query.first()
                if not existing_settings:
                    print("🔄 Initializing library settings...")
                    
                    default_settings = [
                        {
                            'setting_name': 'student_book_limit',
                            'setting_value': '3',
                            'description': 'Maximum number of books a student can issue at once',
                            'updated_by': 'system'
                        },
                        {
                            'setting_name': 'staff_book_limit',
                            'setting_value': '5',
                            'description': 'Maximum number of books a staff member can issue at once',
                            'updated_by': 'system'
                        },
                        {
                            'setting_name': 'default_issue_days',
                            'setting_value': '14',
                            'description': 'Default number of days for book issue period',
                            'updated_by': 'system'
                        },
                        {
                            'setting_name': 'fine_per_day',
                            'setting_value': '2.0',
                            'description': 'Fine amount per day for overdue books (in rupees)',
                            'updated_by': 'system'
                        },
                        {
                            'setting_name': 'renewal_limit',
                            'setting_value': '2',
                            'description': 'Maximum number of times a book can be renewed',
                            'updated_by': 'system'
                        }
                    ]
                    
                    for setting_data in default_settings:
                        setting = LibrarySettings(**setting_data)
                        db.session.add(setting)
                    
                    db.session.commit()
                    print("✅ Library settings initialized")
                else:
                    print("✅ Library settings already exist")
                    
            except Exception as e:
                print(f"Settings initialization error: {str(e)}")
                db.session.rollback()
                
        except Exception as e:
            print(f"Migration error: {str(e)}")
            db.session.rollback()

def init_db():
    with app.app_context():
        db.create_all()
        
        # Run migration for existing database
        migrate_database()
        
        # Create default admin if not exists
        if not Admin.query.first():
            admin = Admin(
                name='Admin',
                email='<EMAIL>',
                password=generate_password_hash('admin123')
            )
            db.session.add(admin)
            db.session.commit()

def calculate_fine(due_date, return_date=None):
    if return_date is None:
        return_date = datetime.utcnow()

    if return_date > due_date:
        days_overdue = (return_date - due_date).days
        return days_overdue * 2.0  # $2 per day fine
    return 0.0

def get_next_access_number():
    """Get the next available access number"""
    # Get the last access number from the database
    last_book = Book.query.order_by(Book.access_no.desc()).first()

    if not last_book:
        return "1"

    # Extract numeric part from access number
    last_access = last_book.access_no

    # Try to extract number from various formats
    import re
    numbers = re.findall(r'\d+', last_access)

    if numbers:
        # Get the last (usually largest) number found
        last_number = int(numbers[-1])
        return str(last_number + 1)
    else:
        # If no numbers found, start from 1
        return "1"

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in {'xlsx', 'xls'}

def generate_username_password(name, user_id, user_type):
    """Generate username and password based on name and user_id"""
    # Clean name: remove spaces, special characters, convert to lowercase
    clean_name = re.sub(r'[^a-zA-Z]', '', name.lower())
    
    # Generate username: first part of name + user_id
    username = f"{clean_name[:6]}{user_id}@{user_type}.library.com"
    
    # Generate password: user_id + name (as specified)
    password = f"{user_id}{clean_name}"
    
    return username, password

def cleanup_expired_users():
    """Remove users whose validity date has expired"""
    with app.app_context():
        try:
            today = date.today()
            expired_students = Student.query.filter(Student.validity_date < today).all()
            
            for student in expired_students:
                # First, handle any pending book issues
                pending_issues = Issue.query.filter_by(student_id=student.id, return_date=None).all()
                for issue in pending_issues:
                    # Mark books as returned and calculate final fine
                    issue.return_date = datetime.utcnow()
                    issue.fine = calculate_fine(issue.due_date, issue.return_date)
                    # Return book to available stock
                    book = Book.query.get(issue.book_id)
                    if book:
                        book.available_count += 1
                
                # Delete the expired student
                db.session.delete(student)
                print(f"Deleted expired user: {student.name} (ID: {student.user_id})")
            
            if expired_students:
                db.session.commit()
                print(f"Cleanup completed: {len(expired_students)} expired users removed")
            
        except Exception as e:
            print(f"Error during cleanup: {str(e)}")
            db.session.rollback()

def schedule_cleanup():
    """Schedule daily cleanup of expired users"""
    cleanup_expired_users()
    # Schedule next cleanup in 24 hours
    timer = threading.Timer(86400.0, schedule_cleanup)  # 86400 seconds = 24 hours
    timer.daemon = True
    timer.start()

# Department choices for dropdown
DEPARTMENTS = [
    ('CSE', 'Computer Science Engineering'),
    ('IT', 'Information Technology'),
    ('ECE', 'Electronics and Communication Engineering'),
    ('EEE', 'Electrical and Electronics Engineering'),
    ('BME', 'Biomedical Engineering'),
    ('AERO', 'Aeronautical Engineering'),
    ('AIDS', 'Artificial Intelligence and Data Science'),
    ('CSBS', 'Computer Science and Business Systems'),
    ('CIVIL', 'Civil Engineering'),
    ('PCT', 'Petroleum and Chemical Technology')
]

DESIGNATION_CHOICES = [
    ('Student', 'Student'),
    ('Staff', 'Staff')
]

# Helper function to get setting values
def get_setting_value(setting_name, default_value=None):
    """Get a setting value from the database"""
    try:
        setting = LibrarySettings.query.filter_by(setting_name=setting_name).first()
        return setting.setting_value if setting else default_value
    except:
        return default_value

def update_setting_value(setting_name, new_value, updated_by='admin'):
    """Update a setting value in the database"""
    try:
        setting = LibrarySettings.query.filter_by(setting_name=setting_name).first()
        if setting:
            setting.setting_value = new_value
            setting.updated_by = updated_by
            setting.updated_at = datetime.utcnow()
        else:
            setting = LibrarySettings(
                setting_name=setting_name,
                setting_value=new_value,
                updated_by=updated_by
            )
            db.session.add(setting)
        
        db.session.commit()
        return True
    except Exception as e:
        db.session.rollback()
        return False

# Routes
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']

        user = None
        user_role = None

        # Auto-detect user role by checking all user types
        # Check Admin first
        admin = Admin.query.filter(
            db.or_(Admin.email == username, Admin.username == username)
        ).first()
        if admin and check_password_hash(admin.password, password):
            user = admin
            user_role = 'admin'

        # Check Librarian if not admin
        if not user:
            librarian = Librarian.query.filter(
                db.or_(Librarian.email == username, Librarian.username == username)
            ).first()
            if librarian and check_password_hash(librarian.password, password):
                user = librarian
                user_role = 'librarian'

        # Check Student if not admin or librarian
        if not user:
            student = Student.query.filter(
                db.or_(
                    Student.email == username,
                    Student.username == username,
                    Student.user_id == username
                )
            ).first()

            if student and check_password_hash(student.password, password):
                # Check if student account is still valid
                if student.validity_date < date.today():
                    flash('Your account has expired. Please contact the administrator.')
                    return render_template('index.html')
                user = student
                user_role = 'student'

        if user and user_role:
            session['user_id'] = user.id
            session['user_role'] = user_role
            session['user_name'] = user.name
            flash(f'Welcome, {user.name}!')
            return redirect(url_for(f'{user_role}_dashboard'))
        else:
            flash('Invalid username or password')

    return render_template('index.html')

# Keep the old route for backward compatibility
@app.route('/login/<role>', methods=['GET', 'POST'])
def login_role(role):
    return redirect(url_for('login'))

@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('index'))

# Admin Routes
@app.route('/admin/dashboard')
def admin_dashboard():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))

    total_books = Book.query.count()
    total_ebooks = EBook.query.filter_by(is_active=True).count()
    total_students = Student.query.count()
    total_librarians = Librarian.query.count()
    issued_books = Issue.query.filter_by(return_date=None).count()
    overdue_books = Issue.query.filter(
        Issue.return_date == None,
        Issue.due_date < datetime.utcnow()
    ).count()

    stats = {
        'total_books': total_books,
        'total_ebooks': total_ebooks,
        'total_students': total_students,
        'total_librarians': total_librarians,
        'issued_books': issued_books,
        'overdue_books': overdue_books
    }

    return render_template('admin_dashboard.html', stats=stats)

@app.route('/admin/settings', methods=['GET', 'POST'])
def admin_settings():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))
    
    if request.method == 'POST':
        # Update settings
        student_book_limit = request.form.get('student_book_limit')
        staff_book_limit = request.form.get('staff_book_limit')
        default_issue_days = request.form.get('default_issue_days')
        fine_per_day = request.form.get('fine_per_day')
        renewal_limit = request.form.get('renewal_limit')
        
        try:
            # Update each setting
            if student_book_limit:
                update_setting_value('student_book_limit', int(student_book_limit), session.get('username'))
            if staff_book_limit:
                update_setting_value('staff_book_limit', int(staff_book_limit), session.get('username'))
            if default_issue_days:
                update_setting_value('default_issue_days', int(default_issue_days), session.get('username'))
            if fine_per_day:
                update_setting_value('fine_per_day', float(fine_per_day), session.get('username'))
            if renewal_limit:
                update_setting_value('renewal_limit', int(renewal_limit), session.get('username'))
            
            flash('Settings updated successfully!', 'success')
        except ValueError as e:
            flash(f'Error updating settings: Invalid input values', 'danger')
        except Exception as e:
            flash(f'Error updating settings: {str(e)}', 'danger')
        
        return redirect(url_for('admin_settings'))
    
    # Get current settings
    settings = {
        'student_book_limit': get_setting_value('student_book_limit', 3),
        'staff_book_limit': get_setting_value('staff_book_limit', 5),
        'default_issue_days': get_setting_value('default_issue_days', 14),
        'fine_per_day': get_setting_value('fine_per_day', 2.0),
        'renewal_limit': get_setting_value('renewal_limit', 2)
    }
    
    return render_template('admin_settings.html', settings=settings)

@app.route('/admin/books')
def admin_books():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))
    
    books = Book.query.all()
    return render_template('admin_books.html', books=books)

@app.route('/admin/add_book', methods=['GET', 'POST'])
def admin_add_book():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))
    
    if request.method == 'POST':
        try:
            # Check if access number already exists
            if Book.query.filter_by(access_no=request.form['access_no']).first():
                flash('Access number already exists!')
                return render_template('admin_add_book.html', departments=DEPARTMENTS)
            
            copies = int(request.form['copies'])
            
            book = Book(
                access_no=request.form['access_no'],
                title=request.form['title'],
                author=request.form['author'],
                publisher=request.form['publisher'],
                subject=request.form['subject'],
                department=request.form['department'],
                category=request.form['category'],
                location=request.form['location'],
                copies=copies,
                quantity=copies,  # For backward compatibility
                available_count=copies
            )
            db.session.add(book)
            db.session.commit()
            flash('Book added successfully!')
            return redirect(url_for('admin_books'))
            
        except ValueError as e:
            flash('Invalid number of copies. Please enter a valid number.')
            return render_template('admin_add_book.html', departments=DEPARTMENTS)
        except Exception as e:
            flash(f'Error adding book: {str(e)}')
            return render_template('admin_add_book.html', departments=DEPARTMENTS)
    
    return render_template('admin_add_book.html', departments=DEPARTMENTS)

@app.route('/admin/edit_book/<int:book_id>', methods=['GET', 'POST'])
def admin_edit_book(book_id):
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))
    
    book = Book.query.get_or_404(book_id)
    
    if request.method == 'POST':
        book.title = request.form['title']
        book.author = request.form['author']
        book.category = request.form['category']
        old_quantity = book.quantity
        new_quantity = int(request.form['quantity'])
        
        # Update available count based on quantity change
        difference = new_quantity - old_quantity
        book.quantity = new_quantity
        book.available_count = max(0, book.available_count + difference)
        
        db.session.commit()
        flash('Book updated successfully!')
        return redirect(url_for('admin_books'))
    
    return render_template('admin_edit_book.html', book=book)

@app.route('/admin/delete_book/<int:book_id>')
def admin_delete_book(book_id):
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))
    
    book = Book.query.get_or_404(book_id)
    
    # Check if book is currently issued
    active_issues = Issue.query.filter_by(book_id=book_id, return_date=None).count()
    if active_issues > 0:
        flash('Cannot delete book. It is currently issued to students.')
        return redirect(url_for('admin_books'))
    
    db.session.delete(book)
    db.session.commit()
    flash('Book deleted successfully!')
    return redirect(url_for('admin_books'))

@app.route('/admin/librarians')
def admin_librarians():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))
    
    librarians = Librarian.query.all()
    return render_template('admin_librarians.html', librarians=librarians)

@app.route('/admin/add_librarian', methods=['GET', 'POST'])
def admin_add_librarian():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))
    
    if request.method == 'POST':
        # Check if email already exists
        if Librarian.query.filter_by(email=request.form['email']).first():
            flash('Email already exists!')
            return render_template('admin_add_librarian.html')
        
        librarian = Librarian(
            name=request.form['name'],
            email=request.form['email'],
            password=generate_password_hash(request.form['password'])
        )
        db.session.add(librarian)
        db.session.commit()
        flash('Librarian added successfully!')
        return redirect(url_for('admin_librarians'))
    
    return render_template('admin_add_librarian.html')

@app.route('/admin/students')
def admin_students():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))
    
    students = Student.query.all()
    return render_template('admin_students.html', students=students)

@app.route('/admin/student_details/<int:student_id>')
def admin_student_details(student_id):
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))
    
    student = Student.query.get_or_404(student_id)
    active_issues = Issue.query.filter_by(student_id=student.id, return_date=None).all()
    issue_history = Issue.query.filter_by(student_id=student.id).order_by(Issue.issue_date.desc()).all()
    total_fine = sum(issue.fine_amount for issue in issue_history if issue.fine_amount)
    
    from datetime import datetime
    overdue_issues = [issue for issue in active_issues if issue.due_date and issue.due_date < datetime.utcnow()]
    
    return render_template('admin_student_details.html', 
                         student=student, 
                         active_issues=active_issues,
                         issue_history=issue_history,
                         total_fine=total_fine,
                         overdue_count=len(overdue_issues))

@app.route('/admin/add_student', methods=['GET', 'POST'])
def admin_add_student():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))
    
    if request.method == 'POST':
        try:
            # Check if user_id, username, email already exists
            if Student.query.filter_by(user_id=request.form['user_id']).first():
                flash('User ID already exists!', 'danger')
                return render_template('admin_add_student.html', departments=DEPARTMENTS, designations=DESIGNATION_CHOICES)
            
            if Student.query.filter_by(username=request.form['username']).first():
                flash('Username already exists!', 'danger')
                return render_template('admin_add_student.html', departments=DEPARTMENTS, designations=DESIGNATION_CHOICES)
            
            if Student.query.filter_by(email=request.form['email']).first():
                flash('Email already exists!', 'danger')
                return render_template('admin_add_student.html', departments=DEPARTMENTS, designations=DESIGNATION_CHOICES)
            
            # Parse validity date
            validity_date = None
            if request.form.get('validity_date'):
                from datetime import datetime
                validity_date = datetime.strptime(request.form['validity_date'], '%Y-%m-%d').date()
            
            # Parse date of birth
            dob = None
            if request.form.get('dob'):
                dob = datetime.strptime(request.form['dob'], '%Y-%m-%d').date()
            
            # Generate password: user_id + name
            password = request.form['user_id'] + request.form['name'].replace(' ', '').lower()
            hashed_password = generate_password_hash(password)
            
            # Create new student (user_id serves as roll_number for students, staff_id for staff)
            student = Student(
                user_id=request.form['user_id'],
                name=request.form['name'],
                username=request.form['username'],
                email=request.form['email'],
                password=hashed_password,
                roll_number=request.form['user_id'],  # Use user_id as roll_number
                department=request.form['department'],
                designation=request.form['designation'],
                course=request.form.get('course'),
                current_year=request.form.get('current_year'),
                dob=dob,
                validity_date=validity_date
            )
            
            db.session.add(student)
            db.session.commit()
            
            flash(f'Student added successfully! Password: {password}', 'success')
            return redirect(url_for('admin_students'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Error adding student: {str(e)}', 'danger')
            
    return render_template('admin_add_student.html', departments=DEPARTMENTS, designations=DESIGNATION_CHOICES)

@app.route('/admin/issue_history')
def admin_issue_history():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))

    issues = Issue.query.order_by(Issue.issue_date.desc()).all()

    from datetime import date
    overdue_count = len([i for i in issues if i.return_date is None and i.due_date < date.today()])

    return render_template('admin_issue_history.html', issues=issues, overdue_count=overdue_count)

@app.route('/admin/reports')
def admin_reports():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))
    return render_template('admin_reports.html')

@app.route('/admin/ebooks')
def admin_ebooks():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))

    ebooks = EBook.query.all()
    return render_template('admin_ebooks.html', ebooks=ebooks)

@app.route('/admin/add_ebook', methods=['GET', 'POST'])
def admin_add_ebook():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))

    if request.method == 'POST':
        try:
            # Check if access number already exists
            if EBook.query.filter_by(access_no=request.form['access_no']).first():
                flash('Access number already exists!')
                return render_template('admin_add_ebook.html', departments=DEPARTMENTS)

            ebook = EBook(
                access_no=request.form['access_no'],
                title=request.form['title'],
                author=request.form['author'],
                publisher=request.form['publisher'],
                subject=request.form['subject'],
                department=request.form['department'],
                category=request.form['category'],
                file_format=request.form['file_format'],
                file_size=request.form.get('file_size', ''),
                download_url=request.form.get('download_url', ''),
                isbn=request.form.get('isbn', ''),
                pages=int(request.form['pages']) if request.form.get('pages') else None,
                language=request.form.get('language', 'English'),
                description=request.form.get('description', '')
            )
            db.session.add(ebook)
            db.session.commit()
            flash('E-book added successfully!')
            return redirect(url_for('admin_ebooks'))

        except ValueError as e:
            flash('Invalid input. Please check your data.')
            return render_template('admin_add_ebook.html', departments=DEPARTMENTS)
        except Exception as e:
            flash(f'Error adding e-book: {str(e)}')
            return render_template('admin_add_ebook.html', departments=DEPARTMENTS)

    return render_template('admin_add_ebook.html', departments=DEPARTMENTS)

@app.route('/admin/bulk_ebooks', methods=['GET', 'POST'])
def admin_bulk_ebooks():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))

    if request.method == 'POST':
        if 'file' not in request.files:
            flash('No file selected')
            return redirect(request.url)

        file = request.files['file']
        if file.filename == '':
            flash('No file selected')
            return redirect(request.url)

        if file and allowed_file(file.filename):
            try:
                df = pd.read_excel(file)

                required_columns = ['access_no', 'title', 'author', 'publisher', 'subject', 'department', 'category', 'file_format']
                missing_columns = [col for col in required_columns if col not in df.columns]

                if missing_columns:
                    flash(f'Missing required columns: {", ".join(missing_columns)}')
                    return render_template('admin_bulk_ebooks.html')

                success_count = 0
                error_count = 0
                errors = []

                for index, row in df.iterrows():
                    try:
                        # Check if access number already exists
                        if EBook.query.filter_by(access_no=row['access_no']).first():
                            error_count += 1
                            errors.append(f'Row {index + 2}: Access number {row["access_no"]} already exists')
                            continue

                        ebook = EBook(
                            access_no=row['access_no'],
                            title=row['title'],
                            author=row['author'],
                            publisher=row['publisher'],
                            subject=row['subject'],
                            department=row['department'],
                            category=row['category'],
                            file_format=row['file_format'],
                            file_size=row.get('file_size', ''),
                            download_url=row.get('download_url', ''),
                            isbn=row.get('isbn', ''),
                            pages=int(row['pages']) if pd.notna(row.get('pages')) else None,
                            language=row.get('language', 'English'),
                            description=row.get('description', '')
                        )

                        db.session.add(ebook)
                        success_count += 1

                    except Exception as e:
                        error_count += 1
                        errors.append(f'Row {index + 2}: {str(e)}')

                if success_count > 0:
                    db.session.commit()
                    flash(f'Successfully added {success_count} e-books!')

                if error_count > 0:
                    flash(f'{error_count} e-books failed to upload. Errors: {"; ".join(errors[:5])}{"..." if len(errors) > 5 else ""}')

                return redirect(url_for('admin_ebooks'))

            except Exception as e:
                flash(f'Error processing file: {str(e)}')
                return render_template('admin_bulk_ebooks.html')
        else:
            flash('Invalid file format. Please upload an Excel file.')

    return render_template('admin_bulk_ebooks.html')

@app.route('/admin/ebooks/delete/<int:ebook_id>', methods=['POST'])
def admin_delete_ebook(ebook_id):
    if session.get('user_role') != 'admin':
        return jsonify({'success': False, 'message': 'Unauthorized'}), 403

    try:
        ebook = EBook.query.get_or_404(ebook_id)
        db.session.delete(ebook)
        db.session.commit()
        return jsonify({'success': True, 'message': 'E-book deleted successfully!'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'Error deleting e-book: {str(e)}'}), 500

@app.route('/admin/edit_ebook/<int:ebook_id>', methods=['GET', 'POST'])
def admin_edit_ebook(ebook_id):
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))

    ebook = EBook.query.get_or_404(ebook_id)

    if request.method == 'POST':
        try:
            # Check if access number already exists (excluding current ebook)
            existing_ebook = EBook.query.filter_by(access_no=request.form['access_no']).first()
            if existing_ebook and existing_ebook.ebook_id != ebook_id:
                flash('Access number already exists!')
                return render_template('admin_edit_ebook.html', ebook=ebook, departments=DEPARTMENTS)

            ebook.access_no = request.form['access_no']
            ebook.title = request.form['title']
            ebook.author = request.form['author']
            ebook.publisher = request.form['publisher']
            ebook.subject = request.form['subject']
            ebook.department = request.form['department']
            ebook.category = request.form['category']
            ebook.file_format = request.form['file_format']
            ebook.file_size = request.form.get('file_size', '')
            ebook.download_url = request.form.get('download_url', '')
            ebook.isbn = request.form.get('isbn', '')
            ebook.pages = int(request.form['pages']) if request.form.get('pages') else None
            ebook.language = request.form.get('language', 'English')
            ebook.description = request.form.get('description', '')

            db.session.commit()
            flash('E-book updated successfully!')
            return redirect(url_for('admin_ebooks'))

        except ValueError as e:
            flash('Invalid input. Please check your data.')
            return render_template('admin_edit_ebook.html', ebook=ebook, departments=DEPARTMENTS)
        except Exception as e:
            flash(f'Error updating e-book: {str(e)}')
            return render_template('admin_edit_ebook.html', ebook=ebook, departments=DEPARTMENTS)

    return render_template('admin_edit_ebook.html', ebook=ebook, departments=DEPARTMENTS)

@app.route('/admin/generate_report', methods=['GET', 'POST'])
def admin_generate_report():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))

    # Handle GET requests for quick reports
    if request.method == 'GET':
        report_type = request.args.get('report_type')
        export_format = request.args.get('export_format')
        if not report_type or not export_format:
            return redirect(url_for('admin_reports'))
    else:
        report_type = request.form.get('report_type')
        export_format = request.form.get('export_format')

    start_date = request.form.get('start_date') or request.args.get('start_date')
    end_date = request.form.get('end_date') or request.args.get('end_date')
    department = request.form.get('department') or request.args.get('department')
    designation = request.form.get('designation') or request.args.get('designation')
    preview = request.form.get('preview') == 'true'

    if not report_type or not export_format:
        if preview:
            return jsonify({'success': False, 'message': 'Report type and format are required'})
        flash('Report type and export format are required')
        return redirect(url_for('admin_reports'))

    try:
        # Generate report data based on type
        data, filename = generate_report_data(report_type, start_date, end_date, department, designation)

        if preview:
            # Return HTML preview
            html_preview = generate_html_preview(data, report_type)
            return jsonify({'success': True, 'html': html_preview})

        # Generate file based on format
        if export_format == 'excel':
            return generate_excel_report(data, filename)
        elif export_format == 'pdf':
            return generate_pdf_report(data, filename, report_type)
        else:
            flash('Invalid export format')
            return redirect(url_for('admin_reports'))

    except Exception as e:
        if preview:
            return jsonify({'success': False, 'message': str(e)})
        flash(f'Error generating report: {str(e)}')
        return redirect(url_for('admin_reports'))

def generate_report_data(report_type, start_date=None, end_date=None, department=None, designation=None):
    """Generate report data based on type and filters"""

    # Parse dates if provided
    start_dt = None
    end_dt = None
    if start_date:
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
    if end_date:
        end_dt = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)  # Include end date

    # Base query
    query = Issue.query.join(Student).join(Book)

    # Apply date filters
    if start_dt:
        query = query.filter(Issue.issue_date >= start_dt)
    if end_dt:
        query = query.filter(Issue.issue_date < end_dt)

    # Apply department filter
    if department:
        query = query.filter(Student.department == department)

    # Apply designation filter
    if designation:
        query = query.filter(Student.designation == designation)

    data = []
    filename = f"library_report_{report_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

    if report_type == 'issued_books':
        # Currently issued books
        issues = query.filter(Issue.return_date == None).all()
        for issue in issues:
            data.append({
                'Issue ID': issue.issue_id,
                'Book Title': issue.book.title,
                'Author': issue.book.author,
                'Access No': issue.book.access_no,
                'Student Name': issue.student.name,
                'Student ID': issue.student.user_id,
                'Department': issue.student.department,
                'Designation': issue.student.designation,
                'Issue Date': issue.issue_date.strftime('%Y-%m-%d'),
                'Due Date': issue.due_date.strftime('%Y-%m-%d'),
                'Days Overdue': max(0, (datetime.now() - issue.due_date).days) if datetime.now() > issue.due_date else 0
            })
        filename += "_currently_issued"

    elif report_type == 'returned_books':
        # Returned books
        issues = query.filter(Issue.return_date != None).all()
        for issue in issues:
            data.append({
                'Issue ID': issue.issue_id,
                'Book Title': issue.book.title,
                'Author': issue.book.author,
                'Access No': issue.book.access_no,
                'Student Name': issue.student.name,
                'Student ID': issue.student.user_id,
                'Department': issue.student.department,
                'Designation': issue.student.designation,
                'Issue Date': issue.issue_date.strftime('%Y-%m-%d'),
                'Due Date': issue.due_date.strftime('%Y-%m-%d'),
                'Return Date': issue.return_date.strftime('%Y-%m-%d'),
                'Fine': f"₹{issue.fine:.2f}" if issue.fine > 0 else "₹0.00"
            })
        filename += "_returned_books"

    elif report_type == 'overdue_books':
        # Overdue books
        issues = query.filter(
            Issue.return_date == None,
            Issue.due_date < datetime.now()
        ).all()
        for issue in issues:
            days_overdue = (datetime.now() - issue.due_date).days
            data.append({
                'Issue ID': issue.issue_id,
                'Book Title': issue.book.title,
                'Author': issue.book.author,
                'Access No': issue.book.access_no,
                'Student Name': issue.student.name,
                'Student ID': issue.student.user_id,
                'Department': issue.student.department,
                'Designation': issue.student.designation,
                'Issue Date': issue.issue_date.strftime('%Y-%m-%d'),
                'Due Date': issue.due_date.strftime('%Y-%m-%d'),
                'Days Overdue': days_overdue,
                'Expected Fine': f"₹{days_overdue * 1.0:.2f}"  # ₹1 per day
            })
        filename += "_overdue_books"

    elif report_type == 'all_transactions':
        # All transactions
        issues = query.all()
        for issue in issues:
            data.append({
                'Issue ID': issue.issue_id,
                'Book Title': issue.book.title,
                'Author': issue.book.author,
                'Access No': issue.book.access_no,
                'Student Name': issue.student.name,
                'Student ID': issue.student.user_id,
                'Department': issue.student.department,
                'Designation': issue.student.designation,
                'Issue Date': issue.issue_date.strftime('%Y-%m-%d'),
                'Due Date': issue.due_date.strftime('%Y-%m-%d'),
                'Return Date': issue.return_date.strftime('%Y-%m-%d') if issue.return_date else 'Not Returned',
                'Status': 'Returned' if issue.return_date else 'Issued',
                'Fine': f"₹{issue.fine:.2f}" if issue.fine > 0 else "₹0.00"
            })
        filename += "_all_transactions"

    elif report_type == 'fine_report':
        # Fine report
        issues = query.filter(Issue.fine > 0).all()
        for issue in issues:
            data.append({
                'Issue ID': issue.issue_id,
                'Book Title': issue.book.title,
                'Student Name': issue.student.name,
                'Student ID': issue.student.user_id,
                'Department': issue.student.department,
                'Designation': issue.student.designation,
                'Issue Date': issue.issue_date.strftime('%Y-%m-%d'),
                'Due Date': issue.due_date.strftime('%Y-%m-%d'),
                'Return Date': issue.return_date.strftime('%Y-%m-%d') if issue.return_date else 'Not Returned',
                'Fine Amount': f"₹{issue.fine:.2f}"
            })
        filename += "_fine_report"

    elif report_type == 'student_activity':
        # Student activity report
        from sqlalchemy import func
        student_stats = db.session.query(
            Student.name,
            Student.user_id,
            Student.department,
            Student.designation,
            func.count(Issue.issue_id).label('total_books_borrowed'),
            func.count(Issue.return_date).label('books_returned'),
            func.sum(Issue.fine).label('total_fines')
        ).join(Issue).group_by(Student.id).all()

        for stat in student_stats:
            data.append({
                'Student Name': stat.name,
                'Student ID': stat.user_id,
                'Department': stat.department,
                'Designation': stat.designation,
                'Total Books Borrowed': stat.total_books_borrowed,
                'Books Returned': stat.books_returned or 0,
                'Books Currently Issued': stat.total_books_borrowed - (stat.books_returned or 0),
                'Total Fines': f"₹{stat.total_fines:.2f}" if stat.total_fines else "₹0.00"
            })
        filename += "_student_activity"

    return data, filename

def generate_excel_report(data, filename):
    """Generate Excel report"""
    if not data:
        raise Exception("No data available for the selected criteria")

    df = pd.DataFrame(data)

    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='Report')

        # Get the workbook and worksheet
        workbook = writer.book
        worksheet = writer.sheets['Report']

        # Auto-adjust column widths
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width

    output.seek(0)

    return send_file(
        output,
        as_attachment=True,
        download_name=f'{filename}.xlsx',
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )

def generate_pdf_report(data, filename, report_type):
    """Generate PDF report using HTML to PDF conversion"""
    if not data:
        raise Exception("No data available for the selected criteria")

    # For now, we'll create a simple HTML table and convert to PDF
    # This is a basic implementation - you can enhance it with proper PDF libraries
    html_content = f"""
    <html>
    <head>
        <title>{report_type.replace('_', ' ').title()} Report</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            h1 {{ color: #333; text-align: center; }}
            table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; font-weight: bold; }}
            tr:nth-child(even) {{ background-color: #f9f9f9; }}
            .header {{ text-align: center; margin-bottom: 20px; }}
            .footer {{ text-align: center; margin-top: 20px; font-size: 12px; color: #666; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>Library Management System</h1>
            <h2>{report_type.replace('_', ' ').title()} Report</h2>
            <p>Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
        <table>
            <thead>
                <tr>
    """

    # Add headers
    if data:
        for key in data[0].keys():
            html_content += f"<th>{key}</th>"

    html_content += """
                </tr>
            </thead>
            <tbody>
    """

    # Add data rows
    for row in data:
        html_content += "<tr>"
        for value in row.values():
            html_content += f"<td>{value}</td>"
        html_content += "</tr>"

    html_content += """
            </tbody>
        </table>
        <div class="footer">
            <p>Total Records: {}</p>
        </div>
    </body>
    </html>
    """.format(len(data))

    # For now, return HTML as PDF (you can integrate with libraries like weasyprint or pdfkit)
    output = BytesIO()
    output.write(html_content.encode('utf-8'))
    output.seek(0)

    return send_file(
        output,
        as_attachment=True,
        download_name=f'{filename}.html',  # Change to .pdf when using proper PDF library
        mimetype='text/html'  # Change to application/pdf when using proper PDF library
    )

def generate_html_preview(data, report_type):
    """Generate HTML preview for reports"""
    if not data:
        return "<div class='alert alert-warning'>No data available for the selected criteria</div>"

    # Limit preview to first 10 rows
    preview_data = data[:10]

    html = f"""
    <div class="table-responsive">
        <h5>{report_type.replace('_', ' ').title()} Report Preview</h5>
        <p class="text-muted">Showing first {len(preview_data)} of {len(data)} records</p>
        <table class="table table-striped table-bordered">
            <thead class="table-dark">
                <tr>
    """

    # Add headers
    if preview_data:
        for key in preview_data[0].keys():
            html += f"<th>{key}</th>"

    html += """
                </tr>
            </thead>
            <tbody>
    """

    # Add data rows
    for row in preview_data:
        html += "<tr>"
        for value in row.values():
            html += f"<td>{value}</td>"
        html += "</tr>"

    html += """
            </tbody>
        </table>
    </div>
    """

    if len(data) > 10:
        html += f"<div class='alert alert-info mt-2'>... and {len(data) - 10} more records</div>"

    return html

@app.route('/admin/delete_librarian/<int:librarian_id>', methods=['POST'])
def admin_delete_librarian(librarian_id):
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    
    librarian = Librarian.query.get_or_404(librarian_id)
    db.session.delete(librarian)
    db.session.commit()
    flash('Librarian deleted successfully!')
    return redirect(url_for('admin_librarians'))

@app.route('/admin/delete_student/<int:student_id>', methods=['POST'])
def admin_delete_student(student_id):
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    
    student = Student.query.get_or_404(student_id)
    
    # Check if student has active issues
    active_issues = Issue.query.filter_by(student_id=student_id, return_date=None).count()
    if active_issues > 0:
        flash('Cannot delete student. Student has active book issues.')
        return redirect(url_for('admin_students'))
    
    db.session.delete(student)
    db.session.commit()
    flash('Student deleted successfully!')
    return redirect(url_for('admin_students'))

# Librarian Routes
@app.route('/librarian/dashboard')
def librarian_dashboard():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))
    
    total_books = Book.query.count()
    available_books = sum(book.available_count for book in Book.query.all())
    issued_books = Issue.query.filter_by(return_date=None).count()
    overdue_books = Issue.query.filter(
        Issue.return_date == None,
        Issue.due_date < datetime.utcnow()
    ).count()
    
    stats = {
        'total_books': total_books,
        'available_books': available_books,
        'issued_books': issued_books,
        'overdue_books': overdue_books
    }
    
    return render_template('librarian_dashboard.html', stats=stats)

@app.route('/librarian/issue_book', methods=['GET', 'POST'])
def librarian_issue_book():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))
    
    if request.method == 'POST':
        book_id = request.form.get('book_id')
        student_user_id = request.form.get('student_user_id')
        if not book_id or not student_user_id:
            flash('Please select a book and enter a valid Student User ID.')
            books = Book.query.filter(Book.available_count > 0).all()
            return render_template('librarian_issue_book.html', books=books)

        book = Book.query.get(book_id)
        student = Student.query.filter_by(user_id=student_user_id).first()

        if not book or not student:
            flash('Invalid book or student!')
            books = Book.query.filter(Book.available_count > 0).all()
            return render_template('librarian_issue_book.html', books=books)

        if book.available_count <= 0:
            flash('Book not available!')
            books = Book.query.filter(Book.available_count > 0).all()
            return render_template('librarian_issue_book.html', books=books)

        # Check if student already has this book
        existing_issue = Issue.query.filter_by(
            book_id=book_id,
            student_id=student.id,
            return_date=None
        ).first()

        if existing_issue:
            flash('Student already has this book!')
            books = Book.query.filter(Book.available_count > 0).all()
            return render_template('librarian_issue_book.html', books=books)

        # Check book limit
        active_issues_count = Issue.query.filter_by(student_id=student.id, return_date=None).count()
        if student.designation == 'Staff':
            book_limit = int(get_setting_value('staff_book_limit', 5))
        else:
            book_limit = int(get_setting_value('student_book_limit', 3))
        if active_issues_count >= book_limit:
            flash('Maximum limit reached for borrowing books!')
            books = Book.query.filter(Book.available_count > 0).all()
            return render_template('librarian_issue_book.html', books=books)

        # Create new issue
        issue = Issue(
            book_id=book_id,
            student_id=student.id,
            due_date=datetime.utcnow() + timedelta(days=14)  # 14 days loan period
        )

        book.available_count -= 1

        db.session.add(issue)
        db.session.commit()

        flash('Book issued successfully!')
        return redirect(url_for('librarian_dashboard'))

    books = Book.query.filter(Book.available_count > 0).all()
    return render_template('librarian_issue_book.html', books=books)

@app.route('/librarian/return_book', methods=['GET', 'POST'])
def librarian_return_book():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))
    
    if request.method == 'POST':
        issue_id = request.form.get('issue_id')
        if not issue_id:
            flash('No issue selected for return. Please try again.')
            active_issues = Issue.query.filter_by(return_date=None).all()
            return render_template('librarian_return_book.html', issues=active_issues)

        issue = Issue.query.get(issue_id)

        if not issue or issue.return_date:
            flash('Invalid issue or book already returned!')
            active_issues = Issue.query.filter_by(return_date=None).all()
            return render_template('librarian_return_book.html', issues=active_issues)

        # Calculate fine
        return_date = datetime.utcnow()
        fine = calculate_fine(issue.due_date, return_date)

        # Update issue record
        issue.return_date = return_date
        issue.fine = fine

        # Update book availability
        book = Book.query.get(issue.book_id)
        book.available_count += 1

        db.session.commit()

        if fine > 0:
            flash(f'Book returned successfully! Fine: ${fine:.2f}')
        else:
            flash('Book returned successfully!')

        return redirect(url_for('librarian_dashboard'))

    # Get all active issues
    active_issues = Issue.query.filter_by(return_date=None).all()
    return render_template('librarian_return_book.html', issues=active_issues)

@app.route('/librarian/books')
def librarian_books():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))

    books = Book.query.all()
    return render_template('librarian_books.html', books=books)

@app.route('/librarian/ebooks')
def librarian_ebooks():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))

    ebooks = EBook.query.all()
    return render_template('librarian_ebooks.html', ebooks=ebooks)

@app.route('/librarian/add_ebook', methods=['GET', 'POST'])
def librarian_add_ebook():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))

    if request.method == 'POST':
        try:
            # Check if access number already exists
            if EBook.query.filter_by(access_no=request.form['access_no']).first():
                flash('Access number already exists!')
                return render_template('librarian_add_ebook.html', departments=DEPARTMENTS)

            ebook = EBook(
                access_no=request.form['access_no'],
                title=request.form['title'],
                author=request.form['author'],
                publisher=request.form['publisher'],
                subject=request.form['subject'],
                department=request.form['department'],
                category=request.form['category'],
                file_format=request.form['file_format'],
                file_size=request.form.get('file_size', ''),
                download_url=request.form.get('download_url', ''),
                isbn=request.form.get('isbn', ''),
                pages=int(request.form['pages']) if request.form.get('pages') else None,
                language=request.form.get('language', 'English'),
                description=request.form.get('description', '')
            )
            db.session.add(ebook)
            db.session.commit()
            flash('E-book added successfully!')
            return redirect(url_for('librarian_ebooks'))

        except ValueError as e:
            flash('Invalid input. Please check your data.')
            return render_template('librarian_add_ebook.html', departments=DEPARTMENTS)
        except Exception as e:
            flash(f'Error adding e-book: {str(e)}')
            return render_template('librarian_add_ebook.html', departments=DEPARTMENTS)

    return render_template('librarian_add_ebook.html', departments=DEPARTMENTS)

@app.route('/librarian/bulk_ebooks', methods=['GET', 'POST'])
def librarian_bulk_ebooks():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))

    if request.method == 'POST':
        if 'file' not in request.files:
            flash('No file selected')
            return redirect(request.url)

        file = request.files['file']
        if file.filename == '':
            flash('No file selected')
            return redirect(request.url)

        if file and allowed_file(file.filename):
            try:
                df = pd.read_excel(file)

                required_columns = ['access_no', 'title', 'author', 'publisher', 'subject', 'department', 'category', 'file_format']
                missing_columns = [col for col in required_columns if col not in df.columns]

                if missing_columns:
                    flash(f'Missing required columns: {", ".join(missing_columns)}')
                    return render_template('librarian_bulk_ebooks.html')

                success_count = 0
                error_count = 0
                errors = []

                for index, row in df.iterrows():
                    try:
                        # Check if access number already exists
                        if EBook.query.filter_by(access_no=row['access_no']).first():
                            error_count += 1
                            errors.append(f'Row {index + 2}: Access number {row["access_no"]} already exists')
                            continue

                        ebook = EBook(
                            access_no=row['access_no'],
                            title=row['title'],
                            author=row['author'],
                            publisher=row['publisher'],
                            subject=row['subject'],
                            department=row['department'],
                            category=row['category'],
                            file_format=row['file_format'],
                            file_size=row.get('file_size', ''),
                            download_url=row.get('download_url', ''),
                            isbn=row.get('isbn', ''),
                            pages=int(row['pages']) if pd.notna(row.get('pages')) else None,
                            language=row.get('language', 'English'),
                            description=row.get('description', '')
                        )

                        db.session.add(ebook)
                        success_count += 1

                    except Exception as e:
                        error_count += 1
                        errors.append(f'Row {index + 2}: {str(e)}')

                if success_count > 0:
                    db.session.commit()
                    flash(f'Successfully added {success_count} e-books!')

                if error_count > 0:
                    flash(f'{error_count} e-books failed to upload. Errors: {"; ".join(errors[:5])}{"..." if len(errors) > 5 else ""}')

                return redirect(url_for('librarian_ebooks'))

            except Exception as e:
                flash(f'Error processing file: {str(e)}')
                return render_template('librarian_bulk_ebooks.html')
        else:
            flash('Invalid file format. Please upload an Excel file.')

    return render_template('librarian_bulk_ebooks.html')

@app.route('/librarian/edit_ebook/<int:ebook_id>', methods=['GET', 'POST'])
def librarian_edit_ebook(ebook_id):
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))

    ebook = EBook.query.get_or_404(ebook_id)

    if request.method == 'POST':
        try:
            # Check if access number already exists (excluding current ebook)
            existing_ebook = EBook.query.filter_by(access_no=request.form['access_no']).first()
            if existing_ebook and existing_ebook.ebook_id != ebook_id:
                flash('Access number already exists!')
                return render_template('librarian_edit_ebook.html', ebook=ebook, departments=DEPARTMENTS)

            ebook.access_no = request.form['access_no']
            ebook.title = request.form['title']
            ebook.author = request.form['author']
            ebook.publisher = request.form['publisher']
            ebook.subject = request.form['subject']
            ebook.department = request.form['department']
            ebook.category = request.form['category']
            ebook.file_format = request.form['file_format']
            ebook.file_size = request.form.get('file_size', '')
            ebook.download_url = request.form.get('download_url', '')
            ebook.isbn = request.form.get('isbn', '')
            ebook.pages = int(request.form['pages']) if request.form.get('pages') else None
            ebook.language = request.form.get('language', 'English')
            ebook.description = request.form.get('description', '')

            db.session.commit()
            flash('E-book updated successfully!')
            return redirect(url_for('librarian_ebooks'))

        except ValueError as e:
            flash('Invalid input. Please check your data.')
            return render_template('librarian_edit_ebook.html', ebook=ebook, departments=DEPARTMENTS)
        except Exception as e:
            flash(f'Error updating e-book: {str(e)}')
            return render_template('librarian_edit_ebook.html', ebook=ebook, departments=DEPARTMENTS)

    return render_template('librarian_edit_ebook.html', ebook=ebook, departments=DEPARTMENTS)

@app.route('/librarian/add_book', methods=['GET', 'POST'])
def librarian_add_book():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))
    
    if request.method == 'POST':
        try:
            # Check if access number already exists
            if Book.query.filter_by(access_no=request.form['access_no']).first():
                flash('Access number already exists!')
                return render_template('librarian_add_book.html', departments=DEPARTMENTS)
            
            copies = int(request.form['copies'])
            
            book = Book(
                access_no=request.form['access_no'],
                title=request.form['title'],
                author=request.form['author'],
                publisher=request.form['publisher'],
                subject=request.form['subject'],
                department=request.form['department'],
                category=request.form['category'],
                location=request.form['location'],
                copies=copies,
                quantity=copies,  # For backward compatibility
                available_count=copies
            )
            db.session.add(book)
            db.session.commit()
            flash('Book added successfully!')
            return redirect(url_for('librarian_books'))
            
        except ValueError as e:
            flash('Invalid number of copies. Please enter a valid number.')
            return render_template('librarian_add_book.html', departments=DEPARTMENTS)
        except Exception as e:
            flash(f'Error adding book: {str(e)}')
            return render_template('librarian_add_book.html', departments=DEPARTMENTS)
    
    return render_template('librarian_add_book.html', departments=DEPARTMENTS)

@app.route('/librarian/edit_book/<int:book_id>', methods=['GET', 'POST'])
def librarian_edit_book(book_id):
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))
    book = Book.query.get_or_404(book_id)
    if request.method == 'POST':
        try:
            book.title = request.form['title']
            book.publisher = request.form['publisher']
            book.subject = request.form['subject']
            book.department = request.form['department']
            book.location = request.form['location']
            book.copies = int(request.form['copies'])
            db.session.commit()
            flash('Book updated successfully!', 'success')
            return redirect(url_for('librarian_books'))
        except Exception as e:
            flash(f'Error updating book: {str(e)}', 'danger')
    return render_template('librarian_edit_book.html', book=book)

@app.route('/librarian/books/delete/<int:book_id>', methods=['POST'])
def librarian_delete_book(book_id):
    if session.get('user_role') != 'librarian':
        return jsonify({'success': False, 'message': 'Unauthorized'}), 403
    
    try:
        book = Book.query.get_or_404(book_id)
        
        # Check if book is currently issued
        active_issues = Issue.query.filter_by(book_id=book_id, return_date=None).count()
        if active_issues > 0:
            return jsonify({'success': False, 'message': 'Cannot delete book. It is currently issued to students.'}), 400
        
        db.session.delete(book)
        db.session.commit()
        return jsonify({'success': True, 'message': 'Book deleted successfully!'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'Error deleting book: {str(e)}'}), 500

@app.route('/librarian/students')
def librarian_students():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))
    
    students = Student.query.all()
    return render_template('librarian_students.html', students=students)

@app.route('/librarian/student_search')
def librarian_student_search():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))
    q = request.args.get('q', '').strip()
    students = []
    if q:
        students = Student.query.filter(
            (Student.name.ilike(f'%{q}%')) |
            (Student.user_id.ilike(f'%{q}%')) |
            (Student.email.ilike(f'%{q}%'))
        ).all()
    return render_template('librarian_students.html', students=students)

@app.route('/librarian/student/<int:student_id>')
def librarian_student_details(student_id):
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))
    student = Student.query.get_or_404(student_id)
    borrowed_books = Issue.query.filter_by(student_id=student_id, return_date=None).all()
    return render_template('librarian_student_details.html', student=student, borrowed_books=borrowed_books)

# Librarian bulk book upload
@app.route('/librarian/bulk_books', methods=['GET', 'POST'])
def librarian_bulk_books():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    
    if request.method == 'POST':
        if 'file' not in request.files:
            flash('No file selected!')
            return redirect(request.url)
        
        file = request.files['file']
        if file.filename == '':
            flash('No file selected!')
            return redirect(request.url)
        
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)
            
            try:
                df = pd.read_excel(filepath)
                required_columns = ['access_no', 'title', 'author', 'publisher', 'subject', 
                                  'department', 'category', 'location', 'copies']
                
                if not all(col in df.columns for col in required_columns):
                    flash(f'Excel file must contain columns: {", ".join(required_columns)}')
                    os.remove(filepath)
                    return redirect(request.url)
                
                created_books = []
                errors = []
                
                for index, row in df.iterrows():
                    try:
                        access_no = str(row['access_no']).strip()
                        
                        if Book.query.filter_by(access_no=access_no).first():
                            errors.append(f"Row {index + 2}: Access number {access_no} already exists")
                            continue
                        
                        copies = int(row['copies'])
                        
                        new_book = Book(
                            access_no=access_no,
                            title=str(row['title']).strip(),
                            author=str(row['author']).strip(),
                            publisher=str(row['publisher']).strip(),
                            subject=str(row['subject']).strip(),
                            department=str(row['department']).strip(),
                            category=str(row['category']).strip(),
                            location=str(row['location']).strip(),
                            copies=copies,
                            quantity=copies,
                            available_count=copies
                        )
                        
                        db.session.add(new_book)
                        created_books.append({
                            'access_no': access_no,
                            'title': str(row['title']).strip(),
                            'copies': copies
                        })
                        
                    except Exception as e:
                        errors.append(f"Row {index + 2}: {str(e)}")
                
                db.session.commit()
                os.remove(filepath)
                
                session['bulk_book_results'] = {
                    'created_books': created_books,
                    'errors': errors
                }
                
                flash(f'Bulk book creation completed! {len(created_books)} book(s) added.')
                return redirect(url_for('librarian_bulk_book_results'))
                
            except Exception as e:
                if os.path.exists(filepath):
                    os.remove(filepath)
                flash(f'Error processing file: {str(e)}')
                return redirect(request.url)
        else:
            flash('Invalid file type. Please upload an Excel file (.xlsx or .xls)')
    
    return render_template('librarian_bulk_books.html', departments=DEPARTMENTS)

@app.route('/librarian/bulk_book_results')
def librarian_bulk_book_results():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    
    results = session.get('bulk_book_results')
    if not results:
        return redirect(url_for('librarian_bulk_books'))
    
    return render_template('librarian_bulk_book_results.html', results=results)

@app.route('/librarian/download_book_template')
def librarian_download_book_template():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    
    data = {
        'access_no': ['ACC00001', 'ACC00002', 'ACC00003'],
        'title': ['Introduction to Computer Science', 'Advanced Mathematics', 'Digital Electronics'],
        'author': ['John Smith', 'Jane Doe', 'Mike Johnson'],
        'publisher': ['Tech Publications', 'Academic Press', 'Engineering Books'],
        'subject': ['Computer Science', 'Mathematics', 'Electronics'],
        'department': ['CSE', 'CSE', 'ECE'],
        'category': ['Textbook', 'Reference', 'Laboratory Manual'],
        'location': ['Section A, Shelf 1', 'Section B, Shelf 2', 'Section C, Shelf 1'],
        'copies': [5, 3, 8]
    }
    
    df = pd.DataFrame(data)
    template_filename = 'book_template.xlsx'
    template_path = os.path.join(app.config['UPLOAD_FOLDER'], template_filename)
    
    with pd.ExcelWriter(template_path, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='Book Template')
    
    return redirect(f'/uploads/{template_filename}')

@app.route('/librarian/bulk_users', methods=['GET', 'POST'])
def librarian_bulk_users():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))
    
    if request.method == 'POST':
        if 'file' not in request.files:
            flash('No file selected')
            return redirect(request.url)
        
        file = request.files['file']
        if file.filename == '':
            flash('No file selected')
            return redirect(request.url)
        
        if file and allowed_file(file.filename):
            try:
                df = pd.read_excel(file)
                required_columns = ['user_id', 'username', 'name', 'department', 'designation', 'course', 'dob', 'current_year', 'validity_date']
                
                if not all(col in df.columns for col in required_columns):
                    flash(f'Missing required columns: {required_columns}')
                    return redirect(request.url)
                
                results = {
                    'total': len(df),
                    'successful': 0,
                    'failed': 0,
                    'skipped': 0,
                    'errors': []
                }
                
                for index, row in df.iterrows():
                    try:
                        # Check if student already exists
                        existing_student = Student.query.filter_by(user_id=row['user_id']).first()
                        if existing_student:
                            results['skipped'] += 1
                            results['errors'].append(f'Row {index + 1}: Student {row["user_id"]} already exists')
                            continue
                        
                        # Parse dates
                        dob = pd.to_datetime(row['dob']).date() if pd.notna(row['dob']) else None
                        validity_date = pd.to_datetime(row['validity_date']).date() if pd.notna(row['validity_date']) else None
                        
                        # Generate password (user_id + name)
                        name = str(row['name']).strip()
                        user_id = str(row['user_id']).strip()
                        clean_name = re.sub(r'[^a-zA-Z]', '', name.lower())
                        password = f"{user_id}{clean_name}"
                        
                        new_student = Student(
                            user_id=row['user_id'],
                            username=row['username'],
                            name=name,
                            roll_number=user_id,
                            password=generate_password_hash(password),
                            department=row['department'],
                            designation=row['designation'],
                            course=row['course'],
                            dob=dob,
                            current_year=int(row['current_year']) if pd.notna(row['current_year']) else None,
                            validity_date=validity_date
                        )
                        
                        db.session.add(new_student)
                        results['successful'] += 1
                        
                    except Exception as e:
                        results['failed'] += 1
                        results['errors'].append(f'Row {index + 1}: {str(e)}')
                
                db.session.commit()
                session['bulk_user_results'] = results
                flash(f'Processed {results["total"]} records: {results["successful"]} successful, {results["failed"]} failed, {results["skipped"]} skipped')
                return redirect(url_for('librarian_bulk_user_results'))
                
            except Exception as e:
                flash(f'Error processing file: {str(e)}')
                return redirect(request.url)
        else:
            flash('Invalid file format. Please upload an Excel file.')
            return redirect(request.url)
    
    return render_template('librarian_bulk_users.html', departments=DEPARTMENTS)

@app.route('/librarian/bulk_user_results')
def librarian_bulk_user_results():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))
    
    results = session.get('bulk_user_results')
    if not results:
        return redirect(url_for('librarian_bulk_users'))
    
    return render_template('librarian_bulk_user_results.html', results=results)

@app.route('/librarian/bulk_users_template')
def librarian_bulk_users_template():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))
    
    data = {
        'user_id': ['2024001', '2024002', '2024003'],
        'username': ['johndoe', 'janesmith', 'mikejohnson'],
        'name': ['John Doe', 'Jane Smith', 'Mike Johnson'],
        'roll_number': ['21CS001', '21IT002', '21ECE003'],
        'email': ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
        'department': ['CSE', 'IT', 'ECE'],
        'college': ['Engineering College', 'Engineering College', 'Engineering College'],
        'designation': ['Student', 'Student', 'Staff'],
        'course': ['B.Tech Computer Science', 'B.Tech Information Technology', 'B.Tech Electronics'],
        'dob': ['2003-05-15', '2003-08-20', '1995-12-10'],
        'current_year': [2, 2, 3],
        'validity_date': ['2026-05-14', '2026-05-14', '2025-12-31']
    }
    
    df = pd.DataFrame(data)
    
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='Students')
    
    output.seek(0)
    
    return send_file(
        output,
        as_attachment=True,
        download_name='student_template.xlsx',
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )

@app.route('/librarian/issue_return')
def librarian_issue_return():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))

    return render_template('librarian_issue_return.html')

@app.route('/librarian/issue_return_dashboard')
def librarian_issue_return_dashboard():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))

    return render_template('librarian_issue_return_dashboard.html')

@app.route('/librarian/issue_history')
def librarian_issue_history():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))
    issues = Issue.query.order_by(Issue.issue_date.desc()).all()
    from datetime import datetime
    now = datetime.utcnow()
    overdue_count = len([i for i in issues if i.return_date is None and i.due_date < now])
    return render_template('librarian_issue_history.html', issues=issues, overdue_count=overdue_count, now=now)

# API Routes for AJAX
@app.route('/api/search_books')
def api_search_books():
    search_query = request.args.get('q', '')
    books = Book.query.filter(
        (Book.title.contains(search_query)) |
        (Book.author.contains(search_query))
    ).limit(10).all()

    result = []
    for book in books:
        result.append({
            'book_id': book.book_id,
            'title': book.title,
            'author': book.author,
            'category': book.category,
            'available_count': book.available_count
        })

    return jsonify(result)

@app.route('/api/next_access_number')
def api_next_access_number():
    """API endpoint to get the next available access number"""
    try:
        next_number = get_next_access_number()
        return jsonify({'success': True, 'next_access_number': next_number})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/next_ebook_access_number')
def api_next_ebook_access_number():
    """API endpoint to get the next available e-book access number"""
    try:
        # Get the last e-book access number
        last_ebook = EBook.query.order_by(EBook.access_no.desc()).first()

        if not last_ebook:
            return jsonify({'success': True, 'next_access_number': '1'})

        # Extract numeric part from access number
        last_access = last_ebook.access_no
        import re
        numbers = re.findall(r'\d+', last_access)

        if numbers:
            last_number = int(numbers[-1])
            return jsonify({'success': True, 'next_access_number': str(last_number + 1)})
        else:
            return jsonify({'success': True, 'next_access_number': '1'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/search_books_detailed')
def api_search_books_detailed():
    """API endpoint for detailed book search by title or access number"""
    search_query = request.args.get('q', '').strip()

    if not search_query:
        return jsonify([])

    # Search by title, author, or access number
    books = Book.query.filter(
        db.or_(
            Book.title.ilike(f'%{search_query}%'),
            Book.author.ilike(f'%{search_query}%'),
            Book.access_no.ilike(f'%{search_query}%')
        )
    ).filter(Book.available_count > 0).limit(10).all()

    result = []
    for book in books:
        result.append({
            'book_id': book.book_id,
            'title': book.title,
            'author': book.author,
            'access_no': book.access_no,
            'category': book.category,
            'available_count': book.available_count,
            'publisher': book.publisher,
            'location': book.location
        })

    return jsonify(result)

@app.route('/download_ebook_template')
def download_ebook_template():
    """Generate and download e-book Excel template"""
    try:
        # Create sample data for template
        template_data = {
            'access_no': ['E001', 'E002'],
            'title': ['Sample E-Book 1', 'Sample E-Book 2'],
            'author': ['Author Name 1', 'Author Name 2'],
            'publisher': ['Publisher 1', 'Publisher 2'],
            'subject': ['Computer Science', 'Information Technology'],
            'department': ['CSE', 'IT'],
            'category': ['Textbook', 'Reference'],
            'file_format': ['PDF', 'EPUB'],
            'file_size': ['5.2 MB', '3.8 MB'],
            'download_url': ['https://example.com/ebook1.pdf', 'https://example.com/ebook2.epub'],
            'isbn': ['978-0-123456-78-9', '978-0-987654-32-1'],
            'pages': [250, 180],
            'language': ['English', 'English'],
            'description': ['Sample description 1', 'Sample description 2']
        }

        df = pd.DataFrame(template_data)

        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='E-Books Template')

            # Get the workbook and worksheet
            workbook = writer.book
            worksheet = writer.sheets['E-Books Template']

            # Auto-adjust column widths
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width

        output.seek(0)

        return send_file(
            output,
            as_attachment=True,
            download_name='ebook_template.xlsx',
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    except Exception as e:
        flash(f'Error generating template: {str(e)}')
        return redirect(request.referrer or url_for('admin_dashboard'))

# Librarian API Routes
@app.route('/librarian/api/user_details')
def librarian_api_user_details():
    if session.get('user_role') != 'librarian':
        return jsonify({'success': False, 'message': 'Unauthorized'}), 403

    user_id = request.args.get('user_id', '').strip()
    if not user_id:
        return jsonify({'success': False, 'message': 'User ID is required'})

    # Find student by user_id
    student = Student.query.filter_by(user_id=user_id).first()
    if not student:
        return jsonify({'success': False, 'message': 'Student not found'})

    # Get borrowed books
    borrowed_issues = Issue.query.filter_by(student_id=student.id, return_date=None).all()
    borrowed_books = []
    for issue in borrowed_issues:
        borrowed_books.append({
            'issue_id': issue.issue_id,
            'title': issue.book.title,
            'access_no': issue.book.access_no,
            'due_date': issue.due_date.strftime('%Y-%m-%d')
        })

    # Get available books
    available_books = Book.query.filter(Book.available_count > 0).all()
    books_list = []
    for book in available_books:
        books_list.append({
            'book_id': book.book_id,
            'title': book.title,
            'access_no': book.access_no
        })

    # Determine book limit
    if student.designation == 'Staff':
        book_limit = int(get_setting_value('staff_book_limit', 5))
    else:
        book_limit = int(get_setting_value('student_book_limit', 3))

    return jsonify({
        'success': True,
        'user': {
            'name': student.name,
            'user_id': student.user_id,
            'designation': student.designation
        },
        'borrowed': borrowed_books,
        'books': books_list,
        'limit': book_limit
    })

@app.route('/librarian/api/issue_book', methods=['POST'])
def librarian_api_issue_book():
    if session.get('user_role') != 'librarian':
        return jsonify({'success': False, 'message': 'Unauthorized'}), 403

    user_id = request.form.get('user_id', '').strip()
    book_id = request.form.get('book_id')

    if not user_id or not book_id:
        return jsonify({'success': False, 'message': 'User ID and Book ID are required'})

    # Find student and book
    student = Student.query.filter_by(user_id=user_id).first()
    book = Book.query.get(book_id)

    if not student:
        return jsonify({'success': False, 'message': 'Student not found'})

    if not book:
        return jsonify({'success': False, 'message': 'Book not found'})

    if book.available_count <= 0:
        return jsonify({'success': False, 'message': 'Book not available'})

    # Check book limit
    active_issues_count = Issue.query.filter_by(student_id=student.id, return_date=None).count()
    if student.designation == 'Staff':
        book_limit = int(get_setting_value('staff_book_limit', 5))
    else:
        book_limit = int(get_setting_value('student_book_limit', 3))

    if active_issues_count >= book_limit:
        return jsonify({'success': False, 'message': 'Maximum book limit reached'})

    # Create new issue
    issue = Issue(
        book_id=book_id,
        student_id=student.id,
        due_date=datetime.utcnow() + timedelta(days=14)
    )

    book.available_count -= 1

    db.session.add(issue)
    db.session.commit()

    return jsonify({'success': True, 'message': 'Book issued successfully'})

@app.route('/librarian/api/return_book', methods=['POST'])
def librarian_api_return_book():
    if session.get('user_role') != 'librarian':
        return jsonify({'success': False, 'message': 'Unauthorized'}), 403

    issue_id = request.form.get('issue_id')

    if not issue_id:
        return jsonify({'success': False, 'message': 'Issue ID is required'})

    issue = Issue.query.get(issue_id)

    if not issue:
        return jsonify({'success': False, 'message': 'Issue not found'})

    if issue.return_date:
        return jsonify({'success': False, 'message': 'Book already returned'})

    # Calculate fine
    return_date = datetime.utcnow()
    fine = calculate_fine(issue.due_date, return_date)

    # Update issue record
    issue.return_date = return_date
    issue.fine = fine

    # Update book availability
    book = Book.query.get(issue.book_id)
    book.available_count += 1

    db.session.commit()

    message = 'Book returned successfully'
    if fine > 0:
        message += f' (Fine: ₹{fine:.2f})'

    return jsonify({'success': True, 'message': message})

@app.route('/api/global_search')
def api_global_search():
    """Global search API for books, e-books, and students"""
    search_query = request.args.get('q', '').strip()
    search_type = request.args.get('type', 'all')  # all, books, ebooks, students

    if not search_query or len(search_query) < 2:
        return jsonify([])

    results = []

    # Search books
    if search_type in ['all', 'books']:
        books = Book.query.filter(
            db.or_(
                Book.title.ilike(f'%{search_query}%'),
                Book.author.ilike(f'%{search_query}%'),
                Book.access_no.ilike(f'%{search_query}%'),
                Book.subject.ilike(f'%{search_query}%')
            )
        ).limit(5).all()

        for book in books:
            results.append({
                'type': 'book',
                'id': book.book_id,
                'title': book.title,
                'subtitle': f'by {book.author}',
                'access_no': book.access_no,
                'available': book.available_count > 0,
                'icon': 'fas fa-book',
                'category': 'Physical Book'
            })

    # Search e-books
    if search_type in ['all', 'ebooks']:
        ebooks = EBook.query.filter(
            db.or_(
                EBook.title.ilike(f'%{search_query}%'),
                EBook.author.ilike(f'%{search_query}%'),
                EBook.access_no.ilike(f'%{search_query}%'),
                EBook.subject.ilike(f'%{search_query}%')
            )
        ).filter_by(is_active=True).limit(5).all()

        for ebook in ebooks:
            results.append({
                'type': 'ebook',
                'id': ebook.ebook_id,
                'title': ebook.title,
                'subtitle': f'by {ebook.author} ({ebook.file_format})',
                'access_no': ebook.access_no,
                'available': True,
                'icon': 'fas fa-tablet-alt',
                'category': 'E-Book'
            })

    # Search students (admin and librarian only)
    if search_type in ['all', 'students'] and session.get('user_role') in ['admin', 'librarian']:
        students = Student.query.filter(
            db.or_(
                Student.name.ilike(f'%{search_query}%'),
                Student.user_id.ilike(f'%{search_query}%'),
                Student.email.ilike(f'%{search_query}%'),
                Student.department.ilike(f'%{search_query}%')
            )
        ).limit(5).all()

        for student in students:
            results.append({
                'type': 'student',
                'id': student.id,
                'title': student.name,
                'subtitle': f'{student.user_id} - {student.department}',
                'access_no': student.user_id,
                'available': True,
                'icon': 'fas fa-graduation-cap',
                'category': 'Student'
            })

    return jsonify(results)

# Admin bulk book upload
@app.route('/admin/bulk_books', methods=['GET', 'POST'])
def admin_bulk_books():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    
    if request.method == 'POST':
        if 'file' not in request.files:
            flash('No file selected!')
            return redirect(request.url)
        
        file = request.files['file']
        if file.filename == '':
            flash('No file selected!')
            return redirect(request.url)
        
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)
            
            try:
                df = pd.read_excel(filepath)
                required_columns = ['access_no', 'title', 'author', 'publisher', 'subject', 
                                  'department', 'category', 'location', 'copies']
                
                if not all(col in df.columns for col in required_columns):
                    flash(f'Excel file must contain columns: {", ".join(required_columns)}')
                    os.remove(filepath)
                    return redirect(request.url)
                
                created_books = []
                errors = []
                
                for index, row in df.iterrows():
                    try:
                        access_no = str(row['access_no']).strip()
                        
                        if Book.query.filter_by(access_no=access_no).first():
                            errors.append(f"Row {index + 2}: Access number {access_no} already exists")
                            continue
                        
                        copies = int(row['copies'])
                        
                        new_book = Book(
                            access_no=access_no,
                            title=str(row['title']).strip(),
                            author=str(row['author']).strip(),
                            publisher=str(row['publisher']).strip(),
                            subject=str(row['subject']).strip(),
                            department=str(row['department']).strip(),
                            category=str(row['category']).strip(),
                            location=str(row['location']).strip(),
                            copies=copies,
                            quantity=copies,
                            available_count=copies
                        )
                        
                        db.session.add(new_book)
                        created_books.append({
                            'access_no': access_no,
                            'title': str(row['title']).strip(),
                            'copies': copies
                        })
                        
                    except Exception as e:
                        errors.append(f"Row {index + 2}: {str(e)}")
                
                db.session.commit()
                os.remove(filepath)
                
                session['bulk_book_results'] = {
                    'created_books': created_books,
                    'errors': errors
                }
                
                flash(f'Bulk book creation completed! {len(created_books)} book(s) added.')
                return redirect(url_for('admin_bulk_book_results'))
                
            except Exception as e:
                if os.path.exists(filepath):
                    os.remove(filepath)
                flash(f'Error processing file: {str(e)}')
                return redirect(request.url)
        else:
            flash('Invalid file type. Please upload an Excel file (.xlsx or .xls)')
    
    return render_template('admin_bulk_books.html', departments=DEPARTMENTS)

@app.route('/admin/bulk_book_results')
def admin_bulk_book_results():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    
    results = session.get('bulk_book_results')
    if not results:
        return redirect(url_for('admin_bulk_books'))
    
    return render_template('admin_bulk_book_results.html', results=results)

@app.route('/admin/download_book_template')
def admin_download_book_template():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    
    data = {
        'access_no': ['ACC00001', 'ACC00002', 'ACC00003'],
        'title': ['Introduction to Computer Science', 'Advanced Mathematics', 'Digital Electronics'],
        'author': ['John Smith', 'Jane Doe', 'Mike Johnson'],
        'publisher': ['Tech Publications', 'Academic Press', 'Engineering Books'],
        'subject': ['Computer Science', 'Mathematics', 'Electronics'],
        'department': ['CSE', 'CSE', 'ECE'],
        'category': ['Textbook', 'Reference', 'Laboratory Manual'],
        'location': ['Section A, Shelf 1', 'Section B, Shelf 2', 'Section C, Shelf 1'],
        'copies': [5, 3, 8]
    }
    
    df = pd.DataFrame(data)
    template_filename = 'book_template.xlsx'
    template_path = os.path.join(app.config['UPLOAD_FOLDER'], template_filename)
    
    with pd.ExcelWriter(template_path, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='Book Template')
    
    return redirect(f'/uploads/{template_filename}')

@app.route('/admin/bulk_users', methods=['GET', 'POST'])
def admin_bulk_users():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    
    if request.method == 'POST':
        # Check if file was uploaded
        if 'file' not in request.files:
            flash('No file selected!')
            return redirect(request.url)
        
        file = request.files['file']
        user_type = request.form.get('user_type')
        
        if file.filename == '':
            flash('No file selected!')
            return redirect(request.url)
        
        if not user_type or user_type not in ['librarian', 'student']:
            flash('Please select a valid user type!')
            return redirect(request.url)
        
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)
            
            try:
                # Read Excel file
                df = pd.read_excel(filepath)
                
                # Validate required columns
                if user_type == 'student':
                    required_columns = ['user_id', 'username', 'name', 'email', 
                                      'department', 'designation', 'course', 'dob', 'current_year', 'validity_date']
                else:
                    required_columns = ['name', 'user_id', 'email']
                
                if not all(col in df.columns for col in required_columns):
                    flash(f'Excel file must contain columns: {", ".join(required_columns)}')
                    os.remove(filepath)
                    return redirect(request.url)
                
                created_users = []
                errors = []
                
                for index, row in df.iterrows():
                    try:
                        name = str(row['name']).strip()
                        user_id = str(row['user_id']).strip()
                        email = str(row['email']).strip() if pd.notna(row['email']) else None
                        
                        # Generate username and password
                        generated_email, password = generate_username_password(name, user_id, user_type)
                        
                        # Use provided email or generated one
                        final_email = email if email else generated_email
                        
                        # Check if user already exists
                        if user_type == 'librarian':
                            existing_user = Librarian.query.filter_by(email=final_email).first()
                        else:
                            existing_user = Student.query.filter_by(email=final_email).first() or \
                                          Student.query.filter_by(roll_number=user_id).first() or \
                                          Student.query.filter_by(user_id=str(row['user_id']).strip()).first() or \
                                          Student.query.filter_by(username=str(row['username']).strip()).first()
                        
                        if existing_user:
                            errors.append(f"Row {index + 2}: User already exists (duplicate user_id, username, email, or roll_number)")
                            continue
                        
                        # Create new user
                        if user_type == 'librarian':
                            new_user = Librarian(
                                name=name,
                                email=final_email,
                                password=generate_password_hash(password)
                            )
                        else:
                            # Parse date fields for student
                            try:
                                dob = pd.to_datetime(row['dob']).date()
                                validity_date = pd.to_datetime(row['validity_date']).date()
                                
                                # Check if validity date is in the future
                                if validity_date <= date.today():
                                    errors.append(f"Row {index + 2}: Validity date must be in the future")
                                    continue
                                
                                new_user = Student(
                                    user_id=str(row['user_id']).strip(),
                                    username=str(row['username']).strip(),
                                    name=name,
                                    roll_number=user_id,
                                    email=final_email,
                                    password=generate_password_hash(password),
                                    department=str(row['department']).strip(),
                                    college=str(row.get('college', 'Engineering College')).strip(),
                                    designation=str(row['designation']).strip(),
                                    course=str(row['course']).strip(),
                                    dob=dob,
                                    current_year=int(row['current_year']),
                                    validity_date=validity_date
                                )
                            except (ValueError, TypeError) as date_error:
                                errors.append(f"Row {index + 2}: Invalid date format or current_year - {str(date_error)}")
                                continue
                        
                        db.session.add(new_user)
                        created_users.append({
                            'name': name,
                            'email': final_email,
                            'password': password,
                            'user_id': user_id if user_type == 'student' else 'N/A'
                        })
                        
                    except Exception as e:
                        errors.append(f"Row {index + 2}: {str(e)}")
                
                # Commit all changes
                db.session.commit()
                
                # Clean up uploaded file
                os.remove(filepath)
                
                # Show results
                session['bulk_results'] = {
                    'created_users': created_users,
                    'errors': errors,
                    'user_type': user_type
                }
                
                flash(f'Bulk creation completed! {len(created_users)} {user_type}(s) created successfully.')
                return redirect(url_for('admin_bulk_results'))
                
            except Exception as e:
                # Clean up uploaded file
                if os.path.exists(filepath):
                    os.remove(filepath)
                flash(f'Error processing file: {str(e)}')
                return redirect(request.url)
        else:
            flash('Invalid file type. Please upload an Excel file (.xlsx or .xls)')
    
    return render_template('admin_bulk_users.html')

@app.route('/admin/bulk_results')
def admin_bulk_results():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    
    results = session.get('bulk_results')
    if not results:
        return redirect(url_for('admin_bulk_users'))
    
    return render_template('admin_bulk_results.html', results=results)

@app.route('/admin/download_template/<user_type>')
def admin_download_template(user_type):
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    
    if user_type not in ['librarian', 'student']:
        flash('Invalid user type!')
        return redirect(url_for('admin_bulk_users'))
    
    # Create sample data
    if user_type == 'librarian':
        data = {
            'name': ['John Doe', 'Jane Smith', 'Mike Johnson'],
            'roll_number': ['LIB001', 'LIB002', 'LIB003'],
            'email': ['<EMAIL>', '<EMAIL>', '']  # Third one will be auto-generated
        }
    else:
        data = {
            'user_id': ['STU001', 'STU002', 'STU003'],
            'username': ['alice_brown', 'bob_wilson', 'carol_davis'],
            'name': ['Alice Brown', 'Bob Wilson', 'Carol Davis'],
            'roll_number': ['21CS001', '21IT002', '21ECE003'],
            'email': ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
            'department': ['CSE', 'IT', 'ECE'],
            'college': ['Engineering College', 'Engineering College', 'Engineering College'],
            'designation': ['Student', 'Student', 'Staff'],
            'course': ['B.Tech Computer Science', 'B.Tech Information Technology', 'B.Tech Electronics'],
            'dob': ['2003-05-15', '2003-08-20', '1995-12-10'],
            'current_year': [2, 2, 3],
            'validity_date': ['2026-05-14', '2026-05-14', '2025-12-31']
        }
    
    # Create DataFrame and save to Excel
    df = pd.DataFrame(data)
    template_filename = f'{user_type}_template.xlsx'
    template_path = os.path.join(app.config['UPLOAD_FOLDER'], template_filename)
    
    with pd.ExcelWriter(template_path, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name=f'{user_type.title()} Template')
    
    return redirect(f'/uploads/{template_filename}')

@app.route('/uploads/<filename>')
def uploaded_file(filename):
    """Serve uploaded files"""
    from flask import send_from_directory
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

if __name__ == '__main__':
    init_db()
    # Start the background cleanup scheduler
    schedule_cleanup()
    print("✅ Background cleanup scheduler started")
    app.run(debug=True)
