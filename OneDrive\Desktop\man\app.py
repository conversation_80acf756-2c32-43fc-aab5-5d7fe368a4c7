# Corrected: removed invalid placeholder
# Place all route decorators and functions after app = Flask(__name__)
from flask import Flask, render_template, request, redirect, url_for, session, flash, jsonify, send_file
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import text
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
from datetime import datetime, timedelta, date
from io import BytesIO
import os
import pandas as pd
import re
import threading
import atexit

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///library.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Create uploads directory if it doesn't exist
if not os.path.exists(app.config['UPLOAD_FOLDER']):
    os.makedirs(app.config['UPLOAD_FOLDER'])

db = SQLAlchemy(app)

# Database Models
class Admin(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password = db.Column(db.String(200), nullable=False)

class Librarian(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password = db.Column(db.String(200), nullable=False)

class College(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), unique=True, nullable=False)
    code = db.Column(db.String(10), unique=True, nullable=False)
    address = db.Column(db.Text, nullable=True)
    phone = db.Column(db.String(20), nullable=True)
    email = db.Column(db.String(120), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)

    # Relationship
    departments = db.relationship('Department', backref='college', lazy=True, cascade='all, delete-orphan')

class Department(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    code = db.Column(db.String(10), nullable=False)
    college_id = db.Column(db.Integer, db.ForeignKey('college.id'), nullable=False)
    head_of_department = db.Column(db.String(100), nullable=True)
    phone = db.Column(db.String(20), nullable=True)
    email = db.Column(db.String(120), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)

    # Unique constraint for code within a college
    __table_args__ = (db.UniqueConstraint('college_id', 'code', name='unique_dept_code_per_college'),)

class Student(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.String(50), unique=True, nullable=False)
    username = db.Column(db.String(50), unique=True, nullable=True)  # Made nullable, use user_id for login
    name = db.Column(db.String(100), nullable=False)
    roll_number = db.Column(db.String(50), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password = db.Column(db.String(200), nullable=False)
    department_id = db.Column(db.Integer, db.ForeignKey('department.id'), nullable=True)  # New foreign key
    college_id = db.Column(db.Integer, db.ForeignKey('college.id'), nullable=True)  # New foreign key
    department = db.Column(db.String(10), nullable=True)  # Keep for backward compatibility
    college = db.Column(db.String(100), nullable=True)  # Keep for backward compatibility
    designation = db.Column(db.String(20), nullable=False, default='Student')  # Student or Staff
    course = db.Column(db.String(100), nullable=False)
    dob = db.Column(db.Date, nullable=False)
    current_year = db.Column(db.Integer, nullable=False)
    validity_date = db.Column(db.Date, nullable=False)
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)

class Book(db.Model):
    book_id = db.Column(db.Integer, primary_key=True)
    access_no = db.Column(db.String(50), unique=True, nullable=False)  # Access Number
    title = db.Column(db.String(200), nullable=False)
    author = db.Column(db.String(100), nullable=False)
    publisher = db.Column(db.String(150), nullable=False)
    subject = db.Column(db.String(100), nullable=False)
    department_id = db.Column(db.Integer, db.ForeignKey('department.id'), nullable=True)  # New foreign key
    college_id = db.Column(db.Integer, db.ForeignKey('college.id'), nullable=True)  # New foreign key
    department = db.Column(db.String(10), nullable=True)  # Keep for backward compatibility
    category = db.Column(db.String(100), nullable=False)
    location = db.Column(db.String(100), nullable=False)  # Location of the book
    copies = db.Column(db.Integer, nullable=False, default=1)  # Total copies
    quantity = db.Column(db.Integer, nullable=False, default=1)  # For backward compatibility
    available_count = db.Column(db.Integer, nullable=False, default=1)
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)

class EBook(db.Model):
    ebook_id = db.Column(db.Integer, primary_key=True)
    access_no = db.Column(db.String(50), unique=True, nullable=False)  # Access Number
    title = db.Column(db.String(200), nullable=False)
    author = db.Column(db.String(100), nullable=False)
    publisher = db.Column(db.String(150), nullable=False)
    subject = db.Column(db.String(100), nullable=False)
    department = db.Column(db.String(10), nullable=False)  # CSE, IT, ECE, etc.
    category = db.Column(db.String(100), nullable=False)
    file_format = db.Column(db.String(10), nullable=False)  # PDF, EPUB, etc.
    file_size = db.Column(db.String(20), nullable=True)  # File size in MB
    download_url = db.Column(db.String(500), nullable=True)  # URL or file path
    isbn = db.Column(db.String(20), nullable=True)  # ISBN number
    pages = db.Column(db.Integer, nullable=True)  # Number of pages
    language = db.Column(db.String(50), nullable=False, default='English')
    description = db.Column(db.Text, nullable=True)  # Book description
    is_active = db.Column(db.Boolean, nullable=False, default=True)  # Active status
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)

class Issue(db.Model):
    issue_id = db.Column(db.Integer, primary_key=True)
    book_id = db.Column(db.Integer, db.ForeignKey('book.book_id'), nullable=False)
    student_id = db.Column(db.Integer, db.ForeignKey('student.id'), nullable=False)
    issue_date = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    due_date = db.Column(db.DateTime, nullable=False)
    return_date = db.Column(db.DateTime, nullable=True)
    fine = db.Column(db.Float, default=0.0)

    book = db.relationship('Book', backref=db.backref('issues', lazy=True))
    student = db.relationship('Student', backref=db.backref('issues', lazy=True))

class NewsClipping(db.Model):
    clipping_id = db.Column(db.Integer, primary_key=True)
    clipping_no = db.Column(db.String(50), nullable=False, unique=True)
    newspaper_name = db.Column(db.String(200), nullable=False)
    news_type = db.Column(db.String(100), nullable=False)
    date = db.Column(db.Date, nullable=False)
    pages = db.Column(db.String(50), nullable=False)  # e.g., "1-3", "5", "7-9"
    keywords = db.Column(db.Text, nullable=False)
    abstract = db.Column(db.Text, nullable=False)
    content = db.Column(db.Text, nullable=False)
    college_id = db.Column(db.Integer, db.ForeignKey('college.id'), nullable=False)
    department_id = db.Column(db.Integer, db.ForeignKey('department.id'), nullable=False)
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    created_by = db.Column(db.String(50), nullable=False)  # Admin who created it
    is_active = db.Column(db.Boolean, nullable=False, default=True)

    # Relationships
    college = db.relationship('College', backref=db.backref('news_clippings', lazy=True))
    department = db.relationship('Department', backref=db.backref('news_clippings', lazy=True))

class LibrarySettings(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    setting_name = db.Column(db.String(50), unique=True, nullable=False)
    setting_value = db.Column(db.String(200), nullable=False)
    description = db.Column(db.String(500))
    updated_by = db.Column(db.String(50))
    updated_at = db.Column(db.DateTime, default=datetime.utcnow)

# Enhanced Circulation Models
class Reservation(db.Model):
    reservation_id = db.Column(db.Integer, primary_key=True)
    book_id = db.Column(db.Integer, db.ForeignKey('book.book_id'), nullable=False)
    student_id = db.Column(db.Integer, db.ForeignKey('student.id'), nullable=False)
    reservation_date = db.Column(db.DateTime, default=datetime.utcnow)
    status = db.Column(db.String(20), default='Active')  # Active, Fulfilled, Cancelled
    notification_sent = db.Column(db.Boolean, default=False)
    expiry_date = db.Column(db.DateTime)
    priority = db.Column(db.Integer, default=1)  # Queue position

    book = db.relationship('Book', backref='reservations')
    student = db.relationship('Student', backref='reservations')

class Payment(db.Model):
    payment_id = db.Column(db.Integer, primary_key=True)
    student_id = db.Column(db.Integer, db.ForeignKey('student.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    payment_type = db.Column(db.String(50), nullable=False)  # Fine, Fee, Damage, Lost
    payment_method = db.Column(db.String(50), default='Cash')  # Cash, Online, Card
    description = db.Column(db.String(200))
    payment_date = db.Column(db.DateTime, default=datetime.utcnow)
    processed_by = db.Column(db.String(100))  # Staff member who processed
    receipt_number = db.Column(db.String(50), unique=True)
    issue_id = db.Column(db.Integer, db.ForeignKey('issue.issue_id'), nullable=True)  # Related issue if applicable

    student = db.relationship('Student', backref='payments')
    issue = db.relationship('Issue', backref='payments')

class BindingRecord(db.Model):
    binding_id = db.Column(db.Integer, primary_key=True)
    book_id = db.Column(db.Integer, db.ForeignKey('book.book_id'), nullable=False)
    vendor_name = db.Column(db.String(200), nullable=False)
    vendor_contact = db.Column(db.String(100))
    date_sent = db.Column(db.DateTime, default=datetime.utcnow)
    expected_return = db.Column(db.DateTime)
    actual_return = db.Column(db.DateTime)
    status = db.Column(db.String(50), default='Sent')  # Sent, Returned, Lost
    cost = db.Column(db.Float)
    notes = db.Column(db.Text)
    processed_by = db.Column(db.String(100))

    book = db.relationship('Book', backref='binding_records')

# Helper Functions
def migrate_database():
    """Migrate database to add new columns to existing Student and Book tables"""
    with app.app_context():
        try:
            # Migrate Student table
            try:
                db.session.execute(text('SELECT user_id FROM student LIMIT 1'))
                print("✅ Student table already has new columns")
            except:
                print("🔄 Adding new columns to Student table...")
                
                # Add missing columns to existing Student table
                db.session.execute(text('ALTER TABLE student ADD COLUMN user_id VARCHAR(50)'))
                db.session.execute(text('ALTER TABLE student ADD COLUMN username VARCHAR(50)'))
                db.session.execute(text('ALTER TABLE student ADD COLUMN department VARCHAR(10)'))
                db.session.execute(text('ALTER TABLE student ADD COLUMN college VARCHAR(100) DEFAULT "Engineering College"'))
                db.session.execute(text('ALTER TABLE student ADD COLUMN designation VARCHAR(20) DEFAULT "Student"'))
                db.session.execute(text('ALTER TABLE student ADD COLUMN course VARCHAR(100)'))
                db.session.execute(text('ALTER TABLE student ADD COLUMN dob DATE'))
                db.session.execute(text('ALTER TABLE student ADD COLUMN current_year INTEGER'))
                db.session.execute(text('ALTER TABLE student ADD COLUMN validity_date DATE'))
                db.session.execute(text('ALTER TABLE student ADD COLUMN created_at DATETIME DEFAULT CURRENT_TIMESTAMP'))
                
                db.session.commit()
                
                # Update existing student records with default values
                from datetime import date, timedelta
                default_validity = date.today() + timedelta(days=365)  # 1 year from now
                
                students = Student.query.all()
                for student in students:
                    if not student.user_id:
                        student.user_id = f"STU{student.id:03d}"
                    if not student.username:
                        student.username = student.email.split('@')[0] if student.email else f"student{student.id}"
                    if not student.department:
                        student.department = 'CSE'
                    if not student.college:
                        student.college = 'Engineering College'
                    if not student.designation:
                        student.designation = 'Student'
                    if not student.course:
                        student.course = 'B.Tech Computer Science'
                    if not student.dob:
                        student.dob = date(2000, 1, 1)  # Default DOB
                    if not student.current_year:
                        student.current_year = 2
                    if not student.validity_date:
                        student.validity_date = default_validity
                
                db.session.commit()
                print("✅ Student table migration completed")

            # Migrate Book table
            try:
                db.session.execute(text('SELECT access_no FROM book LIMIT 1'))
                print("✅ Book table already has new columns")
            except:
                print("🔄 Adding new columns to Book table...")
                
                # Add missing columns to existing Book table
                db.session.execute(text('ALTER TABLE book ADD COLUMN access_no VARCHAR(50)'))
                db.session.execute(text('ALTER TABLE book ADD COLUMN publisher VARCHAR(150)'))
                db.session.execute(text('ALTER TABLE book ADD COLUMN subject VARCHAR(100)'))
                db.session.execute(text('ALTER TABLE book ADD COLUMN department VARCHAR(10)'))
                db.session.execute(text('ALTER TABLE book ADD COLUMN location VARCHAR(100)'))
                db.session.execute(text('ALTER TABLE book ADD COLUMN copies INTEGER DEFAULT 1'))
                db.session.execute(text('ALTER TABLE book ADD COLUMN created_at DATETIME DEFAULT CURRENT_TIMESTAMP'))
                
                db.session.commit()
                
                # Update existing book records with default values
                books = Book.query.all()
                for book in books:
                    if not book.access_no:
                        book.access_no = f"ACC{book.book_id:05d}"
                    if not book.publisher:
                        book.publisher = 'Unknown Publisher'
                    if not book.subject:
                        book.subject = book.category  # Use category as subject initially
                    if not book.department:
                        book.department = 'CSE'
                    if not book.location:
                        book.location = 'Section A, Shelf 1'
                    if not book.copies:
                        book.copies = book.quantity
                
                db.session.commit()
                print("✅ Book table migration completed")
                
            # Initialize Library Settings
            try:
                existing_settings = LibrarySettings.query.first()
                if not existing_settings:
                    print("🔄 Initializing library settings...")
                    
                    default_settings = [
                        {
                            'setting_name': 'student_book_limit',
                            'setting_value': '3',
                            'description': 'Maximum number of books a student can issue at once',
                            'updated_by': 'system'
                        },
                        {
                            'setting_name': 'staff_book_limit',
                            'setting_value': '5',
                            'description': 'Maximum number of books a staff member can issue at once',
                            'updated_by': 'system'
                        },
                        {
                            'setting_name': 'default_issue_days',
                            'setting_value': '14',
                            'description': 'Default number of days for book issue period',
                            'updated_by': 'system'
                        },
                        {
                            'setting_name': 'fine_per_day',
                            'setting_value': '2.0',
                            'description': 'Fine amount per day for overdue books (in rupees)',
                            'updated_by': 'system'
                        },
                        {
                            'setting_name': 'renewal_limit',
                            'setting_value': '2',
                            'description': 'Maximum number of times a book can be renewed',
                            'updated_by': 'system'
                        }
                    ]
                    
                    for setting_data in default_settings:
                        setting = LibrarySettings(**setting_data)
                        db.session.add(setting)
                    
                    db.session.commit()
                    print("✅ Library settings initialized")
                else:
                    print("✅ Library settings already exist")
                    
            except Exception as e:
                print(f"Settings initialization error: {str(e)}")
                db.session.rollback()
                
        except Exception as e:
            print(f"Migration error: {str(e)}")
            db.session.rollback()

def init_db():
    with app.app_context():
        # Clean all existing data
        print("🧹 Cleaning database...")
        db.drop_all()
        db.create_all()

        # Create default admin only
        admin = Admin(
            name='System Administrator',
            email='<EMAIL>',
            password=generate_password_hash('admin123')
        )
        db.session.add(admin)

        # Create default librarian
        librarian = Librarian(
            name='Head Librarian',
            email='<EMAIL>',
            password=generate_password_hash('librarian123')
        )
        db.session.add(librarian)

        db.session.commit()
        print("✅ Database cleaned and initialized")
        print("👤 Admin: <EMAIL> / admin123")
        print("📚 Librarian: <EMAIL> / librarian123")

def calculate_fine(due_date, return_date=None):
    if return_date is None:
        return_date = datetime.utcnow()

    if return_date > due_date:
        days_overdue = (return_date - due_date).days
        return days_overdue * 2.0  # $2 per day fine
    return 0.0

def get_next_access_number():
    """Get the next available access number"""
    # Get the last access number from the database
    last_book = Book.query.order_by(Book.access_no.desc()).first()

    if not last_book:
        return "1"

    # Extract numeric part from access number
    last_access = last_book.access_no

    # Try to extract number from various formats
    import re
    numbers = re.findall(r'\d+', last_access)

    if numbers:
        # Get the last (usually largest) number found
        last_number = int(numbers[-1])
        return str(last_number + 1)
    else:
        # If no numbers found, start from 1
        return "1"

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in {'xlsx', 'xls'}

def generate_username_password(name, user_id, user_type):
    """Generate username and password based on name and user_id"""
    # Clean name: remove spaces, special characters, convert to lowercase
    clean_name = re.sub(r'[^a-zA-Z]', '', name.lower())
    
    # Generate username: first part of name + user_id
    username = f"{clean_name[:6]}{user_id}@{user_type}.library.com"
    
    # Generate password: user_id + name (as specified)
    password = f"{user_id}{clean_name}"
    
    return username, password

def cleanup_expired_users():
    """Remove users whose validity date has expired"""
    with app.app_context():
        try:
            today = date.today()
            expired_students = Student.query.filter(Student.validity_date < today).all()
            
            for student in expired_students:
                # First, handle any pending book issues
                pending_issues = Issue.query.filter_by(student_id=student.id, return_date=None).all()
                for issue in pending_issues:
                    # Mark books as returned and calculate final fine
                    issue.return_date = datetime.utcnow()
                    issue.fine = calculate_fine(issue.due_date, issue.return_date)
                    # Return book to available stock
                    book = Book.query.get(issue.book_id)
                    if book:
                        book.available_count += 1
                
                # Delete the expired student
                db.session.delete(student)
                print(f"Deleted expired user: {student.name} (ID: {student.user_id})")
            
            if expired_students:
                db.session.commit()
                print(f"Cleanup completed: {len(expired_students)} expired users removed")
            
        except Exception as e:
            print(f"Error during cleanup: {str(e)}")
            db.session.rollback()

def schedule_cleanup():
    """Schedule daily cleanup of expired users"""
    cleanup_expired_users()
    # Schedule next cleanup in 24 hours
    timer = threading.Timer(86400.0, schedule_cleanup)  # 86400 seconds = 24 hours
    timer.daemon = True
    timer.start()

# Helper functions for dynamic data
def get_colleges():
    """Get all active colleges"""
    return College.query.filter_by(is_active=True).all()

def get_departments(college_id=None):
    """Get all active departments, optionally filtered by college"""
    if college_id:
        return Department.query.filter_by(college_id=college_id, is_active=True).all()
    return Department.query.filter_by(is_active=True).all()

def get_departments_for_dropdown(college_id=None):
    """Get departments formatted for dropdown (code, name)"""
    departments = get_departments(college_id)
    return [(dept.code, dept.name) for dept in departments]

def get_colleges_for_dropdown():
    """Get colleges formatted for dropdown (id, name)"""
    colleges = get_colleges()
    return [(college.id, college.name) for college in colleges]

DESIGNATION_CHOICES = [
    ('Student', 'Student'),
    ('Staff', 'Staff')
]

# Helper function to get setting values
def get_setting_value(setting_name, default_value=None):
    """Get a setting value from the database"""
    try:
        setting = LibrarySettings.query.filter_by(setting_name=setting_name).first()
        return setting.setting_value if setting else default_value
    except:
        return default_value

def update_setting_value(setting_name, new_value, updated_by='admin'):
    """Update a setting value in the database"""
    try:
        setting = LibrarySettings.query.filter_by(setting_name=setting_name).first()
        if setting:
            setting.setting_value = new_value
            setting.updated_by = updated_by
            setting.updated_at = datetime.utcnow()
        else:
            setting = LibrarySettings(
                setting_name=setting_name,
                setting_value=new_value,
                updated_by=updated_by
            )
            db.session.add(setting)
        
        db.session.commit()
        return True
    except Exception as e:
        db.session.rollback()
        return False

# Routes
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']

        user = None
        user_role = None

        # Auto-detect user role by checking all user types
        # Check Admin first (Admin only has email, no username)
        admin = Admin.query.filter_by(email=username).first()
        if admin and check_password_hash(admin.password, password):
            user = admin
            user_role = 'admin'

        # Check Librarian if not admin (Librarian only has email, no username)
        if not user:
            librarian = Librarian.query.filter_by(email=username).first()
            if librarian and check_password_hash(librarian.password, password):
                user = librarian
                user_role = 'librarian'

        # Check Student if not admin or librarian
        if not user:
            student = Student.query.filter(
                db.or_(
                    Student.email == username,
                    Student.username == username,
                    Student.user_id == username
                )
            ).first()

            if student and check_password_hash(student.password, password):
                # Check if student account is still valid
                if student.validity_date < date.today():
                    flash('Your account has expired. Please contact the administrator.')
                    return render_template('index.html')
                user = student
                user_role = 'student'

        if user and user_role:
            session['user_id'] = user.id
            session['user_role'] = user_role
            session['user_name'] = user.name
            flash(f'Welcome, {user.name}!')
            return redirect(url_for(f'{user_role}_dashboard'))
        else:
            flash('Invalid username or password')

    return render_template('index.html')

# Keep the old route for backward compatibility
@app.route('/login/<role>', methods=['GET', 'POST'])
def login_role(role):
    return redirect(url_for('login'))

@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('index'))

# Admin Routes
@app.route('/admin/dashboard')
def admin_dashboard():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))

    # Get real statistics from database (no dummy data)
    total_books = Book.query.count()
    total_ebooks = EBook.query.filter_by(is_active=True).count()
    total_students = Student.query.count()
    total_librarians = Librarian.query.count()
    issued_books = Issue.query.filter_by(return_date=None).count()

    # Calculate overdue books using timezone-aware datetime
    from datetime import datetime, timezone
    now = datetime.now(timezone.utc)
    overdue_books = Issue.query.filter(
        Issue.return_date == None,
        Issue.due_date < now
    ).count()

    # Calculate available books
    available_books = total_books - issued_books if total_books > 0 else 0

    # Calculate total fines collected (real data only)
    total_fines = db.session.query(db.func.sum(Issue.fine)).filter(
        Issue.fine > 0,
        Issue.return_date.isnot(None)
    ).scalar() or 0

    stats = {
        'total_books': total_books,
        'total_ebooks': total_ebooks,
        'total_students': total_students,
        'total_librarians': total_librarians,
        'issued_books': issued_books,
        'overdue_books': overdue_books,
        'available_books': available_books,
        'total_fines': round(total_fines, 2)
    }

    return render_template('admin_dashboard.html', stats=stats)

@app.route('/admin/settings', methods=['GET', 'POST'])
def admin_settings():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))
    
    if request.method == 'POST':
        # Update settings
        student_book_limit = request.form.get('student_book_limit')
        staff_book_limit = request.form.get('staff_book_limit')
        default_issue_days = request.form.get('default_issue_days')
        fine_per_day = request.form.get('fine_per_day')
        renewal_limit = request.form.get('renewal_limit')
        
        try:
            # Update each setting
            if student_book_limit:
                update_setting_value('student_book_limit', int(student_book_limit), session.get('username'))
            if staff_book_limit:
                update_setting_value('staff_book_limit', int(staff_book_limit), session.get('username'))
            if default_issue_days:
                update_setting_value('default_issue_days', int(default_issue_days), session.get('username'))
            if fine_per_day:
                update_setting_value('fine_per_day', float(fine_per_day), session.get('username'))
            if renewal_limit:
                update_setting_value('renewal_limit', int(renewal_limit), session.get('username'))
            
            flash('Settings updated successfully!', 'success')
        except ValueError as e:
            flash(f'Error updating settings: Invalid input values', 'danger')
        except Exception as e:
            flash(f'Error updating settings: {str(e)}', 'danger')
        
        return redirect(url_for('admin_settings'))
    
    # Get current settings
    settings = {
        'student_book_limit': get_setting_value('student_book_limit', 3),
        'staff_book_limit': get_setting_value('staff_book_limit', 5),
        'default_issue_days': get_setting_value('default_issue_days', 14),
        'fine_per_day': get_setting_value('fine_per_day', 2.0),
        'renewal_limit': get_setting_value('renewal_limit', 2)
    }
    
    return render_template('admin_settings.html', settings=settings)

@app.route('/admin/news-clippings')
def admin_news_clippings():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_news_clippings.html',
                         colleges=get_colleges_for_dropdown(),
                         departments=get_departments_for_dropdown())

@app.route('/admin/news-clippings/add', methods=['GET', 'POST'])
def admin_add_news_clipping():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))

    if request.method == 'POST':
        try:
            # Get form data
            clipping_no = request.form.get('clipping_no')
            newspaper_name = request.form.get('newspaper_name')
            news_type = request.form.get('news_type')
            date_str = request.form.get('date')
            pages = request.form.get('pages')
            keywords = request.form.get('keywords')
            abstract = request.form.get('abstract')
            content = request.form.get('content')
            college_id = request.form.get('college_id')
            department_id = request.form.get('department_id')

            # Validate required fields
            if not all([clipping_no, newspaper_name, news_type, date_str, pages, keywords, abstract, content, college_id, department_id]):
                flash('All fields are required!', 'error')
                return redirect(url_for('admin_add_news_clipping'))

            # Check if clipping number already exists
            existing_clipping = NewsClipping.query.filter_by(clipping_no=clipping_no).first()
            if existing_clipping:
                flash('News clipping number already exists!', 'error')
                return redirect(url_for('admin_add_news_clipping'))

            # Parse date
            from datetime import datetime
            date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()

            # Create new news clipping
            news_clipping = NewsClipping(
                clipping_no=clipping_no,
                newspaper_name=newspaper_name,
                news_type=news_type,
                date=date_obj,
                pages=pages,
                keywords=keywords,
                abstract=abstract,
                content=content,
                college_id=int(college_id),
                department_id=int(department_id),
                created_by=session.get('user_id', 'admin')
            )

            db.session.add(news_clipping)
            db.session.commit()

            flash('News clipping added successfully!', 'success')
            return redirect(url_for('admin_news_clippings'))

        except Exception as e:
            db.session.rollback()
            flash(f'Error adding news clipping: {str(e)}', 'error')
            return redirect(url_for('admin_add_news_clipping'))

    return render_template('admin_add_news_clipping.html',
                         colleges=get_colleges_for_dropdown(),
                         departments=get_departments_for_dropdown())

@app.route('/api/admin/news-clippings')
def admin_news_clippings_api():
    if session.get('user_role') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    try:
        # Get query parameters for filtering
        college_id = request.args.get('college_id')
        department_id = request.args.get('department_id')
        news_type = request.args.get('news_type')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        # Build query
        query = NewsClipping.query.filter_by(is_active=True)

        if college_id:
            query = query.filter_by(college_id=college_id)
        if department_id:
            query = query.filter_by(department_id=department_id)
        if news_type:
            query = query.filter_by(news_type=news_type)
        if start_date:
            from datetime import datetime
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            query = query.filter(NewsClipping.date >= start_date_obj)
        if end_date:
            from datetime import datetime
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            query = query.filter(NewsClipping.date <= end_date_obj)

        # Get results
        clippings = query.order_by(NewsClipping.date.desc()).all()

        clippings_data = []
        for clipping in clippings:
            clippings_data.append({
                'clipping_id': clipping.clipping_id,
                'clipping_no': clipping.clipping_no,
                'newspaper_name': clipping.newspaper_name,
                'news_type': clipping.news_type,
                'date': clipping.date.strftime('%Y-%m-%d'),
                'pages': clipping.pages,
                'keywords': clipping.keywords,
                'abstract': clipping.abstract[:200] + '...' if len(clipping.abstract) > 200 else clipping.abstract,
                'college_name': clipping.college.name,
                'department_name': clipping.department.name,
                'created_by': clipping.created_by,
                'created_at': clipping.created_at.strftime('%Y-%m-%d %H:%M')
            })

        return jsonify({
            'clippings': clippings_data,
            'total_count': len(clippings_data)
        })

    except Exception as e:
        return jsonify({
            'clippings': [],
            'total_count': 0,
            'error': str(e)
        })

@app.route('/admin/books')
def admin_books():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))

    books = Book.query.all()
    return render_template('admin_books.html', books=books)

@app.route('/admin/add_book', methods=['GET', 'POST'])
def admin_add_book():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))
    
    if request.method == 'POST':
        try:
            # Check if access number already exists
            if Book.query.filter_by(access_no=request.form['access_no']).first():
                flash('Access number already exists!')
                return render_template('admin_add_book.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())
            
            copies = int(request.form['copies'])

            # Get college and department information
            college_id = request.form.get('college')
            department_code = request.form.get('department')

            # Find department by code and college
            department = None
            if college_id and department_code:
                department = Department.query.filter_by(
                    college_id=college_id,
                    code=department_code,
                    is_active=True
                ).first()

            book = Book(
                access_no=request.form['access_no'],
                title=request.form['title'],
                author=request.form['author'],
                publisher=request.form['publisher'],
                subject=request.form['subject'],
                college_id=int(college_id) if college_id else None,
                department_id=department.id if department else None,
                department=department_code,  # Keep for backward compatibility
                category=request.form['category'],
                location=request.form['location'],
                copies=copies,
                quantity=copies,  # For backward compatibility
                available_count=copies
            )
            db.session.add(book)
            db.session.commit()
            flash('Book added successfully!')
            return redirect(url_for('admin_books'))
            
        except ValueError as e:
            flash('Invalid number of copies. Please enter a valid number.')
            return render_template('admin_add_book.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())
        except Exception as e:
            flash(f'Error adding book: {str(e)}')
            return render_template('admin_add_book.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())

    return render_template('admin_add_book.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())

@app.route('/admin/edit_book/<int:book_id>', methods=['GET', 'POST'])
def admin_edit_book(book_id):
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))
    
    book = Book.query.get_or_404(book_id)
    
    if request.method == 'POST':
        book.title = request.form['title']
        book.author = request.form['author']
        book.category = request.form['category']
        old_quantity = book.quantity
        new_quantity = int(request.form['quantity'])
        
        # Update available count based on quantity change
        difference = new_quantity - old_quantity
        book.quantity = new_quantity
        book.available_count = max(0, book.available_count + difference)
        
        db.session.commit()
        flash('Book updated successfully!')
        return redirect(url_for('admin_books'))
    
    return render_template('admin_edit_book.html', book=book)

@app.route('/admin/delete_book/<int:book_id>')
def admin_delete_book(book_id):
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))
    
    book = Book.query.get_or_404(book_id)
    
    # Check if book is currently issued
    active_issues = Issue.query.filter_by(book_id=book_id, return_date=None).count()
    if active_issues > 0:
        flash('Cannot delete book. It is currently issued to students.')
        return redirect(url_for('admin_books'))
    
    db.session.delete(book)
    db.session.commit()
    flash('Book deleted successfully!')
    return redirect(url_for('admin_books'))

@app.route('/admin/librarians')
def admin_librarians():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))
    
    librarians = Librarian.query.all()
    return render_template('admin_librarians.html', librarians=librarians)

@app.route('/admin/add_librarian', methods=['GET', 'POST'])
def admin_add_librarian():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))
    
    if request.method == 'POST':
        # Check if email already exists
        if Librarian.query.filter_by(email=request.form['email']).first():
            flash('Email already exists!')
            return render_template('admin_add_librarian.html')
        
        librarian = Librarian(
            name=request.form['name'],
            email=request.form['email'],
            password=generate_password_hash(request.form['password'])
        )
        db.session.add(librarian)
        db.session.commit()
        flash('Librarian added successfully!')
        return redirect(url_for('admin_librarians'))
    
    return render_template('admin_add_librarian.html')

@app.route('/admin/students')
def admin_students():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))
    
    students = Student.query.all()
    return render_template('admin_students.html', students=students)

@app.route('/admin/student_details/<int:student_id>')
def admin_student_details(student_id):
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))
    
    student = Student.query.get_or_404(student_id)
    active_issues = Issue.query.filter_by(student_id=student.id, return_date=None).all()
    issue_history = Issue.query.filter_by(student_id=student.id).order_by(Issue.issue_date.desc()).all()
    total_fine = sum(issue.fine for issue in issue_history if issue.fine)
    
    from datetime import datetime
    overdue_issues = [issue for issue in active_issues if issue.due_date and issue.due_date < datetime.utcnow()]
    
    return render_template('admin_student_details.html', 
                         student=student, 
                         active_issues=active_issues,
                         issue_history=issue_history,
                         total_fine=total_fine,
                         overdue_count=len(overdue_issues))

@app.route('/admin/add_student', methods=['GET', 'POST'])
def admin_add_student():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))
    
    if request.method == 'POST':
        try:
            # Check if user_id, username, email already exists
            if Student.query.filter_by(user_id=request.form['user_id']).first():
                flash('User ID already exists!', 'danger')
                return render_template('admin_add_student.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown(), designations=DESIGNATION_CHOICES)

            # Username check removed - using user_id for login

            if Student.query.filter_by(email=request.form['email']).first():
                flash('Email already exists!', 'danger')
                return render_template('admin_add_student.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown(), designations=DESIGNATION_CHOICES)
            
            # Parse validity date
            validity_date = None
            if request.form.get('validity_date'):
                from datetime import datetime
                validity_date = datetime.strptime(request.form['validity_date'], '%Y-%m-%d').date()
            
            # Parse date of birth
            dob = None
            if request.form.get('dob'):
                dob = datetime.strptime(request.form['dob'], '%Y-%m-%d').date()
            
            # Generate password: user_id + name
            password = request.form['user_id'] + request.form['name'].replace(' ', '').lower()
            hashed_password = generate_password_hash(password)
            
            # Get college and department information
            college_id = request.form.get('college')
            department_code = request.form.get('department')

            # Find department by code and college
            department = None
            if college_id and department_code:
                department = Department.query.filter_by(
                    college_id=college_id,
                    code=department_code,
                    is_active=True
                ).first()

            # Create new student (user_id serves as roll_number for students, staff_id for staff)
            student = Student(
                user_id=request.form['user_id'],
                name=request.form['name'],
                username=None,  # No longer using username - user_id is used for login
                email=request.form['email'],
                password=hashed_password,
                roll_number=request.form['user_id'],  # Use user_id as roll_number
                college_id=int(college_id) if college_id else None,
                department_id=department.id if department else None,
                department=department_code,  # Keep for backward compatibility
                college=College.query.get(college_id).name if college_id else None,  # Keep for backward compatibility
                designation=request.form['designation'],
                course=request.form.get('course'),
                current_year=request.form.get('current_year'),
                dob=dob,
                validity_date=validity_date
            )
            
            db.session.add(student)
            db.session.commit()
            
            flash(f'Student added successfully! Password: {password}', 'success')
            return redirect(url_for('admin_students'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Error adding student: {str(e)}', 'danger')
            
    return render_template('admin_add_student.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown(), designations=DESIGNATION_CHOICES)

@app.route('/admin/issue_history')
def admin_issue_history():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))

    issues = Issue.query.order_by(Issue.issue_date.desc()).all()

    from datetime import date
    overdue_count = len([i for i in issues if i.return_date is None and i.due_date < date.today()])

    return render_template('admin_issue_history.html', issues=issues, overdue_count=overdue_count)

@app.route('/admin/circulation')
def admin_circulation():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))

    # Get circulation statistics
    from datetime import datetime, timedelta

    # Current circulation data
    total_issued = Issue.query.filter_by(return_date=None).count()
    total_overdue = Issue.query.filter(
        Issue.return_date == None,
        Issue.due_date < datetime.utcnow()
    ).count()

    # Recent activity (last 7 days)
    week_ago = datetime.utcnow() - timedelta(days=7)
    recent_issues = Issue.query.filter(Issue.issue_date >= week_ago).count()
    recent_returns = Issue.query.filter(
        Issue.return_date >= week_ago,
        Issue.return_date != None
    ).count()

    # Most popular books (by issue count)
    from sqlalchemy import func
    popular_books = db.session.query(
        Book.title,
        Book.author,
        Book.access_no,
        func.count(Issue.issue_id).label('issue_count')
    ).join(Issue).group_by(Book.book_id).order_by(func.count(Issue.issue_id).desc()).limit(10).all()

    # Active students with most books
    active_students = db.session.query(
        Student.name,
        Student.user_id,
        Student.department,
        func.count(Issue.issue_id).label('active_books')
    ).join(Issue).filter(Issue.return_date == None).group_by(Student.id).order_by(func.count(Issue.issue_id).desc()).limit(10).all()

    # Department-wise circulation
    dept_circulation = db.session.query(
        Student.department,
        func.count(Issue.issue_id).label('total_issues')
    ).join(Issue).filter(Issue.return_date == None).group_by(Student.department).all()

    circulation_data = {
        'total_issued': total_issued,
        'total_overdue': total_overdue,
        'recent_issues': recent_issues,
        'recent_returns': recent_returns,
        'popular_books': popular_books,
        'active_students': active_students,
        'dept_circulation': dept_circulation
    }

    return render_template('admin_circulation.html', data=circulation_data)

# Enhanced Circulation Routes
@app.route('/admin/circulation/counter')
def admin_circulation_counter():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_circulation_counter.html')

@app.route('/librarian/circulation/counter')
def librarian_circulation_counter():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    return render_template('librarian_circulation_counter.html')

@app.route('/admin/circulation/bulk')
def admin_bulk_operations():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_bulk_operations.html')

@app.route('/librarian/circulation/bulk')
def librarian_bulk_operations():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    return render_template('librarian_bulk_operations.html')

@app.route('/admin/circulation/payments')
def admin_payment_management():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_payment_management.html')

@app.route('/api/admin/outstanding-fines')
def admin_outstanding_fines():
    if session.get('user_role') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    # Get overdue issues to calculate fines
    from datetime import date, timedelta
    today = date.today()

    # Get all overdue issues (assuming 14 days is the standard loan period)
    overdue_issues = Issue.query.filter(
        Issue.return_date.is_(None),  # Not returned yet
        Issue.issue_date < (today - timedelta(days=14))  # Overdue
    ).join(Student).join(Book).all()

    outstanding_fines = []
    for issue in overdue_issues:
        days_overdue = (today - issue.issue_date).days - 14  # 14 days grace period
        if days_overdue > 0:
            fine_amount = days_overdue * 2.0  # ₹2 per day fine
            outstanding_fines.append({
                'student_name': issue.student.name,
                'user_id': issue.student.user_id,
                'department': issue.student.department or 'N/A',
                'book_title': issue.book.title,
                'issue_date': issue.issue_date.strftime('%Y-%m-%d'),
                'days_overdue': days_overdue,
                'fine_amount': fine_amount,
                'issue_id': issue.issue_id
            })

    return jsonify({
        'fines': outstanding_fines,
        'total_amount': sum(f['fine_amount'] for f in outstanding_fines),
        'total_count': len(outstanding_fines)
    })

@app.route('/api/admin/payment-history')
def admin_payment_history():
    if session.get('user_role') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    try:
        # Get query parameters
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        payment_type = request.args.get('payment_type')
        payment_method = request.args.get('payment_method')

        # For now, return simulated data since we don't have a payments table
        # In a real implementation, you would query the payments table
        from datetime import datetime, timedelta
        import random

        payments = []

        # Generate some sample payment records based on actual issues
        issues = Issue.query.filter(Issue.return_date.is_not(None)).limit(20).all()

        for i, issue in enumerate(issues):
            # Simulate some payments for returned books with fines
            if random.choice([True, False]):  # 50% chance of having a fine payment
                payment_date = issue.return_date or datetime.now().date()

                # Apply date filter if provided
                if start_date and payment_date < datetime.strptime(start_date, '%Y-%m-%d').date():
                    continue
                if end_date and payment_date > datetime.strptime(end_date, '%Y-%m-%d').date():
                    continue

                payment_types = ['fine', 'deposit', 'membership']
                payment_methods = ['cash', 'card', 'upi', 'online']

                p_type = random.choice(payment_types)
                p_method = random.choice(payment_methods)

                # Apply filters
                if payment_type and p_type != payment_type:
                    continue
                if payment_method and p_method != payment_method:
                    continue

                fine_amount = random.randint(10, 100)

                payments.append({
                    'receipt_no': f'RCP{str(i+1).zfill(3)}',
                    'payment_date': payment_date.strftime('%Y-%m-%d'),
                    'student_name': issue.student.name,
                    'user_id': issue.student.user_id,
                    'payment_type': p_type.title(),
                    'amount': fine_amount,
                    'method': p_method.upper(),
                    'processed_by': 'Admin',
                    'book_title': issue.book.title if p_type == 'fine' else 'N/A'
                })

        return jsonify({
            'payments': payments,
            'total_amount': sum(p['amount'] for p in payments),
            'total_count': len(payments)
        })

    except Exception as e:
        return jsonify({
            'payments': [],
            'total_amount': 0,
            'total_count': 0,
            'error': str(e)
        })

@app.route('/librarian/circulation/payments')
def librarian_payment_management():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    return render_template('librarian_payment_management.html')

@app.route('/admin/circulation/binding')
def admin_binding_management():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_binding_management.html')

@app.route('/librarian/circulation/binding')
def librarian_binding_management():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    return render_template('librarian_binding_management.html')

@app.route('/admin/circulation/fines')
def admin_fine_management():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_fine_management.html')

@app.route('/librarian/circulation/fines')
def librarian_fine_management():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    return render_template('librarian_fine_management.html')

# College and Department Management Routes
@app.route('/admin/colleges')
def admin_colleges():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    colleges = get_colleges()
    return render_template('admin_colleges.html', colleges=colleges)

@app.route('/admin/departments')
def admin_departments():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    colleges = get_colleges()
    departments = get_departments()
    return render_template('admin_departments.html', colleges=colleges, departments=departments)

@app.route('/api/colleges', methods=['GET', 'POST'])
def api_colleges():
    if session.get('user_role') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    if request.method == 'POST':
        data = request.get_json()
        try:
            college = College(
                name=data['name'],
                code=data['code'],
                address=data.get('address', ''),
                phone=data.get('phone', ''),
                email=data.get('email', '')
            )
            db.session.add(college)
            db.session.commit()
            return jsonify({'success': True, 'message': 'College added successfully'})
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 400

    # GET request
    colleges = get_colleges()
    return jsonify([{
        'id': c.id,
        'name': c.name,
        'code': c.code,
        'address': c.address,
        'phone': c.phone,
        'email': c.email,
        'is_active': c.is_active
    } for c in colleges])

@app.route('/api/departments', methods=['GET', 'POST'])
def api_departments():
    if session.get('user_role') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    if request.method == 'POST':
        data = request.get_json()
        try:
            department = Department(
                name=data['name'],
                code=data['code'],
                college_id=data['college_id'],
                head_of_department=data.get('head_of_department', ''),
                phone=data.get('phone', ''),
                email=data.get('email', '')
            )
            db.session.add(department)
            db.session.commit()
            return jsonify({'success': True, 'message': 'Department added successfully'})
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 400

    # GET request
    college_id = request.args.get('college_id')
    departments = get_departments(college_id)
    return jsonify([{
        'id': d.id,
        'name': d.name,
        'code': d.code,
        'college_id': d.college_id,
        'college_name': d.college.name,
        'head_of_department': d.head_of_department,
        'phone': d.phone,
        'email': d.email,
        'is_active': d.is_active
    } for d in departments])

@app.route('/api/departments/<int:college_id>')
def api_departments_by_college(college_id):
    """Get departments for a specific college"""
    departments = Department.query.filter_by(college_id=college_id, is_active=True).all()
    return jsonify([{
        'id': d.id,
        'name': d.name,
        'code': d.code
    } for d in departments])

# AutoLib-Style Report Routes for Admin
@app.route('/admin/reports/library-connection')
def admin_library_connection_report():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_library_connection_report.html')

@app.route('/api/admin/library-connection-data')
def admin_library_connection_data():
    if session.get('user_role') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    # Get filter parameters
    date_from = request.args.get('dateFrom')
    date_to = request.args.get('dateTo')
    user_type = request.args.get('userType')
    department = request.args.get('department')

    # For now, return real user data with simulated login sessions
    # In a real system, you'd have a UserSession table to track logins
    users_data = []

    # Get students
    students = Student.query.filter_by(is_active=True).all() if not user_type or user_type == 'student' else []
    for student in students:
        if department and student.department != department:
            continue
        users_data.append({
            'user': f"{student.name} ({student.user_id})",
            'type': 'Student',
            'department': student.department or 'N/A',
            'loginTime': '2024-01-15 09:30:00',  # Would come from session table
            'logoutTime': '2024-01-15 11:45:00',  # Would come from session table
            'duration': '2h 15m',
            'ipAddress': '*************',  # Would come from session table
            'status': 'Completed'
        })

    # Get librarians
    librarians = Librarian.query.all() if not user_type or user_type == 'librarian' else []
    for librarian in librarians:
        users_data.append({
            'user': f"{librarian.name} (LIB)",
            'type': 'Librarian',
            'department': 'Library',
            'loginTime': '2024-01-15 08:00:00',  # Would come from session table
            'logoutTime': 'Active',  # Would come from session table
            'duration': '6h 30m',
            'ipAddress': '************',  # Would come from session table
            'status': 'Active'
        })

    # Calculate statistics
    total_sessions = len(users_data)
    active_users = len([u for u in users_data if u['status'] == 'Active'])

    return jsonify({
        'data': users_data[:50],  # Limit to 50 records for performance
        'statistics': {
            'totalSessions': total_sessions,
            'avgDuration': '2h 30m',  # Would be calculated from actual data
            'peakHour': '10:00 AM',  # Would be calculated from actual data
            'activeUsers': active_users
        }
    })

@app.route('/admin/reports/access-register')
def admin_access_register_report():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_access_register_report.html')

@app.route('/api/admin/access-register-data')
def admin_access_register_data():
    if session.get('user_role') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    # Get filter parameters
    date_from = request.args.get('dateFrom')
    date_to = request.args.get('dateTo')
    user_type = request.args.get('userType')
    department = request.args.get('department')

    # Get real issue data to simulate access register
    # In a real system, you'd have an AccessLog table
    access_data = []

    # Get recent issues as access records
    issues = Issue.query.join(Student).filter(
        Issue.issue_date >= datetime.strptime(date_from, '%Y-%m-%d').date() if date_from else True,
        Issue.issue_date <= datetime.strptime(date_to, '%Y-%m-%d').date() if date_to else True
    ).order_by(Issue.issue_date.desc()).limit(100).all()

    for i, issue in enumerate(issues, 1):
        student = issue.student
        if user_type and user_type != 'student':
            continue
        if department and student.department != department:
            continue

        access_data.append({
            'sno': i,
            'userId': student.user_id,
            'name': student.name,
            'department': student.department or 'N/A',
            'entryTime': issue.issue_date.strftime('%Y-%m-%d %H:%M:%S'),
            'exitTime': issue.return_date.strftime('%Y-%m-%d %H:%M:%S') if issue.return_date else 'Still Inside',
            'duration': '2h 30m',  # Would be calculated from entry/exit times
            'purpose': 'Book Issue/Return',
            'status': 'Completed' if issue.return_date else 'Inside'
        })

    # Calculate statistics
    total_entries = len(access_data)
    currently_inside = len([a for a in access_data if a['status'] == 'Inside'])

    return jsonify({
        'data': access_data,
        'statistics': {
            'totalEntries': total_entries,
            'avgDuration': '2h 15m',  # Would be calculated from actual data
            'peakTime': '10:30 AM',  # Would be calculated from actual data
            'currentlyInside': currently_inside
        }
    })

@app.route('/admin/reports/bibliography')
def admin_bibliography_report():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_bibliography_report.html',
                         colleges=get_colleges_for_dropdown(),
                         departments=get_departments_for_dropdown())

@app.route('/admin/reports/counter')
def admin_counter_report():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_counter_report.html')

@app.route('/admin/reports/statistics')
def admin_statistics_report():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_statistics_report.html')

@app.route('/api/admin/statistics-data')
def admin_statistics_data():
    if session.get('user_role') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    try:
        # Get real statistics
        total_books = Book.query.count()
        total_ebooks = EBook.query.count() if 'EBook' in globals() else 0
        active_students = Student.query.filter_by(is_active=True).count()
        current_issues = Issue.query.filter(Issue.return_date.is_(None)).count()

        # Calculate utilization rate
        total_resources = total_books + total_ebooks
        utilization_rate = (current_issues / total_resources * 100) if total_resources > 0 else 0

        # Get department-wise statistics
        from sqlalchemy import func
        dept_stats = db.session.query(
            Student.department,
            func.count(Issue.issue_id).label('issue_count')
        ).join(Issue, Student.user_id == Issue.user_id, isouter=True)\
         .group_by(Student.department)\
         .all()

        department_data = []
        for dept, count in dept_stats:
            if dept:  # Only include departments with names
                department_data.append({
                    'department': dept,
                    'issues': count or 0
                })

        # Get monthly circulation trends (last 6 months)
        from datetime import date, timedelta
        import calendar

        circulation_trends = []
        for i in range(6):
            month_start = date.today().replace(day=1) - timedelta(days=i*30)
            month_name = calendar.month_name[month_start.month]

            # Count issues for this month (simplified)
            month_issues = Issue.query.filter(
                Issue.issue_date >= month_start,
                Issue.issue_date < month_start + timedelta(days=30)
            ).count()

            circulation_trends.append({
                'month': month_name[:3],  # Short month name
                'issues': month_issues
            })

        circulation_trends.reverse()  # Show oldest to newest

        return jsonify({
            'summary': {
                'total_books': total_books + total_ebooks,
                'active_members': active_students,
                'current_issues': current_issues,
                'utilization_rate': round(utilization_rate, 1)
            },
            'department_stats': department_data,
            'circulation_trends': circulation_trends
        })
    except Exception as e:
        return jsonify({
            'summary': {
                'total_books': 0,
                'active_members': 0,
                'current_issues': 0,
                'utilization_rate': 0
            },
            'department_stats': [],
            'circulation_trends': [],
            'error': str(e)
        })

@app.route('/admin/reports/binding')
def admin_binding_report():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_binding_report.html')

@app.route('/admin/reports/database')
def admin_database_report():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_database_report.html')

@app.route('/api/admin/database-statistics')
def admin_database_statistics():
    if session.get('user_role') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    try:
        # Get real database statistics
        tables_info = []

        # Books table
        books_count = Book.query.count()
        tables_info.append({
            'name': 'Books',
            'icon': 'fas fa-book',
            'count': books_count,
            'size': f"{books_count * 2}",  # Estimated size in KB
            'status': 'Healthy' if books_count >= 0 else 'Warning',
            'last_modified': 'Recent'
        })

        # Students table
        students_count = Student.query.count()
        tables_info.append({
            'name': 'Students',
            'icon': 'fas fa-graduation-cap',
            'count': students_count,
            'size': f"{students_count * 1}",  # Estimated size in KB
            'status': 'Healthy' if students_count >= 0 else 'Warning',
            'last_modified': 'Recent'
        })

        # Issues table
        issues_count = Issue.query.count()
        tables_info.append({
            'name': 'Issues',
            'icon': 'fas fa-exchange-alt',
            'count': issues_count,
            'size': f"{issues_count * 1}",  # Estimated size in KB
            'status': 'Healthy' if issues_count >= 0 else 'Warning',
            'last_modified': 'Recent'
        })

        # Librarians table
        librarians_count = Librarian.query.count()
        tables_info.append({
            'name': 'Librarians',
            'icon': 'fas fa-user-tie',
            'count': librarians_count,
            'size': f"{librarians_count * 1}",  # Estimated size in KB
            'status': 'Healthy' if librarians_count >= 0 else 'Warning',
            'last_modified': 'Recent'
        })

        # Colleges table
        colleges_count = College.query.count()
        tables_info.append({
            'name': 'Colleges',
            'icon': 'fas fa-university',
            'count': colleges_count,
            'size': f"{colleges_count * 1}",  # Estimated size in KB
            'status': 'Healthy' if colleges_count >= 0 else 'Warning',
            'last_modified': 'Recent'
        })

        # Departments table
        departments_count = Department.query.count()
        tables_info.append({
            'name': 'Departments',
            'icon': 'fas fa-building',
            'count': departments_count,
            'size': f"{departments_count * 1}",  # Estimated size in KB
            'status': 'Healthy' if departments_count >= 0 else 'Warning',
            'last_modified': 'Recent'
        })

        # Calculate totals
        total_records = sum(table['count'] for table in tables_info)
        total_size = sum(int(table['size']) for table in tables_info)

        return jsonify({
            'tables': tables_info,
            'summary': {
                'total_records': total_records,
                'total_size': total_size,
                'total_tables': len(tables_info),
                'database_status': 'Healthy'
            }
        })
    except Exception as e:
        return jsonify({
            'tables': [],
            'summary': {
                'total_records': 0,
                'total_size': 0,
                'total_tables': 0,
                'database_status': 'Error'
            },
            'error': str(e)
        })

@app.route('/admin/reports/member')
def admin_member_report():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_member_report.html')

@app.route('/admin/reports/resource')
def admin_resource_report():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_resource_report.html')

@app.route('/admin/reports/no-dues')
def admin_no_dues_report():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_no_dues_report.html')

@app.route('/admin/reports/qb')
def admin_qb_report():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_qb_report.html')

@app.route('/admin/reports/transfer')
def admin_transfer_report():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_transfer_report.html')

@app.route('/admin/reports/missing')
def admin_missing_report():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_missing_report.html')

@app.route('/admin/reports/news-clipping')
def admin_news_clipping_report():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    return render_template('admin_news_clipping_report.html',
                         colleges=get_colleges_for_dropdown(),
                         departments=get_departments_for_dropdown())

# Librarian Report Routes
@app.route('/librarian/reports/access-register')
def librarian_access_register_report():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    return render_template('librarian_access_register_report.html')

@app.route('/librarian/reports/bibliography')
def librarian_bibliography_report():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    return render_template('librarian_bibliography_report.html')

@app.route('/librarian/reports/counter')
def librarian_counter_report():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    return render_template('librarian_counter_report.html')

@app.route('/librarian/reports/statistics')
def librarian_statistics_report():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    return render_template('librarian_statistics_report.html')

@app.route('/librarian/reports/member')
def librarian_member_report():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    return render_template('librarian_member_report.html')

@app.route('/librarian/reports/resource')
def librarian_resource_report():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    return render_template('librarian_resource_report.html')

@app.route('/librarian/reports/no-dues')
def librarian_no_dues_report():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    return render_template('librarian_no_dues_report.html')

@app.route('/admin/reports')
def admin_reports():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))
    return render_template('admin_reports.html')

@app.route('/api/admin/quick-statistics')
def admin_quick_statistics():
    if session.get('user_role') != 'admin':
        return jsonify({'error': 'Unauthorized'}), 401

    try:
        # Get real statistics from database
        total_books = Book.query.count()
        total_ebooks = EBook.query.count() if 'EBook' in globals() else 0
        active_students = Student.query.filter_by(is_active=True).count()
        active_librarians = Librarian.query.count()
        current_issues = Issue.query.filter(Issue.return_date.is_(None)).count()

        # Calculate overdue books (assuming 14 days loan period)
        from datetime import date, timedelta
        overdue_date = date.today() - timedelta(days=14)
        overdue_books = Issue.query.filter(
            Issue.return_date.is_(None),
            Issue.issue_date < overdue_date
        ).count()

        return jsonify({
            'total_books': total_books + total_ebooks,
            'active_members': active_students + active_librarians,
            'current_issues': current_issues,
            'overdue_books': overdue_books
        })
    except Exception as e:
        return jsonify({
            'total_books': 0,
            'active_members': 0,
            'current_issues': 0,
            'overdue_books': 0,
            'error': str(e)
        })

@app.route('/admin/ebooks')
def admin_ebooks():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))

    ebooks = EBook.query.all()
    return render_template('admin_ebooks.html', ebooks=ebooks)

@app.route('/admin/add_ebook', methods=['GET', 'POST'])
def admin_add_ebook():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))

    if request.method == 'POST':
        try:
            # Check if access number already exists
            if EBook.query.filter_by(access_no=request.form['access_no']).first():
                flash('Access number already exists!')
                return render_template('admin_add_ebook.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())

            ebook = EBook(
                access_no=request.form['access_no'],
                title=request.form['title'],
                author=request.form['author'],
                publisher=request.form['publisher'],
                subject=request.form['subject'],
                department=request.form['department'],
                category=request.form['category'],
                file_format=request.form['file_format'],
                file_size=request.form.get('file_size', ''),
                download_url=request.form.get('download_url', ''),
                isbn=request.form.get('isbn', ''),
                pages=int(request.form['pages']) if request.form.get('pages') else None,
                language=request.form.get('language', 'English'),
                description=request.form.get('description', '')
            )
            db.session.add(ebook)
            db.session.commit()
            flash('E-book added successfully!')
            return redirect(url_for('admin_ebooks'))

        except ValueError as e:
            flash('Invalid input. Please check your data.')
            return render_template('admin_add_ebook.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())
        except Exception as e:
            flash(f'Error adding e-book: {str(e)}')
            return render_template('admin_add_ebook.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())

    return render_template('admin_add_ebook.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())

@app.route('/admin/bulk_ebooks', methods=['GET', 'POST'])
def admin_bulk_ebooks():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))

    if request.method == 'POST':
        if 'file' not in request.files:
            flash('No file selected')
            return redirect(request.url)

        file = request.files['file']
        if file.filename == '':
            flash('No file selected')
            return redirect(request.url)

        if file and allowed_file(file.filename):
            try:
                df = pd.read_excel(file)

                required_columns = ['access_no', 'title', 'author', 'publisher', 'subject', 'department', 'category', 'file_format']
                missing_columns = [col for col in required_columns if col not in df.columns]

                if missing_columns:
                    flash(f'Missing required columns: {", ".join(missing_columns)}')
                    return render_template('admin_bulk_ebooks.html')

                success_count = 0
                error_count = 0
                errors = []

                for index, row in df.iterrows():
                    try:
                        # Check if access number already exists
                        if EBook.query.filter_by(access_no=row['access_no']).first():
                            error_count += 1
                            errors.append(f'Row {index + 2}: Access number {row["access_no"]} already exists')
                            continue

                        ebook = EBook(
                            access_no=row['access_no'],
                            title=row['title'],
                            author=row['author'],
                            publisher=row['publisher'],
                            subject=row['subject'],
                            department=row['department'],
                            category=row['category'],
                            file_format=row['file_format'],
                            file_size=row.get('file_size', ''),
                            download_url=row.get('download_url', ''),
                            isbn=row.get('isbn', ''),
                            pages=int(row['pages']) if pd.notna(row.get('pages')) else None,
                            language=row.get('language', 'English'),
                            description=row.get('description', '')
                        )

                        db.session.add(ebook)
                        success_count += 1

                    except Exception as e:
                        error_count += 1
                        errors.append(f'Row {index + 2}: {str(e)}')

                if success_count > 0:
                    db.session.commit()
                    flash(f'Successfully added {success_count} e-books!')

                if error_count > 0:
                    flash(f'{error_count} e-books failed to upload. Errors: {"; ".join(errors[:5])}{"..." if len(errors) > 5 else ""}')

                return redirect(url_for('admin_ebooks'))

            except Exception as e:
                flash(f'Error processing file: {str(e)}')
                return render_template('admin_bulk_ebooks.html')
        else:
            flash('Invalid file format. Please upload an Excel file.')

    return render_template('admin_bulk_ebooks.html')

@app.route('/admin/ebooks/delete/<int:ebook_id>', methods=['POST'])
def admin_delete_ebook(ebook_id):
    if session.get('user_role') != 'admin':
        return jsonify({'success': False, 'message': 'Unauthorized'}), 403

    try:
        ebook = EBook.query.get_or_404(ebook_id)
        db.session.delete(ebook)
        db.session.commit()
        return jsonify({'success': True, 'message': 'E-book deleted successfully!'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'Error deleting e-book: {str(e)}'}), 500

@app.route('/admin/edit_ebook/<int:ebook_id>', methods=['GET', 'POST'])
def admin_edit_ebook(ebook_id):
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))

    ebook = EBook.query.get_or_404(ebook_id)

    if request.method == 'POST':
        try:
            # Check if access number already exists (excluding current ebook)
            existing_ebook = EBook.query.filter_by(access_no=request.form['access_no']).first()
            if existing_ebook and existing_ebook.ebook_id != ebook_id:
                flash('Access number already exists!')
                return render_template('admin_edit_ebook.html', ebook=ebook, departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())

            ebook.access_no = request.form['access_no']
            ebook.title = request.form['title']
            ebook.author = request.form['author']
            ebook.publisher = request.form['publisher']
            ebook.subject = request.form['subject']
            ebook.department = request.form['department']
            ebook.category = request.form['category']
            ebook.file_format = request.form['file_format']
            ebook.file_size = request.form.get('file_size', '')
            ebook.download_url = request.form.get('download_url', '')
            ebook.isbn = request.form.get('isbn', '')
            ebook.pages = int(request.form['pages']) if request.form.get('pages') else None
            ebook.language = request.form.get('language', 'English')
            ebook.description = request.form.get('description', '')

            db.session.commit()
            flash('E-book updated successfully!')
            return redirect(url_for('admin_ebooks'))

        except ValueError as e:
            flash('Invalid input. Please check your data.')
            return render_template('admin_edit_ebook.html', ebook=ebook, departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())
        except Exception as e:
            flash(f'Error updating e-book: {str(e)}')
            return render_template('admin_edit_ebook.html', ebook=ebook, departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())

    return render_template('admin_edit_ebook.html', ebook=ebook, departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())

@app.route('/admin/generate_report', methods=['GET', 'POST'])
def admin_generate_report():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login', role='admin'))

    # Handle GET requests for quick reports
    if request.method == 'GET':
        report_type = request.args.get('report_type')
        export_format = request.args.get('export_format')
        if not report_type or not export_format:
            return redirect(url_for('admin_reports'))
    else:
        report_type = request.form.get('report_type')
        export_format = request.form.get('export_format')

    start_date = request.form.get('start_date') or request.args.get('start_date')
    end_date = request.form.get('end_date') or request.args.get('end_date')
    department = request.form.get('department') or request.args.get('department')
    designation = request.form.get('designation') or request.args.get('designation')
    preview = request.form.get('preview') == 'true'

    if not report_type or not export_format:
        if preview:
            return jsonify({'success': False, 'message': 'Report type and format are required'})
        flash('Report type and export format are required')
        return redirect(url_for('admin_reports'))

    try:
        # Generate report data based on type
        data, filename = generate_report_data(report_type, start_date, end_date, department, designation)

        if preview:
            # Return HTML preview
            html_preview = generate_html_preview(data, report_type)
            return jsonify({'success': True, 'html': html_preview})

        # Generate file based on format
        if export_format == 'excel':
            return generate_excel_report(data, filename)
        elif export_format == 'pdf':
            return generate_pdf_report(data, filename, report_type)
        else:
            flash('Invalid export format')
            return redirect(url_for('admin_reports'))

    except Exception as e:
        if preview:
            return jsonify({'success': False, 'message': str(e)})
        flash(f'Error generating report: {str(e)}')
        return redirect(url_for('admin_reports'))

def generate_report_data(report_type, start_date=None, end_date=None, department=None, designation=None):
    """Generate report data based on type and filters"""

    # Parse dates if provided
    start_dt = None
    end_dt = None
    if start_date:
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
    if end_date:
        end_dt = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)  # Include end date

    # Base query
    query = Issue.query.join(Student).join(Book)

    # Apply date filters
    if start_dt:
        query = query.filter(Issue.issue_date >= start_dt)
    if end_dt:
        query = query.filter(Issue.issue_date < end_dt)

    # Apply department filter
    if department:
        query = query.filter(Student.department == department)

    # Apply designation filter
    if designation:
        query = query.filter(Student.designation == designation)

    data = []
    filename = f"library_report_{report_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

    if report_type == 'issued_books':
        # Currently issued books
        issues = query.filter(Issue.return_date == None).all()
        for issue in issues:
            data.append({
                'Issue ID': issue.issue_id,
                'Book Title': issue.book.title,
                'Author': issue.book.author,
                'Access No': issue.book.access_no,
                'Student Name': issue.student.name,
                'Student ID': issue.student.user_id,
                'Department': issue.student.department,
                'Designation': issue.student.designation,
                'Issue Date': issue.issue_date.strftime('%Y-%m-%d'),
                'Due Date': issue.due_date.strftime('%Y-%m-%d'),
                'Days Overdue': max(0, (datetime.now() - issue.due_date).days) if datetime.now() > issue.due_date else 0
            })
        filename += "_currently_issued"

    elif report_type == 'returned_books':
        # Returned books
        issues = query.filter(Issue.return_date != None).all()
        for issue in issues:
            data.append({
                'Issue ID': issue.issue_id,
                'Book Title': issue.book.title,
                'Author': issue.book.author,
                'Access No': issue.book.access_no,
                'Student Name': issue.student.name,
                'Student ID': issue.student.user_id,
                'Department': issue.student.department,
                'Designation': issue.student.designation,
                'Issue Date': issue.issue_date.strftime('%Y-%m-%d'),
                'Due Date': issue.due_date.strftime('%Y-%m-%d'),
                'Return Date': issue.return_date.strftime('%Y-%m-%d'),
                'Fine': f"₹{issue.fine:.2f}" if issue.fine > 0 else "₹0.00"
            })
        filename += "_returned_books"

    elif report_type == 'overdue_books':
        # Overdue books
        issues = query.filter(
            Issue.return_date == None,
            Issue.due_date < datetime.now()
        ).all()
        for issue in issues:
            days_overdue = (datetime.now() - issue.due_date).days
            data.append({
                'Issue ID': issue.issue_id,
                'Book Title': issue.book.title,
                'Author': issue.book.author,
                'Access No': issue.book.access_no,
                'Student Name': issue.student.name,
                'Student ID': issue.student.user_id,
                'Department': issue.student.department,
                'Designation': issue.student.designation,
                'Issue Date': issue.issue_date.strftime('%Y-%m-%d'),
                'Due Date': issue.due_date.strftime('%Y-%m-%d'),
                'Days Overdue': days_overdue,
                'Expected Fine': f"₹{days_overdue * 1.0:.2f}"  # ₹1 per day
            })
        filename += "_overdue_books"

    elif report_type == 'all_transactions':
        # All transactions
        issues = query.all()
        for issue in issues:
            data.append({
                'Issue ID': issue.issue_id,
                'Book Title': issue.book.title,
                'Author': issue.book.author,
                'Access No': issue.book.access_no,
                'Student Name': issue.student.name,
                'Student ID': issue.student.user_id,
                'Department': issue.student.department,
                'Designation': issue.student.designation,
                'Issue Date': issue.issue_date.strftime('%Y-%m-%d'),
                'Due Date': issue.due_date.strftime('%Y-%m-%d'),
                'Return Date': issue.return_date.strftime('%Y-%m-%d') if issue.return_date else 'Not Returned',
                'Status': 'Returned' if issue.return_date else 'Issued',
                'Fine': f"₹{issue.fine:.2f}" if issue.fine > 0 else "₹0.00"
            })
        filename += "_all_transactions"

    elif report_type == 'fine_report':
        # Fine report
        issues = query.filter(Issue.fine > 0).all()
        for issue in issues:
            data.append({
                'Issue ID': issue.issue_id,
                'Book Title': issue.book.title,
                'Student Name': issue.student.name,
                'Student ID': issue.student.user_id,
                'Department': issue.student.department,
                'Designation': issue.student.designation,
                'Issue Date': issue.issue_date.strftime('%Y-%m-%d'),
                'Due Date': issue.due_date.strftime('%Y-%m-%d'),
                'Return Date': issue.return_date.strftime('%Y-%m-%d') if issue.return_date else 'Not Returned',
                'Fine Amount': f"₹{issue.fine:.2f}"
            })
        filename += "_fine_report"

    elif report_type == 'student_activity':
        # Student activity report
        from sqlalchemy import func
        student_stats = db.session.query(
            Student.name,
            Student.user_id,
            Student.department,
            Student.designation,
            func.count(Issue.issue_id).label('total_books_borrowed'),
            func.count(Issue.return_date).label('books_returned'),
            func.sum(Issue.fine).label('total_fines')
        ).join(Issue).group_by(Student.id).all()

        for stat in student_stats:
            data.append({
                'Student Name': stat.name,
                'Student ID': stat.user_id,
                'Department': stat.department,
                'Designation': stat.designation,
                'Total Books Borrowed': stat.total_books_borrowed,
                'Books Returned': stat.books_returned or 0,
                'Books Currently Issued': stat.total_books_borrowed - (stat.books_returned or 0),
                'Total Fines': f"₹{stat.total_fines:.2f}" if stat.total_fines else "₹0.00"
            })
        filename += "_student_activity"

    return data, filename

def generate_excel_report(data, filename):
    """Generate Excel report"""
    if not data:
        raise Exception("No data available for the selected criteria")

    df = pd.DataFrame(data)

    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='Report')

        # Get the workbook and worksheet
        workbook = writer.book
        worksheet = writer.sheets['Report']

        # Auto-adjust column widths
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width

    output.seek(0)

    return send_file(
        output,
        as_attachment=True,
        download_name=f'{filename}.xlsx',
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )

def generate_pdf_report(data, filename, report_type):
    """Generate PDF report using HTML to PDF conversion"""
    if not data:
        raise Exception("No data available for the selected criteria")

    # For now, we'll create a simple HTML table and convert to PDF
    # This is a basic implementation - you can enhance it with proper PDF libraries
    html_content = f"""
    <html>
    <head>
        <title>{report_type.replace('_', ' ').title()} Report</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            h1 {{ color: #333; text-align: center; }}
            table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; font-weight: bold; }}
            tr:nth-child(even) {{ background-color: #f9f9f9; }}
            .header {{ text-align: center; margin-bottom: 20px; }}
            .footer {{ text-align: center; margin-top: 20px; font-size: 12px; color: #666; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>Library Management System</h1>
            <h2>{report_type.replace('_', ' ').title()} Report</h2>
            <p>Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
        <table>
            <thead>
                <tr>
    """

    # Add headers
    if data:
        for key in data[0].keys():
            html_content += f"<th>{key}</th>"

    html_content += """
                </tr>
            </thead>
            <tbody>
    """

    # Add data rows
    for row in data:
        html_content += "<tr>"
        for value in row.values():
            html_content += f"<td>{value}</td>"
        html_content += "</tr>"

    html_content += """
            </tbody>
        </table>
        <div class="footer">
            <p>Total Records: {}</p>
        </div>
    </body>
    </html>
    """.format(len(data))

    # For now, return HTML as PDF (you can integrate with libraries like weasyprint or pdfkit)
    output = BytesIO()
    output.write(html_content.encode('utf-8'))
    output.seek(0)

    return send_file(
        output,
        as_attachment=True,
        download_name=f'{filename}.html',  # Change to .pdf when using proper PDF library
        mimetype='text/html'  # Change to application/pdf when using proper PDF library
    )

def generate_html_preview(data, report_type):
    """Generate HTML preview for reports"""
    if not data:
        return "<div class='alert alert-warning'>No data available for the selected criteria</div>"

    # Limit preview to first 10 rows
    preview_data = data[:10]

    html = f"""
    <div class="table-responsive">
        <h5>{report_type.replace('_', ' ').title()} Report Preview</h5>
        <p class="text-muted">Showing first {len(preview_data)} of {len(data)} records</p>
        <table class="table table-striped table-bordered">
            <thead class="table-dark">
                <tr>
    """

    # Add headers
    if preview_data:
        for key in preview_data[0].keys():
            html += f"<th>{key}</th>"

    html += """
                </tr>
            </thead>
            <tbody>
    """

    # Add data rows
    for row in preview_data:
        html += "<tr>"
        for value in row.values():
            html += f"<td>{value}</td>"
        html += "</tr>"

    html += """
            </tbody>
        </table>
    </div>
    """

    if len(data) > 10:
        html += f"<div class='alert alert-info mt-2'>... and {len(data) - 10} more records</div>"

    return html

@app.route('/admin/delete_librarian/<int:librarian_id>', methods=['POST'])
def admin_delete_librarian(librarian_id):
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    
    librarian = Librarian.query.get_or_404(librarian_id)
    db.session.delete(librarian)
    db.session.commit()
    flash('Librarian deleted successfully!')
    return redirect(url_for('admin_librarians'))

@app.route('/admin/delete_student/<int:student_id>', methods=['POST'])
def admin_delete_student(student_id):
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    
    student = Student.query.get_or_404(student_id)
    
    # Check if student has active issues
    active_issues = Issue.query.filter_by(student_id=student_id, return_date=None).count()
    if active_issues > 0:
        flash('Cannot delete student. Student has active book issues.')
        return redirect(url_for('admin_students'))
    
    db.session.delete(student)
    db.session.commit()
    flash('Student deleted successfully!')
    return redirect(url_for('admin_students'))

# Librarian Routes
@app.route('/librarian/dashboard')
def librarian_dashboard():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))

    # Get real statistics from database (no dummy data)
    total_books = Book.query.count()
    total_ebooks = EBook.query.filter_by(is_active=True).count()
    total_students = Student.query.count()
    available_books = sum(book.available_count for book in Book.query.all())
    issued_books = Issue.query.filter_by(return_date=None).count()

    # Calculate overdue books using timezone-aware datetime
    from datetime import datetime, timezone
    now = datetime.now(timezone.utc)
    overdue_books = Issue.query.filter(
        Issue.return_date == None,
        Issue.due_date < now
    ).count()

    # Calculate pending fines (real data only)
    pending_fines = db.session.query(db.func.sum(Issue.fine)).filter(
        Issue.fine > 0,
        Issue.return_date.is_(None)
    ).scalar() or 0

    stats = {
        'total_books': total_books,
        'total_ebooks': total_ebooks,
        'total_students': total_students,
        'available_books': available_books,
        'issued_books': issued_books,
        'overdue_books': overdue_books,
        'pending_fines': round(pending_fines, 2)
    }

    return render_template('librarian_dashboard.html', stats=stats)

@app.route('/librarian/issue_book', methods=['GET', 'POST'])
def librarian_issue_book():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))
    
    if request.method == 'POST':
        book_id = request.form.get('book_id')
        student_user_id = request.form.get('student_user_id')
        if not book_id or not student_user_id:
            flash('Please select a book and enter a valid Student User ID.')
            books = Book.query.filter(Book.available_count > 0).all()
            return render_template('librarian_issue_book.html', books=books)

        book = Book.query.get(book_id)
        student = Student.query.filter_by(user_id=student_user_id).first()

        if not book or not student:
            flash('Invalid book or student!')
            books = Book.query.filter(Book.available_count > 0).all()
            return render_template('librarian_issue_book.html', books=books)

        if book.available_count <= 0:
            flash('Book not available!')
            books = Book.query.filter(Book.available_count > 0).all()
            return render_template('librarian_issue_book.html', books=books)

        # Check if student already has this book
        existing_issue = Issue.query.filter_by(
            book_id=book_id,
            student_id=student.id,
            return_date=None
        ).first()

        if existing_issue:
            flash('Student already has this book!')
            books = Book.query.filter(Book.available_count > 0).all()
            return render_template('librarian_issue_book.html', books=books)

        # Check book limit
        active_issues_count = Issue.query.filter_by(student_id=student.id, return_date=None).count()
        if student.designation == 'Staff':
            book_limit = int(get_setting_value('staff_book_limit', 5))
        else:
            book_limit = int(get_setting_value('student_book_limit', 3))
        if active_issues_count >= book_limit:
            flash('Maximum limit reached for borrowing books!')
            books = Book.query.filter(Book.available_count > 0).all()
            return render_template('librarian_issue_book.html', books=books)

        # Create new issue
        issue = Issue(
            book_id=book_id,
            student_id=student.id,
            due_date=datetime.utcnow() + timedelta(days=14)  # 14 days loan period
        )

        book.available_count -= 1

        db.session.add(issue)
        db.session.commit()

        flash('Book issued successfully!')
        return redirect(url_for('librarian_dashboard'))

    books = Book.query.filter(Book.available_count > 0).all()
    return render_template('librarian_issue_book.html', books=books)

@app.route('/librarian/return_book', methods=['GET', 'POST'])
def librarian_return_book():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))
    
    if request.method == 'POST':
        issue_id = request.form.get('issue_id')
        if not issue_id:
            flash('No issue selected for return. Please try again.')
            active_issues = Issue.query.filter_by(return_date=None).all()
            return render_template('librarian_return_book.html', issues=active_issues)

        issue = Issue.query.get(issue_id)

        if not issue or issue.return_date:
            flash('Invalid issue or book already returned!')
            active_issues = Issue.query.filter_by(return_date=None).all()
            return render_template('librarian_return_book.html', issues=active_issues)

        # Calculate fine
        return_date = datetime.utcnow()
        fine = calculate_fine(issue.due_date, return_date)

        # Update issue record
        issue.return_date = return_date
        issue.fine = fine

        # Update book availability
        book = Book.query.get(issue.book_id)
        book.available_count += 1

        db.session.commit()

        if fine > 0:
            flash(f'Book returned successfully! Fine: ${fine:.2f}')
        else:
            flash('Book returned successfully!')

        return redirect(url_for('librarian_dashboard'))

    # Get all active issues
    active_issues = Issue.query.filter_by(return_date=None).all()
    return render_template('librarian_return_book.html', issues=active_issues)

@app.route('/librarian/books')
def librarian_books():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))

    books = Book.query.all()
    return render_template('librarian_books.html', books=books)

@app.route('/librarian/ebooks')
def librarian_ebooks():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))

    ebooks = EBook.query.all()
    return render_template('librarian_ebooks.html', ebooks=ebooks)

@app.route('/librarian/add_ebook', methods=['GET', 'POST'])
def librarian_add_ebook():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))

    if request.method == 'POST':
        try:
            # Check if access number already exists
            if EBook.query.filter_by(access_no=request.form['access_no']).first():
                flash('Access number already exists!')
                return render_template('librarian_add_ebook.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())

            ebook = EBook(
                access_no=request.form['access_no'],
                title=request.form['title'],
                author=request.form['author'],
                publisher=request.form['publisher'],
                subject=request.form['subject'],
                department=request.form['department'],
                category=request.form['category'],
                file_format=request.form['file_format'],
                file_size=request.form.get('file_size', ''),
                download_url=request.form.get('download_url', ''),
                isbn=request.form.get('isbn', ''),
                pages=int(request.form['pages']) if request.form.get('pages') else None,
                language=request.form.get('language', 'English'),
                description=request.form.get('description', '')
            )
            db.session.add(ebook)
            db.session.commit()
            flash('E-book added successfully!')
            return redirect(url_for('librarian_ebooks'))

        except ValueError as e:
            flash('Invalid input. Please check your data.')
            return render_template('librarian_add_ebook.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())
        except Exception as e:
            flash(f'Error adding e-book: {str(e)}')
            return render_template('librarian_add_ebook.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())

    return render_template('librarian_add_ebook.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())

@app.route('/librarian/bulk_ebooks', methods=['GET', 'POST'])
def librarian_bulk_ebooks():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))

    if request.method == 'POST':
        if 'file' not in request.files:
            flash('No file selected')
            return redirect(request.url)

        file = request.files['file']
        if file.filename == '':
            flash('No file selected')
            return redirect(request.url)

        if file and allowed_file(file.filename):
            try:
                df = pd.read_excel(file)

                required_columns = ['access_no', 'title', 'author', 'publisher', 'subject', 'department', 'category', 'file_format']
                missing_columns = [col for col in required_columns if col not in df.columns]

                if missing_columns:
                    flash(f'Missing required columns: {", ".join(missing_columns)}')
                    return render_template('librarian_bulk_ebooks.html')

                success_count = 0
                error_count = 0
                errors = []

                for index, row in df.iterrows():
                    try:
                        # Check if access number already exists
                        if EBook.query.filter_by(access_no=row['access_no']).first():
                            error_count += 1
                            errors.append(f'Row {index + 2}: Access number {row["access_no"]} already exists')
                            continue

                        ebook = EBook(
                            access_no=row['access_no'],
                            title=row['title'],
                            author=row['author'],
                            publisher=row['publisher'],
                            subject=row['subject'],
                            department=row['department'],
                            category=row['category'],
                            file_format=row['file_format'],
                            file_size=row.get('file_size', ''),
                            download_url=row.get('download_url', ''),
                            isbn=row.get('isbn', ''),
                            pages=int(row['pages']) if pd.notna(row.get('pages')) else None,
                            language=row.get('language', 'English'),
                            description=row.get('description', '')
                        )

                        db.session.add(ebook)
                        success_count += 1

                    except Exception as e:
                        error_count += 1
                        errors.append(f'Row {index + 2}: {str(e)}')

                if success_count > 0:
                    db.session.commit()
                    flash(f'Successfully added {success_count} e-books!')

                if error_count > 0:
                    flash(f'{error_count} e-books failed to upload. Errors: {"; ".join(errors[:5])}{"..." if len(errors) > 5 else ""}')

                return redirect(url_for('librarian_ebooks'))

            except Exception as e:
                flash(f'Error processing file: {str(e)}')
                return render_template('librarian_bulk_ebooks.html')
        else:
            flash('Invalid file format. Please upload an Excel file.')

    return render_template('librarian_bulk_ebooks.html')

@app.route('/librarian/edit_ebook/<int:ebook_id>', methods=['GET', 'POST'])
def librarian_edit_ebook(ebook_id):
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))

    ebook = EBook.query.get_or_404(ebook_id)

    if request.method == 'POST':
        try:
            # Check if access number already exists (excluding current ebook)
            existing_ebook = EBook.query.filter_by(access_no=request.form['access_no']).first()
            if existing_ebook and existing_ebook.ebook_id != ebook_id:
                flash('Access number already exists!')
                return render_template('librarian_edit_ebook.html', ebook=ebook, departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())

            ebook.access_no = request.form['access_no']
            ebook.title = request.form['title']
            ebook.author = request.form['author']
            ebook.publisher = request.form['publisher']
            ebook.subject = request.form['subject']
            ebook.department = request.form['department']
            ebook.category = request.form['category']
            ebook.file_format = request.form['file_format']
            ebook.file_size = request.form.get('file_size', '')
            ebook.download_url = request.form.get('download_url', '')
            ebook.isbn = request.form.get('isbn', '')
            ebook.pages = int(request.form['pages']) if request.form.get('pages') else None
            ebook.language = request.form.get('language', 'English')
            ebook.description = request.form.get('description', '')

            db.session.commit()
            flash('E-book updated successfully!')
            return redirect(url_for('librarian_ebooks'))

        except ValueError as e:
            flash('Invalid input. Please check your data.')
            return render_template('librarian_edit_ebook.html', ebook=ebook, departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())
        except Exception as e:
            flash(f'Error updating e-book: {str(e)}')
            return render_template('librarian_edit_ebook.html', ebook=ebook, departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())

    return render_template('librarian_edit_ebook.html', ebook=ebook, departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())

@app.route('/librarian/add_book', methods=['GET', 'POST'])
def librarian_add_book():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))
    
    if request.method == 'POST':
        try:
            # Check if access number already exists
            if Book.query.filter_by(access_no=request.form['access_no']).first():
                flash('Access number already exists!')
                return render_template('librarian_add_book.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())
            
            copies = int(request.form['copies'])
            
            book = Book(
                access_no=request.form['access_no'],
                title=request.form['title'],
                author=request.form['author'],
                publisher=request.form['publisher'],
                subject=request.form['subject'],
                department=request.form['department'],
                category=request.form['category'],
                location=request.form['location'],
                copies=copies,
                quantity=copies,  # For backward compatibility
                available_count=copies
            )
            db.session.add(book)
            db.session.commit()
            flash('Book added successfully!')
            return redirect(url_for('librarian_books'))
            
        except ValueError as e:
            flash('Invalid number of copies. Please enter a valid number.')
            return render_template('librarian_add_book.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())
        except Exception as e:
            flash(f'Error adding book: {str(e)}')
            return render_template('librarian_add_book.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())

    return render_template('librarian_add_book.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())

@app.route('/librarian/edit_book/<int:book_id>', methods=['GET', 'POST'])
def librarian_edit_book(book_id):
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))
    book = Book.query.get_or_404(book_id)
    if request.method == 'POST':
        try:
            book.title = request.form['title']
            book.publisher = request.form['publisher']
            book.subject = request.form['subject']
            book.department = request.form['department']
            book.location = request.form['location']
            book.copies = int(request.form['copies'])
            db.session.commit()
            flash('Book updated successfully!', 'success')
            return redirect(url_for('librarian_books'))
        except Exception as e:
            flash(f'Error updating book: {str(e)}', 'danger')
    return render_template('librarian_edit_book.html', book=book)

@app.route('/librarian/books/delete/<int:book_id>', methods=['POST'])
def librarian_delete_book(book_id):
    if session.get('user_role') != 'librarian':
        return jsonify({'success': False, 'message': 'Unauthorized'}), 403
    
    try:
        book = Book.query.get_or_404(book_id)
        
        # Check if book is currently issued
        active_issues = Issue.query.filter_by(book_id=book_id, return_date=None).count()
        if active_issues > 0:
            return jsonify({'success': False, 'message': 'Cannot delete book. It is currently issued to students.'}), 400
        
        db.session.delete(book)
        db.session.commit()
        return jsonify({'success': True, 'message': 'Book deleted successfully!'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'Error deleting book: {str(e)}'}), 500

@app.route('/librarian/students')
def librarian_students():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))
    
    students = Student.query.all()
    return render_template('librarian_students.html', students=students)

@app.route('/librarian/student_search')
def librarian_student_search():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))
    q = request.args.get('q', '').strip()
    students = []
    if q:
        students = Student.query.filter(
            (Student.name.ilike(f'%{q}%')) |
            (Student.user_id.ilike(f'%{q}%')) |
            (Student.email.ilike(f'%{q}%'))
        ).all()
    return render_template('librarian_students.html', students=students)

@app.route('/librarian/student/<int:student_id>')
def librarian_student_details(student_id):
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))
    student = Student.query.get_or_404(student_id)
    borrowed_books = Issue.query.filter_by(student_id=student_id, return_date=None).all()
    return render_template('librarian_student_details.html', student=student, borrowed_books=borrowed_books)

# Librarian bulk book upload
@app.route('/librarian/bulk_books', methods=['GET', 'POST'])
def librarian_bulk_books():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    
    if request.method == 'POST':
        if 'file' not in request.files:
            flash('No file selected!')
            return redirect(request.url)
        
        file = request.files['file']
        if file.filename == '':
            flash('No file selected!')
            return redirect(request.url)
        
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)
            
            try:
                df = pd.read_excel(filepath)
                required_columns = ['access_no', 'title', 'author', 'publisher', 'subject', 
                                  'department', 'category', 'location', 'copies']
                
                if not all(col in df.columns for col in required_columns):
                    flash(f'Excel file must contain columns: {", ".join(required_columns)}')
                    os.remove(filepath)
                    return redirect(request.url)
                
                created_books = []
                errors = []
                
                for index, row in df.iterrows():
                    try:
                        access_no = str(row['access_no']).strip()
                        
                        if Book.query.filter_by(access_no=access_no).first():
                            errors.append(f"Row {index + 2}: Access number {access_no} already exists")
                            continue
                        
                        copies = int(row['copies'])
                        
                        new_book = Book(
                            access_no=access_no,
                            title=str(row['title']).strip(),
                            author=str(row['author']).strip(),
                            publisher=str(row['publisher']).strip(),
                            subject=str(row['subject']).strip(),
                            department=str(row['department']).strip(),
                            category=str(row['category']).strip(),
                            location=str(row['location']).strip(),
                            copies=copies,
                            quantity=copies,
                            available_count=copies
                        )
                        
                        db.session.add(new_book)
                        created_books.append({
                            'access_no': access_no,
                            'title': str(row['title']).strip(),
                            'copies': copies
                        })
                        
                    except Exception as e:
                        errors.append(f"Row {index + 2}: {str(e)}")
                
                db.session.commit()
                os.remove(filepath)
                
                session['bulk_book_results'] = {
                    'created_books': created_books,
                    'errors': errors
                }
                
                flash(f'Bulk book creation completed! {len(created_books)} book(s) added.')
                return redirect(url_for('librarian_bulk_book_results'))
                
            except Exception as e:
                if os.path.exists(filepath):
                    os.remove(filepath)
                flash(f'Error processing file: {str(e)}')
                return redirect(request.url)
        else:
            flash('Invalid file type. Please upload an Excel file (.xlsx or .xls)')
    
    return render_template('librarian_bulk_books.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())

@app.route('/librarian/bulk_book_results')
def librarian_bulk_book_results():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    
    results = session.get('bulk_book_results')
    if not results:
        return redirect(url_for('librarian_bulk_books'))
    
    return render_template('librarian_bulk_book_results.html', results=results)

@app.route('/librarian/download_book_template')
def librarian_download_book_template():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login'))
    
    data = {
        'access_no': ['ACC00001', 'ACC00002', 'ACC00003'],
        'title': ['Introduction to Computer Science', 'Advanced Mathematics', 'Digital Electronics'],
        'author': ['John Smith', 'Jane Doe', 'Mike Johnson'],
        'publisher': ['Tech Publications', 'Academic Press', 'Engineering Books'],
        'subject': ['Computer Science', 'Mathematics', 'Electronics'],
        'department': ['CSE', 'CSE', 'ECE'],
        'category': ['Textbook', 'Reference', 'Laboratory Manual'],
        'location': ['Section A, Shelf 1', 'Section B, Shelf 2', 'Section C, Shelf 1'],
        'copies': [5, 3, 8]
    }
    
    df = pd.DataFrame(data)
    template_filename = 'book_template.xlsx'
    template_path = os.path.join(app.config['UPLOAD_FOLDER'], template_filename)
    
    with pd.ExcelWriter(template_path, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='Book Template')
    
    return redirect(f'/uploads/{template_filename}')

@app.route('/librarian/bulk_users', methods=['GET', 'POST'])
def librarian_bulk_users():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))
    
    if request.method == 'POST':
        if 'file' not in request.files:
            flash('No file selected')
            return redirect(request.url)
        
        file = request.files['file']
        if file.filename == '':
            flash('No file selected')
            return redirect(request.url)
        
        if file and allowed_file(file.filename):
            try:
                df = pd.read_excel(file)
                required_columns = ['user_id', 'name', 'department', 'designation', 'course', 'dob', 'current_year', 'validity_date']
                
                if not all(col in df.columns for col in required_columns):
                    flash(f'Missing required columns: {required_columns}')
                    return redirect(request.url)
                
                results = {
                    'total': len(df),
                    'successful': 0,
                    'failed': 0,
                    'skipped': 0,
                    'errors': []
                }
                
                for index, row in df.iterrows():
                    try:
                        # Check if student already exists
                        existing_student = Student.query.filter_by(user_id=row['user_id']).first()
                        if existing_student:
                            results['skipped'] += 1
                            results['errors'].append(f'Row {index + 1}: Student {row["user_id"]} already exists')
                            continue
                        
                        # Parse dates
                        dob = pd.to_datetime(row['dob']).date() if pd.notna(row['dob']) else None
                        validity_date = pd.to_datetime(row['validity_date']).date() if pd.notna(row['validity_date']) else None
                        
                        # Generate password (user_id + name)
                        name = str(row['name']).strip()
                        user_id = str(row['user_id']).strip()
                        clean_name = re.sub(r'[^a-zA-Z]', '', name.lower())
                        password = f"{user_id}{clean_name}"
                        
                        new_student = Student(
                            user_id=row['user_id'],
                            username=None,  # No longer using username - user_id is used for login
                            name=name,
                            roll_number=user_id,
                            password=generate_password_hash(password),
                            department=row['department'],
                            designation=row['designation'],
                            course=row['course'],
                            dob=dob,
                            current_year=int(row['current_year']) if pd.notna(row['current_year']) else None,
                            validity_date=validity_date
                        )
                        
                        db.session.add(new_student)
                        results['successful'] += 1
                        
                    except Exception as e:
                        results['failed'] += 1
                        results['errors'].append(f'Row {index + 1}: {str(e)}')
                
                db.session.commit()
                session['bulk_user_results'] = results
                flash(f'Processed {results["total"]} records: {results["successful"]} successful, {results["failed"]} failed, {results["skipped"]} skipped')
                return redirect(url_for('librarian_bulk_user_results'))
                
            except Exception as e:
                flash(f'Error processing file: {str(e)}')
                return redirect(request.url)
        else:
            flash('Invalid file format. Please upload an Excel file.')
            return redirect(request.url)
    
    return render_template('librarian_bulk_users.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())

@app.route('/librarian/bulk_user_results')
def librarian_bulk_user_results():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))
    
    results = session.get('bulk_user_results')
    if not results:
        return redirect(url_for('librarian_bulk_users'))
    
    return render_template('librarian_bulk_user_results.html', results=results)

@app.route('/librarian/bulk_users_template')
def librarian_bulk_users_template():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))
    
    data = {
        'user_id': ['2024001', '2024002', '2024003'],
        'name': ['John Doe', 'Jane Smith', 'Mike Johnson'],
        'roll_number': ['21CS001', '21IT002', '21ECE003'],
        'email': ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
        'department': ['CSE', 'IT', 'ECE'],
        'college': ['Engineering College', 'Engineering College', 'Engineering College'],
        'designation': ['Student', 'Student', 'Staff'],
        'course': ['B.Tech Computer Science', 'B.Tech Information Technology', 'B.Tech Electronics'],
        'dob': ['2003-05-15', '2003-08-20', '1995-12-10'],
        'current_year': [2, 2, 3],
        'validity_date': ['2026-05-14', '2026-05-14', '2025-12-31']
    }
    
    df = pd.DataFrame(data)
    
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='Students')
    
    output.seek(0)
    
    return send_file(
        output,
        as_attachment=True,
        download_name='student_template.xlsx',
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )

@app.route('/librarian/issue_return')
def librarian_issue_return():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))

    return render_template('librarian_issue_return.html')

@app.route('/librarian/issue_return_dashboard')
def librarian_issue_return_dashboard():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))

    return render_template('librarian_issue_return_dashboard.html')

@app.route('/librarian/issue_history')
def librarian_issue_history():
    if session.get('user_role') != 'librarian':
        return redirect(url_for('login', role='librarian'))
    issues = Issue.query.order_by(Issue.issue_date.desc()).all()
    from datetime import datetime
    now = datetime.utcnow()
    overdue_count = len([i for i in issues if i.return_date is None and i.due_date < now])
    return render_template('librarian_issue_history.html', issues=issues, overdue_count=overdue_count, now=now)

# API Routes for AJAX
@app.route('/api/search_books')
def api_search_books():
    search_query = request.args.get('q', '')
    books = Book.query.filter(
        (Book.title.contains(search_query)) |
        (Book.author.contains(search_query))
    ).limit(10).all()

    result = []
    for book in books:
        result.append({
            'book_id': book.book_id,
            'title': book.title,
            'author': book.author,
            'category': book.category,
            'available_count': book.available_count
        })

    return jsonify(result)

@app.route('/api/next_access_number')
def api_next_access_number():
    """API endpoint to get the next available access number"""
    try:
        next_number = get_next_access_number()
        return jsonify({'success': True, 'next_access_number': next_number})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/next_ebook_access_number')
def api_next_ebook_access_number():
    """API endpoint to get the next available e-book access number"""
    try:
        # Get the last e-book access number
        last_ebook = EBook.query.order_by(EBook.access_no.desc()).first()

        if not last_ebook:
            return jsonify({'success': True, 'next_access_number': '1'})

        # Extract numeric part from access number
        last_access = last_ebook.access_no
        import re
        numbers = re.findall(r'\d+', last_access)

        if numbers:
            last_number = int(numbers[-1])
            return jsonify({'success': True, 'next_access_number': str(last_number + 1)})
        else:
            return jsonify({'success': True, 'next_access_number': '1'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/search_books_detailed')
def api_search_books_detailed():
    """API endpoint for detailed book search by title or access number"""
    search_query = request.args.get('q', '').strip()

    if not search_query:
        return jsonify([])

    # Search by title, author, or access number
    books = Book.query.filter(
        db.or_(
            Book.title.ilike(f'%{search_query}%'),
            Book.author.ilike(f'%{search_query}%'),
            Book.access_no.ilike(f'%{search_query}%')
        )
    ).filter(Book.available_count > 0).limit(10).all()

    result = []
    for book in books:
        result.append({
            'book_id': book.book_id,
            'title': book.title,
            'author': book.author,
            'access_no': book.access_no,
            'category': book.category,
            'available_count': book.available_count,
            'publisher': book.publisher,
            'location': book.location
        })

    return jsonify(result)

@app.route('/download_ebook_template')
def download_ebook_template():
    """Generate and download e-book Excel template"""
    try:
        # Create sample data for template
        template_data = {
            'access_no': ['E001', 'E002'],
            'title': ['Sample E-Book 1', 'Sample E-Book 2'],
            'author': ['Author Name 1', 'Author Name 2'],
            'publisher': ['Publisher 1', 'Publisher 2'],
            'subject': ['Computer Science', 'Information Technology'],
            'department': ['CSE', 'IT'],
            'category': ['Textbook', 'Reference'],
            'file_format': ['PDF', 'EPUB'],
            'file_size': ['5.2 MB', '3.8 MB'],
            'download_url': ['https://example.com/ebook1.pdf', 'https://example.com/ebook2.epub'],
            'isbn': ['978-0-123456-78-9', '978-0-987654-32-1'],
            'pages': [250, 180],
            'language': ['English', 'English'],
            'description': ['Sample description 1', 'Sample description 2']
        }

        df = pd.DataFrame(template_data)

        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='E-Books Template')

            # Get the workbook and worksheet
            workbook = writer.book
            worksheet = writer.sheets['E-Books Template']

            # Auto-adjust column widths
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width

        output.seek(0)

        return send_file(
            output,
            as_attachment=True,
            download_name='ebook_template.xlsx',
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    except Exception as e:
        flash(f'Error generating template: {str(e)}')
        return redirect(request.referrer or url_for('admin_dashboard'))

# Librarian API Routes
@app.route('/librarian/api/user_details')
def librarian_api_user_details():
    if session.get('user_role') != 'librarian':
        return jsonify({'success': False, 'message': 'Unauthorized'}), 403

    user_id = request.args.get('user_id', '').strip()
    if not user_id:
        return jsonify({'success': False, 'message': 'User ID is required'})

    # Find student by user_id
    student = Student.query.filter_by(user_id=user_id).first()
    if not student:
        return jsonify({'success': False, 'message': 'Student not found'})

    # Get borrowed books
    borrowed_issues = Issue.query.filter_by(student_id=student.id, return_date=None).all()
    borrowed_books = []
    for issue in borrowed_issues:
        borrowed_books.append({
            'issue_id': issue.issue_id,
            'title': issue.book.title,
            'access_no': issue.book.access_no,
            'due_date': issue.due_date.strftime('%Y-%m-%d')
        })

    # Get available books
    available_books = Book.query.filter(Book.available_count > 0).all()
    books_list = []
    for book in available_books:
        books_list.append({
            'book_id': book.book_id,
            'title': book.title,
            'access_no': book.access_no
        })

    # Determine book limit
    if student.designation == 'Staff':
        book_limit = int(get_setting_value('staff_book_limit', 5))
    else:
        book_limit = int(get_setting_value('student_book_limit', 3))

    return jsonify({
        'success': True,
        'user': {
            'name': student.name,
            'user_id': student.user_id,
            'designation': student.designation
        },
        'borrowed': borrowed_books,
        'books': books_list,
        'limit': book_limit
    })

@app.route('/librarian/api/issue_book', methods=['POST'])
def librarian_api_issue_book():
    if session.get('user_role') != 'librarian':
        return jsonify({'success': False, 'message': 'Unauthorized'}), 403

    user_id = request.form.get('user_id', '').strip()
    book_id = request.form.get('book_id')

    if not user_id or not book_id:
        return jsonify({'success': False, 'message': 'User ID and Book ID are required'})

    # Find student and book
    student = Student.query.filter_by(user_id=user_id).first()
    book = Book.query.get(book_id)

    if not student:
        return jsonify({'success': False, 'message': 'Student not found'})

    if not book:
        return jsonify({'success': False, 'message': 'Book not found'})

    if book.available_count <= 0:
        return jsonify({'success': False, 'message': 'Book not available'})

    # Check book limit
    active_issues_count = Issue.query.filter_by(student_id=student.id, return_date=None).count()
    if student.designation == 'Staff':
        book_limit = int(get_setting_value('staff_book_limit', 5))
    else:
        book_limit = int(get_setting_value('student_book_limit', 3))

    if active_issues_count >= book_limit:
        return jsonify({'success': False, 'message': 'Maximum book limit reached'})

    # Create new issue
    issue = Issue(
        book_id=book_id,
        student_id=student.id,
        due_date=datetime.utcnow() + timedelta(days=14)
    )

    book.available_count -= 1

    db.session.add(issue)
    db.session.commit()

    return jsonify({'success': True, 'message': 'Book issued successfully'})

@app.route('/librarian/api/return_book', methods=['POST'])
def librarian_api_return_book():
    if session.get('user_role') != 'librarian':
        return jsonify({'success': False, 'message': 'Unauthorized'}), 403

    issue_id = request.form.get('issue_id')

    if not issue_id:
        return jsonify({'success': False, 'message': 'Issue ID is required'})

    issue = Issue.query.get(issue_id)

    if not issue:
        return jsonify({'success': False, 'message': 'Issue not found'})

    if issue.return_date:
        return jsonify({'success': False, 'message': 'Book already returned'})

    # Calculate fine
    return_date = datetime.utcnow()
    fine = calculate_fine(issue.due_date, return_date)

    # Update issue record
    issue.return_date = return_date
    issue.fine = fine

    # Update book availability
    book = Book.query.get(issue.book_id)
    book.available_count += 1

    db.session.commit()

    message = 'Book returned successfully'
    if fine > 0:
        message += f' (Fine: ₹{fine:.2f})'

    return jsonify({'success': True, 'message': message})

@app.route('/api/global_search')
def api_global_search():
    """Global search API for books, e-books, and students"""
    search_query = request.args.get('q', '').strip()
    search_type = request.args.get('type', 'all')  # all, books, ebooks, students

    if not search_query or len(search_query) < 2:
        return jsonify([])

    results = []

    # Search books
    if search_type in ['all', 'books']:
        books = Book.query.filter(
            db.or_(
                Book.title.ilike(f'%{search_query}%'),
                Book.author.ilike(f'%{search_query}%'),
                Book.access_no.ilike(f'%{search_query}%'),
                Book.subject.ilike(f'%{search_query}%')
            )
        ).limit(5).all()

        for book in books:
            results.append({
                'type': 'book',
                'id': book.book_id,
                'title': book.title,
                'subtitle': f'by {book.author}',
                'access_no': book.access_no,
                'available': book.available_count > 0,
                'icon': 'fas fa-book',
                'category': 'Physical Book'
            })

    # Search e-books
    if search_type in ['all', 'ebooks']:
        ebooks = EBook.query.filter(
            db.or_(
                EBook.title.ilike(f'%{search_query}%'),
                EBook.author.ilike(f'%{search_query}%'),
                EBook.access_no.ilike(f'%{search_query}%'),
                EBook.subject.ilike(f'%{search_query}%')
            )
        ).filter_by(is_active=True).limit(5).all()

        for ebook in ebooks:
            results.append({
                'type': 'ebook',
                'id': ebook.ebook_id,
                'title': ebook.title,
                'subtitle': f'by {ebook.author} ({ebook.file_format})',
                'access_no': ebook.access_no,
                'available': True,
                'icon': 'fas fa-tablet-alt',
                'category': 'E-Book'
            })

    # Search students (admin and librarian only)
    if search_type in ['all', 'students'] and session.get('user_role') in ['admin', 'librarian']:
        students = Student.query.filter(
            db.or_(
                Student.name.ilike(f'%{search_query}%'),
                Student.user_id.ilike(f'%{search_query}%'),
                Student.email.ilike(f'%{search_query}%'),
                Student.department.ilike(f'%{search_query}%')
            )
        ).limit(5).all()

        for student in students:
            results.append({
                'type': 'student',
                'id': student.id,
                'title': student.name,
                'subtitle': f'{student.user_id} - {student.department}',
                'access_no': student.user_id,
                'available': True,
                'icon': 'fas fa-graduation-cap',
                'category': 'Student'
            })

    return jsonify(results)

# Admin bulk book upload
@app.route('/admin/bulk_books', methods=['GET', 'POST'])
def admin_bulk_books():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    
    if request.method == 'POST':
        if 'file' not in request.files:
            flash('No file selected!')
            return redirect(request.url)
        
        file = request.files['file']
        if file.filename == '':
            flash('No file selected!')
            return redirect(request.url)
        
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)
            
            try:
                df = pd.read_excel(filepath)
                required_columns = ['access_no', 'title', 'author', 'publisher', 'subject', 
                                  'department', 'category', 'location', 'copies']
                
                if not all(col in df.columns for col in required_columns):
                    flash(f'Excel file must contain columns: {", ".join(required_columns)}')
                    os.remove(filepath)
                    return redirect(request.url)
                
                created_books = []
                errors = []
                
                for index, row in df.iterrows():
                    try:
                        access_no = str(row['access_no']).strip()
                        
                        if Book.query.filter_by(access_no=access_no).first():
                            errors.append(f"Row {index + 2}: Access number {access_no} already exists")
                            continue
                        
                        copies = int(row['copies'])
                        
                        new_book = Book(
                            access_no=access_no,
                            title=str(row['title']).strip(),
                            author=str(row['author']).strip(),
                            publisher=str(row['publisher']).strip(),
                            subject=str(row['subject']).strip(),
                            department=str(row['department']).strip(),
                            category=str(row['category']).strip(),
                            location=str(row['location']).strip(),
                            copies=copies,
                            quantity=copies,
                            available_count=copies
                        )
                        
                        db.session.add(new_book)
                        created_books.append({
                            'access_no': access_no,
                            'title': str(row['title']).strip(),
                            'copies': copies
                        })
                        
                    except Exception as e:
                        errors.append(f"Row {index + 2}: {str(e)}")
                
                db.session.commit()
                os.remove(filepath)
                
                session['bulk_book_results'] = {
                    'created_books': created_books,
                    'errors': errors
                }
                
                flash(f'Bulk book creation completed! {len(created_books)} book(s) added.')
                return redirect(url_for('admin_bulk_book_results'))
                
            except Exception as e:
                if os.path.exists(filepath):
                    os.remove(filepath)
                flash(f'Error processing file: {str(e)}')
                return redirect(request.url)
        else:
            flash('Invalid file type. Please upload an Excel file (.xlsx or .xls)')
    
    return render_template('admin_bulk_books.html', departments=get_departments_for_dropdown(), colleges=get_colleges_for_dropdown())

@app.route('/admin/bulk_book_results')
def admin_bulk_book_results():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    
    results = session.get('bulk_book_results')
    if not results:
        return redirect(url_for('admin_bulk_books'))
    
    return render_template('admin_bulk_book_results.html', results=results)

@app.route('/admin/download_book_template')
def admin_download_book_template():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    
    data = {
        'access_no': ['ACC00001', 'ACC00002', 'ACC00003'],
        'title': ['Introduction to Computer Science', 'Advanced Mathematics', 'Digital Electronics'],
        'author': ['John Smith', 'Jane Doe', 'Mike Johnson'],
        'publisher': ['Tech Publications', 'Academic Press', 'Engineering Books'],
        'subject': ['Computer Science', 'Mathematics', 'Electronics'],
        'department': ['CSE', 'CSE', 'ECE'],
        'category': ['Textbook', 'Reference', 'Laboratory Manual'],
        'location': ['Section A, Shelf 1', 'Section B, Shelf 2', 'Section C, Shelf 1'],
        'copies': [5, 3, 8]
    }
    
    df = pd.DataFrame(data)
    template_filename = 'book_template.xlsx'
    template_path = os.path.join(app.config['UPLOAD_FOLDER'], template_filename)
    
    with pd.ExcelWriter(template_path, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='Book Template')
    
    return redirect(f'/uploads/{template_filename}')

@app.route('/admin/bulk_users', methods=['GET', 'POST'])
def admin_bulk_users():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    
    if request.method == 'POST':
        # Check if file was uploaded
        if 'file' not in request.files:
            flash('No file selected!')
            return redirect(request.url)
        
        file = request.files['file']
        user_type = request.form.get('user_type')
        
        if file.filename == '':
            flash('No file selected!')
            return redirect(request.url)
        
        if not user_type or user_type not in ['librarian', 'student']:
            flash('Please select a valid user type!')
            return redirect(request.url)
        
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)
            
            try:
                # Read Excel file
                df = pd.read_excel(filepath)
                
                # Get college and department from form
                college_id = request.form.get('college') if user_type == 'student' else None
                department_code = request.form.get('department') if user_type == 'student' else None

                # Validate college and department for students
                if user_type == 'student':
                    if not college_id or not department_code:
                        flash('Please select both college and department for student uploads.')
                        os.remove(filepath)
                        return redirect(request.url)

                # Validate required columns
                if user_type == 'student':
                    required_columns = ['user_id', 'full_name', 'email', 'designation', 'validity_date']
                else:
                    required_columns = ['name', 'user_id', 'email']
                
                if not all(col in df.columns for col in required_columns):
                    flash(f'Excel file must contain columns: {", ".join(required_columns)}')
                    os.remove(filepath)
                    return redirect(request.url)
                
                created_users = []
                errors = []
                
                for index, row in df.iterrows():
                    try:
                        # Handle both 'name' and 'full_name' columns for backward compatibility
                        name = str(row.get('full_name', row.get('name', ''))).strip()
                        user_id = str(row['user_id']).strip()
                        email = str(row['email']).strip() if pd.notna(row['email']) else None
                        
                        # Generate username and password
                        generated_email, password = generate_username_password(name, user_id, user_type)
                        
                        # Use provided email or generated one
                        final_email = email if email else generated_email
                        
                        # Check if user already exists
                        if user_type == 'librarian':
                            existing_user = Librarian.query.filter_by(email=final_email).first()
                        else:
                            existing_user = Student.query.filter_by(email=final_email).first() or \
                                          Student.query.filter_by(roll_number=user_id).first() or \
                                          Student.query.filter_by(user_id=str(row['user_id']).strip()).first()
                        
                        if existing_user:
                            errors.append(f"Row {index + 2}: User already exists (duplicate user_id, email, or roll_number)")
                            continue
                        
                        # Create new user
                        if user_type == 'librarian':
                            new_user = Librarian(
                                name=name,
                                email=final_email,
                                password=generate_password_hash(password)
                            )
                        else:
                            # Parse date fields for student
                            try:
                                validity_date = pd.to_datetime(row['validity_date']).date()

                                # Check if validity date is in the future
                                if validity_date <= date.today():
                                    errors.append(f"Row {index + 2}: Validity date must be in the future")
                                    continue

                                # Get department information
                                department = Department.query.filter_by(
                                    college_id=college_id,
                                    code=department_code,
                                    is_active=True
                                ).first()

                                # Auto-generate course based on department
                                course = f"B.Tech {department.name}" if department else "B.Tech"

                                # Set default DOB for staff or calculate for students
                                designation = str(row['designation']).strip()
                                if designation == 'Staff':
                                    dob = date(1990, 1, 1)  # Default DOB for staff
                                    current_year = None
                                else:
                                    dob = date(2003, 1, 1)  # Default DOB for students
                                    current_year = 1  # Default to first year

                                new_user = Student(
                                    user_id=str(row['user_id']).strip(),
                                    username=None,  # No longer using username - user_id is used for login
                                    name=name,
                                    roll_number=user_id,
                                    email=final_email,
                                    password=generate_password_hash(password),
                                    college_id=int(college_id),
                                    department_id=department.id if department else None,
                                    department=department_code,  # Keep for backward compatibility
                                    college=College.query.get(college_id).name if college_id else None,
                                    designation=designation,
                                    course=course,
                                    dob=dob,
                                    current_year=current_year,
                                    validity_date=validity_date
                                )
                            except (ValueError, TypeError) as date_error:
                                errors.append(f"Row {index + 2}: Invalid date format or current_year - {str(date_error)}")
                                continue
                        
                        db.session.add(new_user)
                        created_users.append({
                            'name': name,
                            'email': final_email,
                            'password': password,
                            'user_id': user_id if user_type == 'student' else 'N/A',
                            'type': user_type,
                            'college': College.query.get(college_id).name if college_id and user_type == 'student' else 'N/A',
                            'department': department.name if user_type == 'student' and department else 'N/A',
                            'designation': designation if user_type == 'student' else 'Librarian'
                        })
                        
                    except Exception as e:
                        errors.append(f"Row {index + 2}: {str(e)}")
                
                # Commit all changes
                db.session.commit()
                
                # Clean up uploaded file
                os.remove(filepath)
                
                # Show results
                session['bulk_results'] = {
                    'created_users': created_users,
                    'errors': errors,
                    'user_type': user_type
                }
                
                flash(f'Bulk creation completed! {len(created_users)} {user_type}(s) created successfully.')
                return redirect(url_for('admin_bulk_results'))
                
            except Exception as e:
                # Clean up uploaded file
                if os.path.exists(filepath):
                    os.remove(filepath)
                flash(f'Error processing file: {str(e)}')
                return redirect(request.url)
        else:
            flash('Invalid file type. Please upload an Excel file (.xlsx or .xls)')
    
    return render_template('admin_bulk_users.html', colleges=get_colleges_for_dropdown())

@app.route('/admin/bulk_results')
def admin_bulk_results():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    
    results = session.get('bulk_results')
    if not results:
        return redirect(url_for('admin_bulk_users'))
    
    return render_template('admin_bulk_results.html', results=results)

@app.route('/admin/download_credentials')
def admin_download_credentials():
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))

    results = session.get('bulk_results')
    if not results or not results.get('created_users'):
        flash('No credentials available for download.')
        return redirect(url_for('admin_bulk_users'))

    # Create credentials Excel file
    credentials_data = []
    for user in results['created_users']:
        credentials_data.append({
            'User ID': user['user_id'],
            'Full Name': user['name'],
            'Email': user['email'],
            'Password': user['password'],
            'Type': user['type'].title(),
            'College': user['college'],
            'Department': user['department'],
            'Designation': user['designation']
        })

    df = pd.DataFrame(credentials_data)

    # Create Excel file in memory
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='User Credentials')

        # Format the Excel file
        workbook = writer.book
        worksheet = writer.sheets['User Credentials']

        # Auto-adjust column widths
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width

    output.seek(0)

    # Generate filename with timestamp
    from datetime import datetime
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f'user_credentials_{timestamp}.xlsx'

    return send_file(
        output,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        as_attachment=True,
        download_name=filename
    )

@app.route('/admin/download_template/<user_type>')
def admin_download_template(user_type):
    if session.get('user_role') != 'admin':
        return redirect(url_for('login'))
    
    if user_type not in ['librarian', 'student']:
        flash('Invalid user type!')
        return redirect(url_for('admin_bulk_users'))
    
    # Create sample data
    if user_type == 'librarian':
        data = {
            'name': ['John Doe', 'Jane Smith', 'Mike Johnson'],
            'roll_number': ['LIB001', 'LIB002', 'LIB003'],
            'email': ['<EMAIL>', '<EMAIL>', '']  # Third one will be auto-generated
        }
    else:
        data = {
            'user_id': ['STU001', 'STU002', 'STU003'],
            'full_name': ['Alice Brown', 'Bob Wilson', 'Carol Davis'],
            'email': ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
            'designation': ['Student', 'Student', 'Staff'],
            'validity_date': ['2026-05-14', '2026-05-14', '2030-12-31']
        }
    
    # Create DataFrame and save to Excel
    df = pd.DataFrame(data)
    template_filename = f'{user_type}_template.xlsx'
    template_path = os.path.join(app.config['UPLOAD_FOLDER'], template_filename)
    
    with pd.ExcelWriter(template_path, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name=f'{user_type.title()} Template')
    
    return redirect(f'/uploads/{template_filename}')

@app.route('/uploads/<filename>')
def uploaded_file(filename):
    """Serve uploaded files"""
    from flask import send_from_directory
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

if __name__ == '__main__':
    init_db()
    # Start the background cleanup scheduler
    schedule_cleanup()
    print("✅ Background cleanup scheduler started")
    app.run(debug=True)
