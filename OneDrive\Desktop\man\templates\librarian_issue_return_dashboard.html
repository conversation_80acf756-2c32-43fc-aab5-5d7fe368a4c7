{% extends "base.html" %}
{% block title %}Issue/Return Books - Librarian{% endblock %}
{% block content %}
<div class="container py-4">
    <h2 class="mb-4"><i class="fas fa-exchange-alt me-2"></i>Issue/Return Books</h2>
    <div class="card mb-4">
        <div class="card-body">
            <form id="userSearchForm" class="row g-3">
                <div class="col-md-6">
                    <label for="user_id" class="form-label">Enter Student/Staff User ID</label>
                    <input type="text" class="form-control" id="user_id" name="user_id" placeholder="User ID" required>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary">Search</button>
                </div>
            </form>
            <div id="userDetails" class="mt-4"></div>
        </div>
    </div>
    <div id="actionSection"></div>
</div>
{% endblock %}
{% block extra_js %}
<script>
$(function() {
    $('#userSearchForm').on('submit', function(e) {
        e.preventDefault();
        var userId = $('#user_id').val().trim();
        if (!userId) return;
        $('#userDetails').html('<div class="text-center"><span class="spinner-border"></span> Loading...</div>');
        $.get('/librarian/api/user_details', { user_id: userId }, function(data) {
            if (data.success) {
                var html = '<div class="card"><div class="card-body">';
                html += '<h5>' + data.user.name + ' (' + data.user.designation + ')</h5>';
                html += '<p>User ID: <strong>' + data.user.user_id + '</strong></p>';
                html += '<p>Borrowed Books: <strong>' + data.borrowed.length + '</strong> / ' + data.limit + '</p>';
                html += '<ul class="list-group mb-3">';
                if (data.borrowed.length > 0) {
                    data.borrowed.forEach(function(book) {
                        html += '<li class="list-group-item d-flex justify-content-between align-items-center">';
                        html += book.title + ' <span class="badge bg-info">Due: ' + book.due_date + '</span>';
                        html += '<button class="btn btn-sm btn-success ms-2 return-btn" data-issue="' + book.issue_id + '">Return</button>';
                        html += '</li>';
                    });
                } else {
                    html += '<li class="list-group-item text-muted">No books currently borrowed.</li>';
                }
                html += '</ul>';
                html += '<div class="mb-2">';
                html += '<label for="book_id" class="form-label">Issue New Book</label>';
                html += '<select class="form-select" id="book_id">';
                data.books.forEach(function(book) {
                    html += '<option value="' + book.book_id + '">' + book.title + ' (' + book.access_no + ')</option>';
                });
                html += '</select>';
                html += '<button class="btn btn-primary mt-2" id="issueBtn">Issue Book</button>';
                html += '</div>';
                html += '</div></div>';
                $('#userDetails').html(html);
            } else {
                $('#userDetails').html('<div class="alert alert-danger">' + data.message + '</div>');
            }
        });
    });
    $(document).on('click', '#issueBtn', function() {
        var userId = $('#user_id').val().trim();
        var bookId = $('#book_id').val();
        $.post('/librarian/api/issue_book', { user_id: userId, book_id: bookId }, function(data) {
            if (data.success) {
                $('#userSearchForm').submit();
                alert('Book issued successfully!');
            } else {
                alert(data.message);
            }
        });
    });
    $(document).on('click', '.return-btn', function() {
        var issueId = $(this).data('issue');
        $.post('/librarian/api/return_book', { issue_id: issueId }, function(data) {
            if (data.success) {
                $('#userSearchForm').submit();
                alert('Book returned successfully!');
            } else {
                alert(data.message);
            }
        });
    });
});
</script>
{% endblock %}
