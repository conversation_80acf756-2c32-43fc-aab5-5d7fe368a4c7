{% extends "base.html" %}

{% block title %}QB Financial Report - Admin{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>

<!-- Fixed Circulation Menu -->
<div class="nav-item">
    <a class="nav-link fw-bold text-primary" data-bs-toggle="collapse" href="#circulationSubmenu" role="button" aria-expanded="false">
        <i class="fas fa-exchange-alt me-2"></i>Circulation
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse" id="circulationSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_circulation_counter') }}">
                <i class="fas fa-desktop me-2"></i>Counter
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bulk_operations') }}">
                <i class="fas fa-layer-group me-2"></i>Bulk Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_payment_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Payment Management
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_management') }}">
                <i class="fas fa-tools me-2"></i>Binding & Maintenance
            </a>
        </div>
    </div>
</div>

<!-- Fixed Reports Menu -->
<div class="nav-item">
    <a class="nav-link fw-bold text-success" data-bs-toggle="collapse" href="#reportsSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-chart-bar me-2"></i>Reports
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="reportsSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_library_connection_report') }}">
                <i class="fas fa-wifi me-2"></i>Library Connection
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_access_register_report') }}">
                <i class="fas fa-sign-in-alt me-2"></i>Access Register
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bibliography_report') }}">
                <i class="fas fa-book-open me-2"></i>Bibliography
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_counter_report') }}">
                <i class="fas fa-calculator me-2"></i>Counter Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_statistics_report') }}">
                <i class="fas fa-chart-line me-2"></i>Statistics
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_report') }}">
                <i class="fas fa-tools me-2"></i>Binding Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_database_report') }}">
                <i class="fas fa-database me-2"></i>Database Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_member_report') }}">
                <i class="fas fa-users me-2"></i>Member Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_resource_report') }}">
                <i class="fas fa-archive me-2"></i>Resource Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_no_dues_report') }}">
                <i class="fas fa-check-circle me-2"></i>No Dues Report
            </a>
            <a class="nav-link py-1 active" href="{{ url_for('admin_qb_report') }}">
                <i class="fas fa-money-check-alt me-2"></i>QB Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_transfer_report') }}">
                <i class="fas fa-exchange-alt me-2"></i>Transfer Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_missing_report') }}">
                <i class="fas fa-exclamation-triangle me-2"></i>Missing Report
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-money-check-alt me-3 text-success"></i>QB Financial Report</h2>
        <p class="text-muted mb-0">Financial summaries, fine collection reports, revenue analysis, and budget utilization</p>
    </div>
    <div>
        <button class="btn btn-success" onclick="exportReport('excel')">
            <i class="fas fa-file-excel me-2"></i>Export Excel
        </button>
        <button class="btn btn-danger ms-2" onclick="exportReport('pdf')">
            <i class="fas fa-file-pdf me-2"></i>Export PDF
        </button>
    </div>
</div>

<!-- Financial Overview Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-success">
                    <i class="fas fa-money-bill-wave fa-3x mb-3"></i>
                </div>
                <h3 class="text-success" id="totalRevenue">₹0</h3>
                <p class="text-muted mb-0">Total Revenue</p>
                <small class="text-success">
                    <i class="fas fa-arrow-up me-1"></i>
                    <span id="revenueGrowth">+0%</span> from last period
                </small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-primary">
                    <i class="fas fa-coins fa-3x mb-3"></i>
                </div>
                <h3 class="text-primary" id="finesCollected">₹0</h3>
                <p class="text-muted mb-0">Fines Collected</p>
                <small class="text-primary">
                    <i class="fas fa-info-circle me-1"></i>
                    Real-time data
                </small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-warning">
                    <i class="fas fa-clock fa-3x mb-3"></i>
                </div>
                <h3 class="text-warning" id="pendingFines">₹0</h3>
                <p class="text-muted mb-0">Pending Fines</p>
                <small class="text-warning">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    Outstanding amount
                </small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-info">
                    <i class="fas fa-chart-line fa-3x mb-3"></i>
                </div>
                <h3 class="text-info" id="budgetUtilized">₹0</h3>
                <p class="text-muted mb-0">Budget Utilized</p>
                <small class="text-info">
                    <i class="fas fa-percentage me-1"></i>
                    <span id="budgetPercentage">0%</span> of total budget
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Filter Section -->
<div class="card autolib-card mb-4">
    <div class="card-header bg-gradient-success text-white">
        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Financial Report Filters</h5>
    </div>
    <div class="card-body">
        <form id="filterForm">
            <div class="row">
                <div class="col-md-3">
                    <label for="reportPeriod" class="form-label">Report Period</label>
                    <select class="form-select" id="reportPeriod" name="reportPeriod" onchange="updateDateRange()">
                        <option value="custom">Custom Range</option>
                        <option value="today">Today</option>
                        <option value="week">This Week</option>
                        <option value="month" selected>This Month</option>
                        <option value="quarter">This Quarter</option>
                        <option value="year">This Year</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="dateFrom" class="form-label">From Date</label>
                    <input type="date" class="form-control" id="dateFrom" name="dateFrom">
                </div>
                <div class="col-md-3">
                    <label for="dateTo" class="form-label">To Date</label>
                    <input type="date" class="form-control" id="dateTo" name="dateTo">
                </div>
                <div class="col-md-3">
                    <label for="revenueType" class="form-label">Revenue Type</label>
                    <select class="form-select" id="revenueType" name="revenueType">
                        <option value="">All Revenue</option>
                        <option value="fines">Fines Only</option>
                        <option value="membership">Membership Fees</option>
                        <option value="services">Service Charges</option>
                        <option value="other">Other Income</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-4">
                    <label for="department" class="form-label">Department</label>
                    <select class="form-select" id="department" name="department">
                        <option value="">All Departments</option>
                        <option value="CSE">Computer Science</option>
                        <option value="IT">Information Technology</option>
                        <option value="ECE">Electronics & Communication</option>
                        <option value="EEE">Electrical & Electronics</option>
                        <option value="MECH">Mechanical</option>
                        <option value="CIVIL">Civil</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="paymentMethod" class="form-label">Payment Method</label>
                    <select class="form-select" id="paymentMethod" name="paymentMethod">
                        <option value="">All Methods</option>
                        <option value="cash">Cash</option>
                        <option value="card">Card</option>
                        <option value="upi">UPI</option>
                        <option value="online">Online Transfer</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="amountRange" class="form-label">Amount Range</label>
                    <select class="form-select" id="amountRange" name="amountRange">
                        <option value="">All Amounts</option>
                        <option value="0-100">₹0 - ₹100</option>
                        <option value="100-500">₹100 - ₹500</option>
                        <option value="500-1000">₹500 - ₹1000</option>
                        <option value="1000+">₹1000+</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button type="button" class="btn btn-success" onclick="generateReport()">
                        <i class="fas fa-search me-2"></i>Generate Report
                    </button>
                    <button type="button" class="btn btn-outline-secondary ms-2" onclick="resetFilters()">
                        <i class="fas fa-refresh me-2"></i>Reset
                    </button>
                    <button type="button" class="btn btn-primary ms-2" onclick="generateInvoice()">
                        <i class="fas fa-file-invoice me-2"></i>Generate Invoice
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Financial Analysis Tabs -->
<ul class="nav nav-tabs mb-4" id="financialTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="revenue-summary-tab" data-bs-toggle="tab" data-bs-target="#revenue-summary" type="button" role="tab">
            <i class="fas fa-chart-pie me-2"></i>Revenue Summary
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="fine-collection-tab" data-bs-toggle="tab" data-bs-target="#fine-collection" type="button" role="tab">
            <i class="fas fa-money-bill-wave me-2"></i>Fine Collection
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="budget-analysis-tab" data-bs-toggle="tab" data-bs-target="#budget-analysis" type="button" role="tab">
            <i class="fas fa-chart-bar me-2"></i>Budget Analysis
        </button>
    </li>
</ul>

<div class="tab-content" id="financialTabContent">
    <!-- Revenue Summary Tab -->
    <div class="tab-pane fade show active" id="revenue-summary" role="tabpanel">
        <div class="row">
            <div class="col-md-6">
                <div class="card autolib-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Revenue Breakdown</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="revenueChart" height="300"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card autolib-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Monthly Revenue Trend</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="revenueTrendChart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card autolib-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-table me-2"></i>Revenue Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="revenueTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Date</th>
                                        <th>Transaction ID</th>
                                        <th>Type</th>
                                        <th>Student</th>
                                        <th>Department</th>
                                        <th>Amount (₹)</th>
                                        <th>Payment Method</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td colspan="8" class="text-center text-muted">
                                            <i class="fas fa-info-circle me-2"></i>No revenue data available. Start collecting fines to see financial reports.
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Fine Collection Tab -->
    <div class="tab-pane fade" id="fine-collection" role="tabpanel">
        <div class="card autolib-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-money-bill-wave me-2"></i>Fine Collection Analysis</h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h4 class="text-success" id="totalFinesCollected">₹0</h4>
                                <p class="text-muted mb-0">Total Collected</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h4 class="text-warning" id="totalFinesPending">₹0</h4>
                                <p class="text-muted mb-0">Pending Collection</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h4 class="text-info" id="avgFineAmount">₹0</h4>
                                <p class="text-muted mb-0">Average Fine</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h4 class="text-primary" id="collectionRate">0%</h4>
                                <p class="text-muted mb-0">Collection Rate</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Student ID</th>
                                <th>Name</th>
                                <th>Department</th>
                                <th>Fine Reason</th>
                                <th>Amount (₹)</th>
                                <th>Due Date</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="fineCollectionTable">
                            <tr>
                                <td colspan="8" class="text-center text-success">
                                    <i class="fas fa-check-circle me-2"></i>No fines to collect. All students are clear!
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Budget Analysis Tab -->
    <div class="tab-pane fade" id="budget-analysis" role="tabpanel">
        <div class="card autolib-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Budget Utilization Analysis</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <canvas id="budgetChart" height="300"></canvas>
                    </div>
                    <div class="col-md-6">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Category</th>
                                        <th>Allocated (₹)</th>
                                        <th>Spent (₹)</th>
                                        <th>Remaining (₹)</th>
                                        <th>Utilization %</th>
                                    </tr>
                                </thead>
                                <tbody id="budgetTable">
                                    <tr>
                                        <td>Books & Resources</td>
                                        <td>0</td>
                                        <td>0</td>
                                        <td>0</td>
                                        <td>0%</td>
                                    </tr>
                                    <tr>
                                        <td>Digital Resources</td>
                                        <td>0</td>
                                        <td>0</td>
                                        <td>0</td>
                                        <td>0%</td>
                                    </tr>
                                    <tr>
                                        <td>Maintenance</td>
                                        <td>0</td>
                                        <td>0</td>
                                        <td>0</td>
                                        <td>0%</td>
                                    </tr>
                                    <tr>
                                        <td>Operations</td>
                                        <td>0</td>
                                        <td>0</td>
                                        <td>0</td>
                                        <td>0%</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // Set default dates (current month)
    updateDateRange();
    
    // Load initial data
    generateReport();
    
    // Initialize charts
    initializeFinancialCharts();
});

function updateDateRange() {
    const period = $('#reportPeriod').val();
    const today = new Date();
    let fromDate, toDate;
    
    switch(period) {
        case 'today':
            fromDate = toDate = today;
            break;
        case 'week':
            fromDate = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            toDate = today;
            break;
        case 'month':
            fromDate = new Date(today.getFullYear(), today.getMonth(), 1);
            toDate = today;
            break;
        case 'quarter':
            fromDate = new Date(today.getFullYear(), Math.floor(today.getMonth() / 3) * 3, 1);
            toDate = today;
            break;
        case 'year':
            fromDate = new Date(today.getFullYear(), 0, 1);
            toDate = today;
            break;
        default:
            return; // Custom range - don't change dates
    }
    
    $('#dateFrom').val(fromDate.toISOString().split('T')[0]);
    $('#dateTo').val(toDate.toISOString().split('T')[0]);
}

function generateReport() {
    const filters = {
        reportPeriod: $('#reportPeriod').val(),
        dateFrom: $('#dateFrom').val(),
        dateTo: $('#dateTo').val(),
        revenueType: $('#revenueType').val(),
        department: $('#department').val(),
        paymentMethod: $('#paymentMethod').val(),
        amountRange: $('#amountRange').val()
    };
    
    // Show loading
    $('#revenueTable tbody').html('<tr><td colspan="8" class="text-center"><i class="fas fa-spinner fa-spin me-2"></i>Loading financial data...</td></tr>');
    
    // Simulate API call - replace with actual implementation
    setTimeout(() => {
        loadFinancialData(filters);
    }, 1000);
}

function loadFinancialData(filters) {
    // Since database is clean, show zero data
    const financialData = []; // Empty array for clean database
    
    // Update financial overview - all zeros for clean database
    $('#totalRevenue').text('₹0');
    $('#finesCollected').text('₹0');
    $('#pendingFines').text('₹0');
    $('#budgetUtilized').text('₹0');
    $('#revenueGrowth').text('+0%');
    $('#budgetPercentage').text('0%');
    
    // Update fine collection metrics
    $('#totalFinesCollected').text('₹0');
    $('#totalFinesPending').text('₹0');
    $('#avgFineAmount').text('₹0');
    $('#collectionRate').text('100%');
    
    // Update revenue table
    if (financialData.length === 0) {
        $('#revenueTable tbody').html(`
            <tr>
                <td colspan="8" class="text-center text-info">
                    <i class="fas fa-info-circle me-2"></i>No revenue data available. Start collecting fines to see financial reports.
                </td>
            </tr>
        `);
        
        $('#fineCollectionTable').html(`
            <tr>
                <td colspan="8" class="text-center text-success">
                    <i class="fas fa-check-circle me-2"></i>No fines to collect. All students are clear!
                </td>
            </tr>
        `);
    }
    
    // Update charts with zero data
    updateFinancialCharts([]);
}

function initializeFinancialCharts() {
    // Revenue breakdown chart
    const ctx1 = document.getElementById('revenueChart').getContext('2d');
    window.revenueChart = new Chart(ctx1, {
        type: 'doughnut',
        data: {
            labels: ['Fines', 'Membership', 'Services', 'Other'],
            datasets: [{
                data: [0, 0, 0, 0],
                backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    
    // Revenue trend chart
    const ctx2 = document.getElementById('revenueTrendChart').getContext('2d');
    window.revenueTrendChart = new Chart(ctx2, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'Revenue (₹)',
                data: [0, 0, 0, 0, 0, 0],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // Budget chart
    const ctx3 = document.getElementById('budgetChart').getContext('2d');
    window.budgetChart = new Chart(ctx3, {
        type: 'bar',
        data: {
            labels: ['Books', 'Digital', 'Maintenance', 'Operations'],
            datasets: [{
                label: 'Allocated',
                data: [0, 0, 0, 0],
                backgroundColor: 'rgba(54, 162, 235, 0.8)'
            }, {
                label: 'Spent',
                data: [0, 0, 0, 0],
                backgroundColor: 'rgba(255, 99, 132, 0.8)'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function updateFinancialCharts(data) {
    if (window.revenueChart && data.length === 0) {
        window.revenueChart.data.datasets[0].data = [0, 0, 0, 0];
        window.revenueChart.update();
    }
    
    if (window.revenueTrendChart && data.length === 0) {
        window.revenueTrendChart.data.datasets[0].data = [0, 0, 0, 0, 0, 0];
        window.revenueTrendChart.update();
    }
    
    if (window.budgetChart && data.length === 0) {
        window.budgetChart.data.datasets[0].data = [0, 0, 0, 0];
        window.budgetChart.data.datasets[1].data = [0, 0, 0, 0];
        window.budgetChart.update();
    }
}

function resetFilters() {
    $('#filterForm')[0].reset();
    $('#reportPeriod').val('month');
    updateDateRange();
    generateReport();
}

function generateInvoice() {
    alert('Invoice generation functionality would be implemented here.');
}

function exportReport(format) {
    alert(`Exporting QB Financial Report as ${format.toUpperCase()}...`);
}
</script>
{% endblock %}
