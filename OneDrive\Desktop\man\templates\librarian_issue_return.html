{% extends "base.html" %}

{% block title %}Issue/Return Books - Librarian{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('librarian_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>
<a class="nav-link" href="{{ url_for('librarian_books') }}">
    <i class="fas fa-book me-2"></i>Manage Books
</a>
<a class="nav-link" href="{{ url_for('librarian_students') }}">
    <i class="fas fa-graduation-cap me-2"></i>Manage Students
</a>
<a class="nav-link" href="{{ url_for('librarian_bulk_users') }}">
    <i class="fas fa-upload me-2"></i>Bulk Create Users
</a>
<a class="nav-link" href="{{ url_for('librarian_bulk_books') }}">
    <i class="fas fa-file-upload me-2"></i>Bulk Add Books
</a>
<a class="nav-link active" href="{{ url_for('librarian_issue_return') }}">
    <i class="fas fa-exchange-alt me-2"></i>Issue/Return Books
</a>
<a class="nav-link" href="{{ url_for('librarian_issue_history') }}">
    <i class="fas fa-history me-2"></i>Issue History
</a>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-exchange-alt me-3"></i>Issue/Return Books</h2>
    <a href="{{ url_for('librarian_issue_history') }}" class="btn btn-info btn-custom">
        <i class="fas fa-history me-2"></i>View History
    </a>
</div>

<div class="row">
    <!-- Issue Book Section -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-hand-holding me-2"></i>Issue Book</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('librarian_issue_book') }}">
                    <div class="mb-3">
                        <label for="student_id_issue" class="form-label">
                            <i class="fas fa-user me-2"></i>Student ID
                        </label>
                        <input type="text" class="form-control" id="student_id_issue" name="student_id" required 
                               placeholder="Enter student ID">
                    </div>
                    
                    <div class="mb-3">
                        <label for="book_id_issue" class="form-label">
                            <i class="fas fa-book me-2"></i>Book ID
                        </label>
                        <input type="text" class="form-control" id="book_id_issue" name="book_id" required 
                               placeholder="Enter book ID or access number">
                    </div>
                    
                    <div class="mb-3">
                        <label for="due_date" class="form-label">
                            <i class="fas fa-calendar me-2"></i>Due Date
                        </label>
                        <input type="date" class="form-control" id="due_date" name="due_date" required>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-hand-holding me-2"></i>Issue Book
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Return Book Section -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="fas fa-undo me-2"></i>Return Book</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('librarian_return_book') }}">
                    <div class="mb-3">
                        <label for="student_id_return" class="form-label">
                            <i class="fas fa-user me-2"></i>Student ID
                        </label>
                        <input type="text" class="form-control" id="student_id_return" name="student_id" required 
                               placeholder="Enter student ID">
                    </div>
                    
                    <div class="mb-3">
                        <label for="book_id_return" class="form-label">
                            <i class="fas fa-book me-2"></i>Book ID
                        </label>
                        <input type="text" class="form-control" id="book_id_return" name="book_id" required 
                               placeholder="Enter book ID or access number">
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-undo me-2"></i>Return Book
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center border-primary">
            <div class="card-body">
                <i class="fas fa-book fa-2x text-primary mb-2"></i>
                <h4 class="text-primary">{{ total_books or 0 }}</h4>
                <p class="mb-0">Total Books</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-success">
            <div class="card-body">
                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                <h4 class="text-success">{{ available_books or 0 }}</h4>
                <p class="mb-0">Available</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-warning">
            <div class="card-body">
                <i class="fas fa-hand-holding fa-2x text-warning mb-2"></i>
                <h4 class="text-warning">{{ issued_books or 0 }}</h4>
                <p class="mb-0">Issued</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-danger">
            <div class="card-body">
                <i class="fas fa-exclamation-triangle fa-2x text-danger mb-2"></i>
                <h4 class="text-danger">{{ overdue_books or 0 }}</h4>
                <p class="mb-0">Overdue</p>
            </div>
        </div>
    </div>
</div>

<!-- Recently Issued Books -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Recently Issued Books</h5>
    </div>
    <div class="card-body">
        {% if recent_issues %}
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Student ID</th>
                        <th>Student Name</th>
                        <th>Book Title</th>
                        <th>Issue Date</th>
                        <th>Due Date</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    {% for issue in recent_issues %}
                    <tr>
                        <td>{{ issue.student.user_id }}</td>
                        <td>{{ issue.student.username }}</td>
                        <td>{{ issue.book.title }}</td>
                        <td>{{ issue.issue_date.strftime('%d-%m-%Y') }}</td>
                        <td>{{ issue.due_date.strftime('%d-%m-%Y') }}</td>
                        <td>
                            {% if issue.return_date %}
                                <span class="badge bg-success">Returned</span>
                            {% elif issue.due_date < today %}
                                <span class="badge bg-danger">Overdue</span>
                            {% else %}
                                <span class="badge bg-warning">Issued</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-4">
            <i class="fas fa-clock fa-3x text-muted mb-3"></i>
            <h6 class="text-muted">No recent book issues</h6>
            <p class="text-muted">Start issuing books to see them here.</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Set default due date to 14 days from today
    const today = new Date();
    const defaultDueDate = new Date(today.getTime() + (14 * 24 * 60 * 60 * 1000));
    const dueDateString = defaultDueDate.toISOString().split('T')[0];
    $('#due_date').val(dueDateString);
    
    // Auto-search functionality for student and book IDs
    $('#student_id_issue, #student_id_return').on('input', function() {
        const studentId = $(this).val();
        if (studentId.length > 2) {
            // Could implement auto-complete here
        }
    });
    
    $('#book_id_issue, #book_id_return').on('input', function() {
        const bookId = $(this).val();
        if (bookId.length > 2) {
            // Could implement auto-complete here
        }
    });
});
</script>
{% endblock %}
