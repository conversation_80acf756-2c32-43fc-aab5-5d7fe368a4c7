{% extends "base.html" %}

{% block title %}Add Student - Admin{% endblock %}

{% block sidebar %}
<!-- Dashboard -->
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>

<!-- Management Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-info" data-bs-toggle="collapse" href="#managementSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-cogs me-2"></i>Management
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="managementSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_books') }}">
                <i class="fas fa-book me-2"></i>Manage Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_ebooks') }}">
                <i class="fas fa-tablet-alt me-2"></i>Manage E-Books
            </a>
            <a class="nav-link py-1 active" href="{{ url_for('admin_students') }}">
                <i class="fas fa-graduation-cap me-2"></i>Manage Students
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_librarians') }}">
                <i class="fas fa-user-tie me-2"></i>Manage Librarians
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_colleges') }}">
                <i class="fas fa-university me-2"></i>Manage Colleges
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_departments') }}">
                <i class="fas fa-building me-2"></i>Manage Departments
            </a>
        </div>
    </div>
</div>

<!-- Circulation Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-primary" data-bs-toggle="collapse" href="#circulationSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-exchange-alt me-2"></i>Circulation
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="circulationSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_circulation_counter') }}">
                <i class="fas fa-desktop me-2"></i>Counter Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_issue_history') }}">
                <i class="fas fa-history me-2"></i>Issue History
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bulk_operations') }}">
                <i class="fas fa-layer-group me-2"></i>Bulk Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_payment_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Payment Management
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_management') }}">
                <i class="fas fa-tools me-2"></i>Binding & Maintenance
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_fine_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Fine Management
            </a>
        </div>
    </div>
</div>

<!-- Reports Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-success" data-bs-toggle="collapse" href="#reportsSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-chart-bar me-2"></i>Reports
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="reportsSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_library_connection_report') }}">
                <i class="fas fa-wifi me-2"></i>Library Connection
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_access_register_report') }}">
                <i class="fas fa-sign-in-alt me-2"></i>Access Register
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bibliography_report') }}">
                <i class="fas fa-book-open me-2"></i>Bibliography
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_counter_report') }}">
                <i class="fas fa-calculator me-2"></i>Counter Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_statistics_report') }}">
                <i class="fas fa-chart-line me-2"></i>Statistics
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_report') }}">
                <i class="fas fa-tools me-2"></i>Binding Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_database_report') }}">
                <i class="fas fa-database me-2"></i>Database Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_member_report') }}">
                <i class="fas fa-users me-2"></i>Member Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_resource_report') }}">
                <i class="fas fa-archive me-2"></i>Resource Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_no_dues_report') }}">
                <i class="fas fa-check-circle me-2"></i>No Dues Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_qb_report') }}">
                <i class="fas fa-money-check-alt me-2"></i>QB Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_transfer_report') }}">
                <i class="fas fa-exchange-alt me-2"></i>Transfer Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_missing_report') }}">
                <i class="fas fa-exclamation-triangle me-2"></i>Missing Report
            </a>
        </div>
    </div>
</div>

<!-- System Administration -->
<div class="nav-item">
    <a class="nav-link fw-bold text-warning" data-bs-toggle="collapse" href="#systemSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-server me-2"></i>System
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="systemSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_settings') }}">
                <i class="fas fa-cog me-2"></i>System Settings
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-user-plus me-3"></i>Add New Student</h2>
    <div>
        <a href="{{ url_for('admin_bulk_users') }}" class="btn btn-info btn-custom me-2">
            <i class="fas fa-upload me-2"></i>Bulk Upload Students
        </a>
        <a href="{{ url_for('admin_students') }}" class="btn btn-secondary btn-custom">
            <i class="fas fa-arrow-left me-2"></i>Back to Students
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user-graduate me-2"></i>Student Information</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <!-- User ID and Username -->
                        <div class="col-md-6 mb-3">
                            <label for="user_id" class="form-label">
                                <i class="fas fa-id-card me-2"></i>User ID (Roll No/Staff ID) <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="user_id" name="user_id" required placeholder="e.g., 21CS001 or STAFF001">
                            <div class="form-text">For students: Enter roll number. For staff: Enter staff ID</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <!-- Username field removed - using user_id for login -->
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Login Info:</strong> Students will use their User ID to log in to the system.
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Name and Email -->
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">
                                <i class="fas fa-user me-2"></i>Full Name <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="name" name="name" required placeholder="Enter full name">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope me-2"></i>Email <span class="text-danger">*</span>
                            </label>
                            <input type="email" class="form-control" id="email" name="email" required placeholder="<EMAIL>">
                        </div>
                    </div>

                    <div class="row">
                        <!-- Password -->
                        <div class="col-md-6 mb-3">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock me-2"></i>Password <span class="text-danger">*</span>
                            </label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="password" name="password" required placeholder="Enter password">
                                <button class="btn btn-outline-secondary" type="button" id="generatePassword">
                                    <i class="fas fa-magic"></i> Generate
                                </button>
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="form-text">Or use auto-generate (name+rollnumber format)</div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- College and Department -->
                        <div class="col-md-6 mb-3">
                            <label for="college" class="form-label">
                                <i class="fas fa-university me-2"></i>College <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="college" name="college" required>
                                <option value="">Select College</option>
                                {% for college_id, college_name in colleges %}
                                <option value="{{ college_id }}">{{ college_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="department" class="form-label">
                                <i class="fas fa-building me-2"></i>Department <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="department" name="department" required disabled>
                                <option value="">Select College First</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Designation -->
                        <div class="col-md-6 mb-3">
                            <label for="designation" class="form-label">
                                <i class="fas fa-user-tag me-2"></i>Designation <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="designation" name="designation" required>
                                <option value="">Select Designation</option>
                                {% for design_code, design_name in designations %}
                                <option value="{{ design_code }}">{{ design_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <!-- Empty column for spacing -->
                        </div>
                    </div>

                    <div class="row">
                        <!-- Course -->
                        <div class="col-md-6 mb-3">
                            <label for="course" class="form-label">
                                <i class="fas fa-graduation-cap me-2"></i>Course <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="course" name="course" required placeholder="e.g., B.Tech Computer Science">
                        </div>
                        <div class="col-md-6 mb-3">
                            <!-- Empty column for spacing -->
                        </div>
                    </div>

                    <div class="row">
                        <!-- Date of Birth and Current Year -->
                        <div class="col-md-6 mb-3">
                            <label for="dob" class="form-label">
                                <i class="fas fa-calendar-alt me-2"></i>Date of Birth <span class="text-danger">*</span>
                            </label>
                            <input type="date" class="form-control" id="dob" name="dob" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="current_year" class="form-label">
                                <i class="fas fa-calendar-check me-2"></i>Current Year <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="current_year" name="current_year" required>
                                <option value="">Select Year</option>
                                <option value="1">1st Year</option>
                                <option value="2">2nd Year</option>
                                <option value="3">3rd Year</option>
                                <option value="4">4th Year</option>
                                <option value="5">5th Year</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Validity Date -->
                        <div class="col-md-6 mb-3">
                            <label for="validity_date" class="form-label">
                                <i class="fas fa-calendar-times me-2"></i>Validity Date <span class="text-danger">*</span>
                            </label>
                            <input type="date" class="form-control" id="validity_date" name="validity_date" required>
                            <div class="form-text text-warning">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                Account will be automatically deleted after this date
                            </div>
                        </div>
                        <div class="col-md-6 mb-3 d-flex align-items-end">
                            <button type="button" class="btn btn-outline-info btn-sm" onclick="setDefaultValidity()">
                                <i class="fas fa-calendar-plus me-2"></i>Set Default (1 Year)
                            </button>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('admin_students') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Add Student
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Guidelines</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-lightbulb me-2"></i>Quick Tips:</h6>
                    <ul class="mb-0 small">
                        <li>User ID should be unique (e.g., STU001, STU002)</li>
                        <li>Username for login (no spaces allowed)</li>
                        <li>Auto-generate password using userid+name format</li>
                        <li>Choose appropriate department from dropdown</li>
                        <li>Validity date determines account expiry</li>
                        <li>Expired accounts are automatically deleted</li>
                    </ul>
                </div>

                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Important:</h6>
                    <ul class="mb-0 small">
                        <li>All fields marked with <span class="text-danger">*</span> are required</li>
                        <li>Email addresses must be unique</li>
                        <li>Roll numbers must be unique</li>
                        <li>Validity date must be in the future</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-generate password
    $('#generatePassword').click(function() {
        const name = $('#name').val().replace(/[^a-zA-Z]/g, '').toLowerCase();
        const userId = $('#user_id').val().replace(/[^a-zA-Z0-9]/g, '');
        
        if (name && userId) {
            $('#password').val(userId + name);
        } else {
            alert('Please enter both user ID and name first.');
        }
    });
    
    // Toggle password visibility
    $('#togglePassword').click(function() {
        const passwordField = $('#password');
        const icon = $(this).find('i');
        
        if (passwordField.attr('type') === 'password') {
            passwordField.attr('type', 'text');
            icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            passwordField.attr('type', 'password');
            icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });
    
    // Load departments when college is selected
    $('#college').change(function() {
        const collegeId = $(this).val();
        const departmentSelect = $('#department');

        if (collegeId) {
            // Enable department dropdown and show loading
            departmentSelect.prop('disabled', false);
            departmentSelect.html('<option value="">Loading departments...</option>');

            // Fetch departments for selected college
            $.ajax({
                url: `/api/departments/${collegeId}`,
                method: 'GET',
                success: function(departments) {
                    departmentSelect.html('<option value="">Select Department</option>');
                    departments.forEach(function(dept) {
                        departmentSelect.append(`<option value="${dept.code}">${dept.code} - ${dept.name}</option>`);
                    });
                },
                error: function() {
                    departmentSelect.html('<option value="">Error loading departments</option>');
                }
            });
        } else {
            // Disable department dropdown if no college selected
            departmentSelect.prop('disabled', true);
            departmentSelect.html('<option value="">Select College First</option>');
        }
    });

    // Auto-fill course based on department selection
    $('#department').change(function() {
        const department = $(this).val();
        const departmentText = $(this).find('option:selected').text();

        if (department) {
            const courseName = departmentText.split(' - ')[1];
            $('#course').val('B.Tech ' + courseName);
        }
    });

    // Handle designation change - disable fields for staff
    $('#designation').change(function() {
        const designation = $(this).val();
        const currentYearField = $('#current_year');
        const validityDateField = $('#validity_date');

        if (designation === 'Staff') {
            // Disable current year and validity date for staff
            currentYearField.prop('disabled', true).val('');
            validityDateField.prop('disabled', true).val('');

            // Set default validity date for staff (5 years from now)
            const today = new Date();
            today.setFullYear(today.getFullYear() + 5);
            const staffValidityDate = today.toISOString().split('T')[0];
            validityDateField.val(staffValidityDate);
        } else {
            // Enable fields for students
            currentYearField.prop('disabled', false);
            validityDateField.prop('disabled', false);

            // Set default validity date for students (1 year from now)
            setDefaultValidity();
        }
    });

    // Trigger designation change on page load
    $('#designation').trigger('change');
});

function setDefaultValidity() {
    const today = new Date();
    today.setFullYear(today.getFullYear() + 1); // Add 1 year
    const defaultDate = today.toISOString().split('T')[0];
    $('#validity_date').val(defaultDate);
}
</script>
{% endblock %}
