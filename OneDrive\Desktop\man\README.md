# Smart Library Management System

A comprehensive web-based Library Management System with multi-role access control for <PERSON><PERSON>, Librarian, and Student users.

## Features

### 🔐 Authentication & Role-Based Access
- **Admin**: Complete system control with user and book management
- **Librarian**: Book issuing, returning, and basic management
- **Student**: Book search, profile management, and issue tracking

### 📚 Book Management
- Add, edit, and delete books
- Track book availability and quantities
- Categorized book organization
- Real-time search functionality

### 👥 User Management
- Admin can manage librarians and students
- User authentication with secure password hashing
- Profile management for all user types

### 📊 Issue & Return System
- 14-day loan period
- Automatic fine calculation ($2.00/day for overdue books)
- Issue history tracking
- Return processing with fine calculation

### 📈 Reports & Analytics
- Dashboard statistics for all user roles
- Overdue book tracking
- Fine management
- User activity monitoring

## Technology Stack

- **Backend**: Python Flask
- **Frontend**: HTML5, CSS3, Bootstrap 5
- **Database**: SQLite (easily configurable to MySQL)
- **Icons**: Font Awesome 6
- **Styling**: Custom CSS with gradient themes

## Installation & Setup

### Prerequisites
- Python 3.7 or higher
- pip (Python package installer)

### Installation Steps

1. **Clone or download the project**
   ```bash
   cd your-project-directory
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**
   ```bash
   python app.py
   ```

4. **Access the application**
   - Open your web browser
   - Navigate to `http://localhost:5000`

## Default Login Credentials

### Administrator
- **Email**: <EMAIL>
- **Password**: admin123

### Librarian & Student Accounts
- Must be created by the Administrator through the admin dashboard

## User Roles & Permissions

### 👨‍💼 Administrator
- ✅ Manage all books (CRUD operations)
- ✅ Manage librarian accounts
- ✅ Manage student accounts
- ✅ View comprehensive reports
- ✅ Monitor all issue/return activities
- ✅ Access to complete system analytics

### 👨‍🏫 Librarian
- ✅ Issue books to students
- ✅ Process book returns
- ✅ Add new books to inventory
- ✅ View student information
- ✅ Monitor book availability
- ❌ Cannot delete books or users

### 👨‍🎓 Student
- ✅ Search books by title/author/category
- ✅ View personal issue history
- ✅ Check due dates and fines
- ✅ Update personal profile
- ❌ Cannot issue books directly (must visit librarian)

## Database Schema

### Tables
1. **Admin** - Administrator accounts
2. **Librarian** - Librarian accounts
3. **Student** - Student accounts
4. **Book** - Book inventory
5. **Issue** - Book issue/return records

### Key Relationships
- Books have one-to-many relationship with Issues
- Students have one-to-many relationship with Issues
- Automatic fine calculation based on return dates

## File Structure

```
library-management/
├── app.py                          # Main Flask application
├── requirements.txt                # Python dependencies
├── library.db                      # SQLite database (auto-created)
├── templates/                      # HTML templates
│   ├── base.html                   # Base template
│   ├── index.html                  # Landing page
│   ├── login.html                  # Login page
│   ├── admin_dashboard.html        # Admin dashboard
│   ├── admin_books.html            # Book management
│   ├── admin_add_book.html         # Add book form
│   ├── admin_edit_book.html        # Edit book form
│   ├── admin_librarians.html       # Librarian management
│   ├── admin_add_librarian.html    # Add librarian form
│   ├── librarian_dashboard.html    # Librarian dashboard
│   ├── librarian_issue_book.html   # Issue book form
│   ├── student_dashboard.html      # Student dashboard
│   ├── student_search_books.html   # Book search
│   └── ...                         # Additional templates
└── static/                         # Static files (CSS, JS, images)
```

## Key Features Explanation

### Fine Calculation
- Automatic calculation based on overdue days
- $2.00 per day fine for late returns
- Real-time fine display on student dashboard

### Search Functionality
- Search by book title or author
- Filter by category
- Real-time search suggestions
- Case-insensitive search

### Security Features
- Password hashing using Werkzeug
- Session-based authentication
- Role-based access control
- CSRF protection through Flask

### Responsive Design
- Mobile-friendly interface
- Bootstrap 5 responsive grid
- Touch-friendly buttons and forms
- Optimized for all screen sizes

## Customization

### Adding New Book Categories
Edit the category options in:
- `templates/admin_add_book.html`
- `templates/admin_edit_book.html`
- `templates/librarian_add_book.html`

### Changing Fine Amount
Modify the `calculate_fine()` function in `app.py`:
```python
def calculate_fine(due_date, return_date=None):
    # Change 2.0 to your desired daily fine amount
    return days_overdue * 2.0
```

### Database Configuration
To use MySQL instead of SQLite, update the database URI in `app.py`:
```python
app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql://username:password@localhost/library_db'
```

## Troubleshooting

### Common Issues

1. **ImportError: No module named 'flask'**
   - Install dependencies: `pip install -r requirements.txt`

2. **Database errors on first run**
   - Delete `library.db` file and restart the application

3. **Permission denied errors**
   - Ensure you have write permissions in the project directory

4. **Port already in use**
   - Change the port in `app.py`: `app.run(debug=True, port=5001)`

## Future Enhancements

- [ ] Email notifications for overdue books
- [ ] PDF report generation
- [ ] Book reservation system
- [ ] Barcode scanning integration
- [ ] Multi-library support
- [ ] Advanced analytics dashboard
- [ ] Mobile app integration
- [ ] Book recommendation system

## Support

For issues or questions, please check the troubleshooting section above or review the code comments for detailed implementation guidance.

## License

This project is open source and available under the MIT License.
