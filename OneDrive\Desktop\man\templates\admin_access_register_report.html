{% extends "base.html" %}

{% block title %}Access Register Report - Admin{% endblock %}

{% block sidebar %}
<!-- Dashboard -->
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>

<!-- Management Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-info" data-bs-toggle="collapse" href="#managementSubmenu" role="button" aria-expanded="false">
        <i class="fas fa-cogs me-2"></i>Management
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse" id="managementSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_books') }}">
                <i class="fas fa-book me-2"></i>Manage Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_ebooks') }}">
                <i class="fas fa-tablet-alt me-2"></i>Manage E-Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_students') }}">
                <i class="fas fa-graduation-cap me-2"></i>Manage Students
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_librarians') }}">
                <i class="fas fa-user-tie me-2"></i>Manage Librarians
            </a>

        </div>
    </div>
</div>

<!-- Circulation Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-primary" data-bs-toggle="collapse" href="#circulationSubmenu" role="button" aria-expanded="false">
        <i class="fas fa-exchange-alt me-2"></i>Circulation
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse" id="circulationSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_circulation_counter') }}">
                <i class="fas fa-desktop me-2"></i>Counter Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_issue_history') }}">
                <i class="fas fa-history me-2"></i>Issue History
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bulk_operations') }}">
                <i class="fas fa-layer-group me-2"></i>Bulk Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_payment_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Payment Management
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_management') }}">
                <i class="fas fa-tools me-2"></i>Binding & Maintenance
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_fine_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Fine Management
            </a>

        </div>
    </div>
</div>

<!-- Reports Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-success" data-bs-toggle="collapse" href="#reportsSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-chart-bar me-2"></i>Reports
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="reportsSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_library_connection_report') }}">
                <i class="fas fa-wifi me-2"></i>Library Connection
            </a>
            <a class="nav-link py-1 active" href="{{ url_for('admin_access_register_report') }}">
                <i class="fas fa-sign-in-alt me-2"></i>Access Register
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bibliography_report') }}">
                <i class="fas fa-book-open me-2"></i>Bibliography
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_counter_report') }}">
                <i class="fas fa-calculator me-2"></i>Counter Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_statistics_report') }}">
                <i class="fas fa-chart-line me-2"></i>Statistics
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_report') }}">
                <i class="fas fa-tools me-2"></i>Binding Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_database_report') }}">
                <i class="fas fa-database me-2"></i>Database Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_member_report') }}">
                <i class="fas fa-users me-2"></i>Member Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_resource_report') }}">
                <i class="fas fa-archive me-2"></i>Resource Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_no_dues_report') }}">
                <i class="fas fa-check-circle me-2"></i>No Dues Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_qb_report') }}">
                <i class="fas fa-money-check-alt me-2"></i>QB Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_transfer_report') }}">
                <i class="fas fa-exchange-alt me-2"></i>Transfer Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_missing_report') }}">
                <i class="fas fa-exclamation-triangle me-2"></i>Missing Report
            </a>
        </div>
    </div>
</div>

<!-- System Administration -->
<div class="nav-item">
    <a class="nav-link fw-bold text-warning" data-bs-toggle="collapse" href="#systemSubmenu" role="button" aria-expanded="false">
        <i class="fas fa-server me-2"></i>System
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse" id="systemSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_settings') }}">
                <i class="fas fa-cog me-2"></i>System Settings
            </a>

        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-sign-in-alt me-3 text-success"></i>Access Register Report</h2>
        <p class="text-muted mb-0">Complete access log of all library users with entry/exit timestamps</p>
    </div>
    <div>
        <button class="btn btn-success" onclick="exportReport('excel')">
            <i class="fas fa-file-excel me-2"></i>Export Excel
        </button>
        <button class="btn btn-danger ms-2" onclick="exportReport('pdf')">
            <i class="fas fa-file-pdf me-2"></i>Export PDF
        </button>
    </div>
</div>

<!-- Filter Section -->
<div class="card autolib-card mb-4">
    <div class="card-header bg-gradient-success text-white">
        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Access Filters</h5>
    </div>
    <div class="card-body">
        <form id="filterForm">
            <div class="row">
                <div class="col-md-3">
                    <label for="dateFrom" class="form-label">From Date</label>
                    <input type="date" class="form-control" id="dateFrom" name="dateFrom">
                </div>
                <div class="col-md-3">
                    <label for="dateTo" class="form-label">To Date</label>
                    <input type="date" class="form-control" id="dateTo" name="dateTo">
                </div>
                <div class="col-md-3">
                    <label for="accessType" class="form-label">Access Type</label>
                    <select class="form-select" id="accessType" name="accessType">
                        <option value="">All Access</option>
                        <option value="entry">Entry Only</option>
                        <option value="exit">Exit Only</option>
                        <option value="both">Entry & Exit</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="department" class="form-label">Department</label>
                    <select class="form-select" id="department" name="department">
                        <option value="">All Departments</option>
                        <option value="CSE">Computer Science</option>
                        <option value="IT">Information Technology</option>
                        <option value="ECE">Electronics & Communication</option>
                        <option value="EEE">Electrical & Electronics</option>
                        <option value="MECH">Mechanical</option>
                        <option value="CIVIL">Civil</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-6">
                    <label for="searchUser" class="form-label">Search User</label>
                    <input type="text" class="form-control" id="searchUser" placeholder="Enter name or ID">
                </div>
                <div class="col-md-6">
                    <label for="timeRange" class="form-label">Time Range</label>
                    <select class="form-select" id="timeRange" name="timeRange">
                        <option value="">All Day</option>
                        <option value="morning">Morning (6 AM - 12 PM)</option>
                        <option value="afternoon">Afternoon (12 PM - 6 PM)</option>
                        <option value="evening">Evening (6 PM - 10 PM)</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button type="button" class="btn btn-success" onclick="generateReport()">
                        <i class="fas fa-search me-2"></i>Generate Report
                    </button>
                    <button type="button" class="btn btn-outline-secondary ms-2" onclick="resetFilters()">
                        <i class="fas fa-refresh me-2"></i>Reset
                    </button>
                    <button type="button" class="btn btn-info ms-2" onclick="showLiveAccess()">
                        <i class="fas fa-eye me-2"></i>Live Access
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Summary Statistics -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-success">
                    <i class="fas fa-sign-in-alt fa-2x mb-2"></i>
                </div>
                <h4 class="text-success" id="totalEntries">0</h4>
                <p class="text-muted mb-0 small">Total Entries</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-danger">
                    <i class="fas fa-sign-out-alt fa-2x mb-2"></i>
                </div>
                <h4 class="text-danger" id="totalExits">0</h4>
                <p class="text-muted mb-0 small">Total Exits</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-warning">
                    <i class="fas fa-users fa-2x mb-2"></i>
                </div>
                <h4 class="text-warning" id="uniqueVisitors">0</h4>
                <p class="text-muted mb-0 small">Unique Visitors</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-info">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                </div>
                <h4 class="text-info" id="avgStayTime">0h 0m</h4>
                <p class="text-muted mb-0 small">Avg Stay Time</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-primary">
                    <i class="fas fa-chart-line fa-2x mb-2"></i>
                </div>
                <h4 class="text-primary" id="peakTime">--:--</h4>
                <p class="text-muted mb-0 small">Peak Time</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-success">
                    <i class="fas fa-user-check fa-2x mb-2"></i>
                </div>
                <h4 class="text-success" id="currentlyInside">0</h4>
                <p class="text-muted mb-0 small">Currently Inside</p>
            </div>
        </div>
    </div>
</div>

<!-- Access Register Table -->
<div class="card autolib-card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="fas fa-table me-2"></i>Access Register</h5>
        <div>
            <span class="badge bg-success" id="liveIndicator">
                <i class="fas fa-circle me-1" style="font-size: 8px;"></i>Live
            </span>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="accessTable">
                <thead class="table-dark">
                    <tr>
                        <th>S.No</th>
                        <th>User ID</th>
                        <th>Name</th>
                        <th>Department</th>
                        <th>Entry Time</th>
                        <th>Exit Time</th>
                        <th>Duration</th>
                        <th>Purpose</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="9" class="text-center text-muted">
                            <i class="fas fa-info-circle me-2"></i>No access data available. Click "Generate Report" to load data.
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <nav aria-label="Access register pagination">
            <ul class="pagination justify-content-center mt-3" id="pagination">
                <!-- Pagination will be generated dynamically -->
            </ul>
        </nav>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Set default dates (today)
    const today = new Date();
    $('#dateTo').val(today.toISOString().split('T')[0]);
    $('#dateFrom').val(today.toISOString().split('T')[0]);
    
    // Load initial data
    generateReport();
    
    // Auto-refresh every 30 seconds for live data
    setInterval(function() {
        if ($('#liveIndicator').hasClass('bg-success')) {
            generateReport();
        }
    }, 30000);
});

function generateReport() {
    const filters = {
        dateFrom: $('#dateFrom').val(),
        dateTo: $('#dateTo').val(),
        accessType: $('#accessType').val(),
        department: $('#department').val(),
        searchUser: $('#searchUser').val(),
        timeRange: $('#timeRange').val()
    };
    
    // Show loading
    $('#accessTable tbody').html('<tr><td colspan="9" class="text-center"><i class="fas fa-spinner fa-spin me-2"></i>Loading access data...</td></tr>');
    
    // Simulate API call - replace with actual implementation
    setTimeout(() => {
        loadAccessData(filters);
    }, 1000);
}

function loadAccessData(filters) {
    // Simulate data - replace with actual API call
    const sampleData = [
        {
            sno: 1,
            userId: 'CS001',
            name: 'John Doe',
            department: 'CSE',
            entryTime: '09:30:00',
            exitTime: '11:45:00',
            duration: '2h 15m',
            purpose: 'Study',
            status: 'Completed'
        },
        {
            sno: 2,
            userId: 'IT002',
            name: 'Jane Smith',
            department: 'IT',
            entryTime: '10:15:00',
            exitTime: 'Inside',
            duration: '1h 30m',
            purpose: 'Research',
            status: 'Inside'
        },
        {
            sno: 3,
            userId: 'ECE003',
            name: 'Mike Johnson',
            department: 'ECE',
            entryTime: '08:45:00',
            exitTime: '10:30:00',
            duration: '1h 45m',
            purpose: 'Book Issue',
            status: 'Completed'
        }
    ];
    
    // Update statistics
    $('#totalEntries').text('25');
    $('#totalExits').text('23');
    $('#uniqueVisitors').text('20');
    $('#avgStayTime').text('2h 15m');
    $('#peakTime').text('10:30 AM');
    $('#currentlyInside').text('2');
    
    // Update table
    let tableHTML = '';
    sampleData.forEach(row => {
        const statusBadge = row.status === 'Inside' ? 
            '<span class="badge bg-warning">Inside</span>' : 
            '<span class="badge bg-success">Completed</span>';
        
        tableHTML += `
            <tr>
                <td>${row.sno}</td>
                <td>${row.userId}</td>
                <td>${row.name}</td>
                <td>${row.department}</td>
                <td>${row.entryTime}</td>
                <td>${row.exitTime}</td>
                <td>${row.duration}</td>
                <td>${row.purpose}</td>
                <td>${statusBadge}</td>
            </tr>
        `;
    });
    
    $('#accessTable tbody').html(tableHTML);
}

function resetFilters() {
    $('#filterForm')[0].reset();
    const today = new Date();
    $('#dateTo').val(today.toISOString().split('T')[0]);
    $('#dateFrom').val(today.toISOString().split('T')[0]);
    generateReport();
}

function showLiveAccess() {
    $('#liveIndicator').removeClass('bg-secondary').addClass('bg-success');
    generateReport();
    alert('Live access monitoring enabled. Data will refresh every 30 seconds.');
}

function exportReport(format) {
    const filters = {
        dateFrom: $('#dateFrom').val(),
        dateTo: $('#dateTo').val(),
        accessType: $('#accessType').val(),
        department: $('#department').val(),
        format: format
    };
    
    alert(`Exporting Access Register Report as ${format.toUpperCase()}...`);
}
</script>
{% endblock %}
