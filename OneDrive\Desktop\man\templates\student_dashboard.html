{% extends "base.html" %}

{% block title %}Student Dashboard{% endblock %}

{% block sidebar %}
<a class="nav-link active" href="{{ url_for('student_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>
<a class="nav-link" href="{{ url_for('student_search_books') }}">
    <i class="fas fa-search me-2"></i>Search Books
</a>
<a class="nav-link" href="{{ url_for('student_issue_history') }}">
    <i class="fas fa-history me-2"></i>Issue History
</a>
<a class="nav-link" href="{{ url_for('student_profile') }}">
    <i class="fas fa-user me-2"></i>My Profile
</a>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-tachometer-alt me-3"></i>Student Dashboard</h2>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-6 mb-3">
        <div class="card stat-card stat-card-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ stats.books_issued }}</h3>
                        <p class="text-muted mb-0">Books Currently Issued</p>
                    </div>
                    <div class="text-primary">
                        <i class="fas fa-book fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-3">
        <div class="card stat-card {% if stats.total_fine > 0 %}stat-card-danger{% else %}stat-card-success{% endif %}">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">${{ "%.2f"|format(stats.total_fine) }}</h3>
                        <p class="text-muted mb-0">Total Fine Due</p>
                    </div>
                    <div class="{% if stats.total_fine > 0 %}text-danger{% else %}text-success{% endif %}">
                        <i class="fas fa-dollar-sign fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Fine Alert -->
{% if stats.total_fine > 0 %}
<div class="alert alert-warning" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    You have <strong>${{ "%.2f"|format(stats.total_fine) }}</strong> in overdue fines. Please return your books on time.
</div>
{% endif %}

<!-- Currently Issued Books -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-book-open me-2"></i>Currently Issued Books</h5>
    </div>
    <div class="card-body">
        {% if issues %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>Book Title</th>
                        <th>Author</th>
                        <th>Issue Date</th>
                        <th>Due Date</th>
                        <th>Status</th>
                        <th>Fine</th>
                    </tr>
                </thead>
                <tbody>
                    {% for issue in issues %}
                    <tr>
                        <td><strong>{{ issue.book.title }}</strong></td>
                        <td>{{ issue.book.author }}</td>
                        <td>{{ issue.issue_date.strftime('%Y-%m-%d') }}</td>
                        <td>{{ issue.due_date.strftime('%Y-%m-%d') }}</td>
                        <td>
                            {% set now = moment() %}
                            {% if issue.due_date < now %}
                                <span class="badge bg-danger">Overdue</span>
                            {% elif (issue.due_date - now).days <= 2 %}
                                <span class="badge bg-warning">Due Soon</span>
                            {% else %}
                                <span class="badge bg-success">On Time</span>
                            {% endif %}
                        </td>
                        <td>
                            {% set fine = calculate_fine(issue.due_date) %}
                            {% if fine > 0 %}
                                <span class="text-danger">${{ "%.2f"|format(fine) }}</span>
                            {% else %}
                                <span class="text-success">$0.00</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-4">
            <i class="fas fa-book fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No books currently issued</h5>
            <p class="text-muted">Visit the library to issue some books!</p>
            <a href="{{ url_for('student_search_books') }}" class="btn btn-primary">
                <i class="fas fa-search me-2"></i>Search Books
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('student_search_books') }}" class="btn btn-primary btn-custom">
                        <i class="fas fa-search me-2"></i>Search Books
                    </a>
                    <a href="{{ url_for('student_issue_history') }}" class="btn btn-info btn-custom">
                        <i class="fas fa-history me-2"></i>View Issue History
                    </a>
                    <a href="{{ url_for('student_profile') }}" class="btn btn-secondary btn-custom">
                        <i class="fas fa-user me-2"></i>Update Profile
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Library Information</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6><i class="fas fa-clock me-2"></i>Loan Period</h6>
                    <p class="text-muted mb-0">Books can be borrowed for 14 days</p>
                </div>
                
                <div class="mb-3">
                    <h6><i class="fas fa-dollar-sign me-2"></i>Fine Policy</h6>
                    <p class="text-muted mb-0">$2.00 per day for overdue books</p>
                </div>
                
                <div class="mb-3">
                    <h6><i class="fas fa-phone me-2"></i>Contact Library</h6>
                    <p class="text-muted mb-0">Visit the librarian for book issues and returns</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reading Progress -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Your Reading Activity</h5>
    </div>
    <div class="card-body">
        <div class="row text-center">
            <div class="col-md-4">
                <i class="fas fa-book-reader fa-2x text-primary mb-2"></i>
                <h6>Books Issued This Month</h6>
                <h4 class="text-primary">{{ stats.books_issued }}</h4>
            </div>
            <div class="col-md-4">
                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                <h6>Books Returned On Time</h6>
                <h4 class="text-success">{{ (stats.books_issued - (stats.total_fine / 2))|int }}</h4>
            </div>
            <div class="col-md-4">
                <i class="fas fa-star fa-2x text-warning mb-2"></i>
                <h6>Reading Score</h6>
                <h4 class="text-warning">{% if stats.total_fine == 0 %}100%{% else %}85%{% endif %}</h4>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Add moment.js functionality for date calculations
function moment() {
    return new Date();
}

function calculate_fine(due_date_str) {
    var due_date = new Date(due_date_str);
    var now = new Date();
    
    if (now > due_date) {
        var days_overdue = Math.floor((now - due_date) / (1000 * 60 * 60 * 24));
        return days_overdue * 2.0;
    }
    return 0.0;
}
</script>
{% endblock %}
