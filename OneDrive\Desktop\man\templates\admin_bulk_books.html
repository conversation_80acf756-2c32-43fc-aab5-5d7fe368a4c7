{% extends "base.html" %}

{% block title %}Bulk Add Books - Admin{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>
<a class="nav-link active" href="{{ url_for('admin_books') }}">
    <i class="fas fa-book me-2"></i>Manage Books
</a>
<a class="nav-link" href="{{ url_for('admin_librarians') }}">
    <i class="fas fa-user-tie me-2"></i>Manage Librarians
</a>
<a class="nav-link" href="{{ url_for('admin_students') }}">
    <i class="fas fa-graduation-cap me-2"></i>Manage Students
</a>
<a class="nav-link" href="{{ url_for('admin_bulk_users') }}">
    <i class="fas fa-upload me-2"></i>Bulk Create Users
</a>
<a class="nav-link" href="{{ url_for('admin_bulk_books') }}">
    <i class="fas fa-file-upload me-2"></i>Bulk Add Books
</a>
<a class="nav-link" href="{{ url_for('admin_issue_history') }}">
    <i class="fas fa-history me-2"></i>Issue History
</a>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-file-upload me-3"></i>Bulk Add Books</h2>
    <div>
        <a href="{{ url_for('admin_add_book') }}" class="btn btn-success btn-custom me-2">
            <i class="fas fa-plus me-2"></i>Add Single Book
        </a>
        <a href="{{ url_for('admin_books') }}" class="btn btn-secondary btn-custom">
            <i class="fas fa-arrow-left me-2"></i>Back to Books
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Upload Form -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cloud-upload-alt me-2"></i>Upload Excel File</h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="file" class="form-label">
                            <i class="fas fa-file-excel me-2"></i>Select Excel File
                        </label>
                        <input type="file" class="form-control" id="file" name="file" accept=".xlsx,.xls" required>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            Upload an Excel file (.xlsx or .xls) with book data
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-upload me-2"></i>Upload & Process Books
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Template Download -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-download me-2"></i>Excel Template</h5>
            </div>
            <div class="card-body">
                <p class="mb-3">
                    <i class="fas fa-info-circle text-info me-2"></i>
                    Download the template to ensure your Excel file has the correct format.
                </p>
                
                <div class="table-responsive mb-3">
                    <table class="table table-bordered table-sm">
                        <thead class="table-light">
                            <tr>
                                <th>access_no</th>
                                <th>title</th>
                                <th>author</th>
                                <th>publisher</th>
                                <th>subject</th>
                                <th>department</th>
                                <th>category</th>
                                <th>location</th>
                                <th>copies</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="text-muted">
                                <td>ACC00001</td>
                                <td>Data Structures</td>
                                <td>Cormen</td>
                                <td>MIT Press</td>
                                <td>Computer Science</td>
                                <td>CSE</td>
                                <td>Textbook</td>
                                <td>Section A, Shelf 1</td>
                                <td>5</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <a href="{{ url_for('admin_download_book_template') }}" class="btn btn-outline-success">
                    <i class="fas fa-download me-2"></i>Download Template
                </a>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Guidelines -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Important Notes</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <h6><i class="fas fa-rules me-2"></i>Required Fields:</h6>
                    <ul class="mb-0 small">
                        <li><strong>access_no:</strong> Must be unique</li>
                        <li><strong>title:</strong> Book title</li>
                        <li><strong>author:</strong> Author name</li>
                        <li><strong>publisher:</strong> Publisher name</li>
                        <li><strong>subject:</strong> Subject area</li>
                        <li><strong>department:</strong> CSE, IT, ECE, etc.</li>
                        <li><strong>category:</strong> Textbook, Reference, etc.</li>
                        <li><strong>location:</strong> Physical location</li>
                        <li><strong>copies:</strong> Number (minimum 1)</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Department List -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-university me-2"></i>Valid Departments</h6>
            </div>
            <div class="card-body">
                <div class="row small">
                    {% for dept_code, dept_name in departments %}
                    <div class="col-6 mb-1">
                        <span class="badge bg-light text-dark">{{ dept_code }}</span>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Category List -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-tags me-2"></i>Valid Categories</h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <span class="badge bg-primary me-1 mb-1">Textbook</span>
                    <span class="badge bg-primary me-1 mb-1">Reference</span>
                    <span class="badge bg-primary me-1 mb-1">Laboratory Manual</span>
                    <span class="badge bg-primary me-1 mb-1">Journal</span>
                    <span class="badge bg-primary me-1 mb-1">Magazine</span>
                    <span class="badge bg-primary me-1 mb-1">Thesis</span>
                    <span class="badge bg-primary me-1 mb-1">Project Report</span>
                </div>
            </div>
        </div>
    </div>
</div>

{% if results %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Upload Results
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="card text-center border-success">
                            <div class="card-body">
                                <h4 class="text-success">{{ results.successful }}</h4>
                                <p class="mb-0"><i class="fas fa-check-circle me-2"></i>Successful</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center border-danger">
                            <div class="card-body">
                                <h4 class="text-danger">{{ results.failed }}</h4>
                                <p class="mb-0"><i class="fas fa-times-circle me-2"></i>Failed</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center border-warning">
                            <div class="card-body">
                                <h4 class="text-warning">{{ results.skipped }}</h4>
                                <p class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Skipped</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center border-info">
                            <div class="card-body">
                                <h4 class="text-info">{{ results.total }}</h4>
                                <p class="mb-0"><i class="fas fa-list me-2"></i>Total</p>
                            </div>
                        </div>
                    </div>
                </div>

                {% if results.errors %}
                <div class="mt-4">
                    <h6><i class="fas fa-exclamation-circle text-danger me-2"></i>Errors:</h6>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            {% for error in results.errors %}
                            <li>{{ error }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // File upload validation
    $('#file').change(function() {
        const file = this.files[0];
        if (file) {
            const fileName = file.name;
            const fileExtension = fileName.split('.').pop().toLowerCase();
            
            if (!['xlsx', 'xls'].includes(fileExtension)) {
                alert('Please select a valid Excel file (.xlsx or .xls)');
                $(this).val('');
                return;
            }
            
            // Check file size (5MB limit)
            if (file.size > 5 * 1024 * 1024) {
                alert('File size should not exceed 5MB');
                $(this).val('');
                return;
            }
        }
    });
    
    // Form submission validation
    $('form').submit(function(e) {
        const fileInput = $('#file')[0];
        if (!fileInput.files.length) {
            e.preventDefault();
            alert('Please select an Excel file to upload');
            return false;
        }
    });
});
</script>
{% endblock %}
