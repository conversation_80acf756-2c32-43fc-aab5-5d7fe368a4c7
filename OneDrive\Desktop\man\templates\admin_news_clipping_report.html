{% extends "base.html" %}

{% block title %}News Clipping Report - Admin{% endblock %}

{% block extra_css %}
<style>
    .card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border-radius: 0.5rem;
        margin-bottom: 1.5rem;
    }

    .card-header {
        background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
        color: white;
        border-radius: 0.5rem 0.5rem 0 0 !important;
        border: none;
        font-weight: 600;
    }

    .btn {
        border-radius: 0.375rem;
        font-weight: 500;
        transition: all 0.2s ease-in-out;
        padding: 0.5rem 1.5rem;
    }

    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    .btn-success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
    }

    .btn-danger {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        border: none;
    }

    .btn-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
    }

    .form-control, .form-select {
        border-radius: 0.375rem;
        border: 1px solid #e0e6ed;
        transition: all 0.2s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #6f42c1;
        box-shadow: 0 0 0 0.2rem rgba(111, 66, 193, 0.25);
    }

    .table {
        border-radius: 0.5rem;
        overflow: hidden;
    }

    .table thead th {
        background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
        color: white;
        border: none;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.85rem;
        letter-spacing: 0.5px;
    }

    .table tbody tr {
        transition: all 0.2s ease;
    }

    .table tbody tr:hover {
        background-color: #f8f9fa;
        transform: scale(1.01);
    }

    .badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
        border-radius: 0.375rem;
        font-weight: 600;
    }

    .stats-card {
        border-radius: 0.5rem;
        padding: 1.5rem;
        text-align: center;
        margin-bottom: 1rem;
        transition: all 0.2s ease;
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .stats-card h4 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .stats-card p {
        font-size: 0.9rem;
        margin: 0;
        font-weight: 500;
    }

    .bg-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
    }

    .bg-success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    }

    .bg-info {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
    }

    .bg-warning {
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
    }

    .filter-section {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        border-left: 4px solid #6f42c1;
    }

    .page-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border-left: 4px solid #6f42c1;
    }

    .page-header h2 {
        color: #6f42c1;
        margin: 0;
        font-weight: 700;
    }

    .report-summary {
        background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
        color: white;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .report-summary h5 {
        margin: 0;
        font-weight: 600;
    }

    .loading-spinner {
        display: inline-block;
        width: 1rem;
        height: 1rem;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #6f42c1;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .empty-state {
        padding: 3rem 1rem;
        text-align: center;
        color: #6c757d;
    }

    .empty-state i {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    .table-responsive {
        border-radius: 0.5rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    .export-buttons {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .filter-buttons {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
        margin-top: 1rem;
    }

    .news-type-badge {
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .keywords-preview, .abstract-preview {
        font-style: italic;
        color: #6c757d;
        font-size: 0.85rem;
    }

    .date-badge {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .newspaper-name {
        font-weight: 600;
        color: #495057;
    }

    .college-dept-info {
        font-size: 0.85rem;
        color: #6c757d;
    }

    .pages-info {
        background: #e9ecef;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 600;
        color: #495057;
    }
</style>
{% endblock %}

{% block sidebar %}
<!-- Dashboard -->
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>

<!-- Management Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-info" data-bs-toggle="collapse" href="#managementSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-cogs me-2"></i>Management
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="managementSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_books') }}">
                <i class="fas fa-book me-2"></i>Manage Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_ebooks') }}">
                <i class="fas fa-tablet-alt me-2"></i>Manage E-Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_students') }}">
                <i class="fas fa-graduation-cap me-2"></i>Manage Students
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_librarians') }}">
                <i class="fas fa-user-tie me-2"></i>Manage Librarians
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_colleges') }}">
                <i class="fas fa-university me-2"></i>Manage Colleges
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_departments') }}">
                <i class="fas fa-building me-2"></i>Manage Departments
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_news_clippings') }}">
                <i class="fas fa-newspaper me-2"></i>News Clippings
            </a>
        </div>
    </div>
</div>

<!-- Circulation Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-primary" data-bs-toggle="collapse" href="#circulationSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-exchange-alt me-2"></i>Circulation
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="circulationSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_circulation_counter') }}">
                <i class="fas fa-desktop me-2"></i>Counter Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_issue_history') }}">
                <i class="fas fa-history me-2"></i>Issue History
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bulk_operations') }}">
                <i class="fas fa-layer-group me-2"></i>Bulk Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_payment_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Payment Management
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_management') }}">
                <i class="fas fa-tools me-2"></i>Binding & Maintenance
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_fine_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Fine Management
            </a>
        </div>
    </div>
</div>

<!-- Reports Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-success" data-bs-toggle="collapse" href="#reportsSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-chart-bar me-2"></i>Reports
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="reportsSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_library_connection_report') }}">
                <i class="fas fa-wifi me-2"></i>Library Connection
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_access_register_report') }}">
                <i class="fas fa-sign-in-alt me-2"></i>Access Register
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bibliography_report') }}">
                <i class="fas fa-book-open me-2"></i>Bibliography
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_counter_report') }}">
                <i class="fas fa-calculator me-2"></i>Counter Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_statistics_report') }}">
                <i class="fas fa-chart-line me-2"></i>Statistics
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_report') }}">
                <i class="fas fa-tools me-2"></i>Binding Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_database_report') }}">
                <i class="fas fa-database me-2"></i>Database Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_member_report') }}">
                <i class="fas fa-users me-2"></i>Member Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_resource_report') }}">
                <i class="fas fa-archive me-2"></i>Resource Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_no_dues_report') }}">
                <i class="fas fa-check-circle me-2"></i>No Dues Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_qb_report') }}">
                <i class="fas fa-money-check-alt me-2"></i>QB Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_transfer_report') }}">
                <i class="fas fa-exchange-alt me-2"></i>Transfer Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_missing_report') }}">
                <i class="fas fa-exclamation-triangle me-2"></i>Missing Report
            </a>
            <a class="nav-link py-1 active" href="{{ url_for('admin_news_clipping_report') }}">
                <i class="fas fa-newspaper me-2"></i>News Clipping Report
            </a>
        </div>
    </div>
</div>

<!-- System Administration -->
<div class="nav-item">
    <a class="nav-link fw-bold text-warning" data-bs-toggle="collapse" href="#systemSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-server me-2"></i>System
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="systemSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_settings') }}">
                <i class="fas fa-cog me-2"></i>System Settings
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-newspaper me-3"></i>News Clipping Report</h2>
    <div>
        <button class="btn btn-success" onclick="exportReport('excel')">
            <i class="fas fa-file-excel me-2"></i>Export Excel
        </button>
        <button class="btn btn-danger" onclick="exportReport('pdf')">
            <i class="fas fa-file-pdf me-2"></i>Export PDF
        </button>
    </div>
</div>

<!-- Filter Section -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Report Filters</h5>
    </div>
    <div class="card-body">
        <form id="filterForm">
            <div class="row">
                <div class="col-md-3">
                    <label for="college" class="form-label">College</label>
                    <select class="form-select" id="college" name="college">
                        <option value="">All Colleges</option>
                        {% for college_id, college_name in colleges %}
                        <option value="{{ college_id }}">{{ college_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="department" class="form-label">Department</label>
                    <select class="form-select" id="department" name="department" disabled>
                        <option value="">Select College First</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="newsType" class="form-label">News Type</label>
                    <select class="form-select" id="newsType" name="newsType">
                        <option value="">All Types</option>
                        <option value="Academic">Academic</option>
                        <option value="Research">Research</option>
                        <option value="Sports">Sports</option>
                        <option value="Cultural">Cultural</option>
                        <option value="Achievement">Achievement</option>
                        <option value="General">General</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="startDate" class="form-label">Start Date</label>
                    <input type="date" class="form-control" id="startDate" name="startDate">
                </div>
                <div class="col-md-2">
                    <label for="endDate" class="form-label">End Date</label>
                    <input type="date" class="form-control" id="endDate" name="endDate">
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-12">
                    <button type="button" class="btn btn-primary" onclick="generateReport()">
                        <i class="fas fa-chart-bar me-2"></i>Generate Report
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="resetFilters()">
                        <i class="fas fa-times me-2"></i>Reset Filters
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Report Results -->
<div id="reportResults" style="display: none;">
    <!-- Summary Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 id="totalClippings">0</h4>
                            <p class="mb-0">Total Clippings</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-newspaper fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 id="totalNewspapers">0</h4>
                            <p class="mb-0">Newspapers</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-building fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 id="totalDepartments">0</h4>
                            <p class="mb-0">Departments</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-sitemap fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 id="dateRange">-</h4>
                            <p class="mb-0">Date Range</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Report Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-table me-2"></i>Detailed News Clipping Report</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover" id="reportTable">
                    <thead class="table-dark">
                        <tr>
                            <th>Clipping No</th>
                            <th>Date</th>
                            <th>Newspaper</th>
                            <th>Type</th>
                            <th>Pages</th>
                            <th>College</th>
                            <th>Department</th>
                            <th>Keywords</th>
                            <th>Abstract</th>
                        </tr>
                    </thead>
                    <tbody id="reportTableBody">
                        <!-- Report data will be populated here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Set default date range (last 30 days)
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
    
    $('#endDate').val(today.toISOString().split('T')[0]);
    $('#startDate').val(thirtyDaysAgo.toISOString().split('T')[0]);
    
    // College/Department dropdown functionality
    $('#college').change(function() {
        const collegeId = $(this).val();
        const departmentSelect = $('#department');
        
        if (collegeId) {
            departmentSelect.prop('disabled', false);
            departmentSelect.html('<option value="">Loading departments...</option>');
            
            $.ajax({
                url: `/api/departments/${collegeId}`,
                method: 'GET',
                success: function(departments) {
                    departmentSelect.html('<option value="">All Departments</option>');
                    departments.forEach(function(dept) {
                        departmentSelect.append(`<option value="${dept.department_id}">${dept.code} - ${dept.name}</option>`);
                    });
                },
                error: function() {
                    departmentSelect.html('<option value="">Error loading departments</option>');
                }
            });
        } else {
            departmentSelect.prop('disabled', true);
            departmentSelect.html('<option value="">Select College First</option>');
        }
    });
});

function generateReport() {
    const college_id = $('#college').val();
    const department_id = $('#department').val();
    const news_type = $('#newsType').val();
    const start_date = $('#startDate').val();
    const end_date = $('#endDate').val();
    
    // Build query parameters
    const params = new URLSearchParams();
    if (college_id) params.append('college_id', college_id);
    if (department_id) params.append('department_id', department_id);
    if (news_type) params.append('news_type', news_type);
    if (start_date) params.append('start_date', start_date);
    if (end_date) params.append('end_date', end_date);
    
    // Show loading
    $('#reportResults').show();
    $('#reportTableBody').html(`
        <tr>
            <td colspan="9" class="text-center">
                <i class="fas fa-spinner fa-spin me-2"></i>Generating report...
            </td>
        </tr>
    `);
    
    $.ajax({
        url: `/api/admin/news-clippings?${params.toString()}`,
        method: 'GET',
        success: function(response) {
            const tbody = $('#reportTableBody');
            tbody.empty();
            
            if (response.clippings.length === 0) {
                tbody.append(`
                    <tr>
                        <td colspan="9" class="text-center text-muted">
                            <i class="fas fa-info-circle me-2"></i>No news clippings found for the selected criteria.
                        </td>
                    </tr>
                `);
                
                // Reset summary
                $('#totalClippings').text('0');
                $('#totalNewspapers').text('0');
                $('#totalDepartments').text('0');
                $('#dateRange').text('-');
                return;
            }
            
            // Populate table
            response.clippings.forEach(clipping => {
                tbody.append(`
                    <tr>
                        <td>${clipping.clipping_no}</td>
                        <td>${clipping.date}</td>
                        <td>${clipping.newspaper_name}</td>
                        <td><span class="badge bg-info">${clipping.news_type}</span></td>
                        <td>${clipping.pages}</td>
                        <td>${clipping.college_name}</td>
                        <td>${clipping.department_name}</td>
                        <td><small>${clipping.keywords.substring(0, 30)}...</small></td>
                        <td><small>${clipping.abstract.substring(0, 50)}...</small></td>
                    </tr>
                `);
            });
            
            // Update summary statistics
            const uniqueNewspapers = [...new Set(response.clippings.map(c => c.newspaper_name))].length;
            const uniqueDepartments = [...new Set(response.clippings.map(c => c.department_name))].length;
            
            $('#totalClippings').text(response.total_count);
            $('#totalNewspapers').text(uniqueNewspapers);
            $('#totalDepartments').text(uniqueDepartments);
            
            if (start_date && end_date) {
                $('#dateRange').text(`${start_date} to ${end_date}`);
            } else {
                $('#dateRange').text('All Dates');
            }
        },
        error: function() {
            $('#reportTableBody').html(`
                <tr>
                    <td colspan="9" class="text-center text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>Error generating report.
                    </td>
                </tr>
            `);
        }
    });
}

function resetFilters() {
    $('#filterForm')[0].reset();
    $('#department').prop('disabled', true).html('<option value="">Select College First</option>');
    $('#reportResults').hide();
    
    // Reset default dates
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
    
    $('#endDate').val(today.toISOString().split('T')[0]);
    $('#startDate').val(thirtyDaysAgo.toISOString().split('T')[0]);
}

function exportReport(format) {
    alert(`Exporting News Clipping Report as ${format.toUpperCase()}...`);
}
</script>
{% endblock %}
