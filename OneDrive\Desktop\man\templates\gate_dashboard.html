<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gate Entry Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .dashboard-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .scanner-section {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
            padding: 2rem;
            margin: 2rem 0;
        }
        
        .scanner-input {
            font-size: 1.5rem;
            padding: 1rem;
            border: 3px solid #667eea;
            border-radius: 0.5rem;
            text-align: center;
            background: #f8f9fa;
        }
        
        .scanner-input:focus {
            border-color: #764ba2;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            background: white;
        }
        
        .stats-card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            text-align: center;
            margin-bottom: 1rem;
            transition: all 0.2s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.15);
        }
        
        .stats-card h3 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .stats-card p {
            color: #6c757d;
            margin: 0;
            font-weight: 500;
        }
        
        .entry-log {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
            overflow: hidden;
        }
        
        .entry-log-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 1.5rem;
        }
        
        .entry-item {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e9ecef;
            transition: all 0.2s ease;
        }
        
        .entry-item:hover {
            background: #f8f9fa;
        }
        
        .entry-item:last-child {
            border-bottom: none;
        }
        
        .entry-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .entry-in {
            background: #d4edda;
            color: #155724;
        }
        
        .entry-out {
            background: #f8d7da;
            color: #721c24;
        }
        
        .scan-result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 0.5rem;
            display: none;
        }
        
        .scan-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .scan-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .scanner-icon {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .logout-btn {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .logout-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(220, 53, 69, 0.3);
            color: white;
        }
        
        .time-display {
            font-size: 1.2rem;
            font-weight: 600;
            color: #667eea;
        }
        
        .manual-entry-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-weight: 500;
            margin-left: 0.5rem;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 0.5rem;
        }
        
        .status-online {
            background: #28a745;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="dashboard-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0">
                        <i class="fas fa-door-open me-2"></i>Gate Entry Dashboard
                        <span class="status-indicator status-online"></span>
                    </h4>
                    <small class="text-muted">Operator: {{ session.gate_full_name }}</small>
                </div>
                <div class="d-flex align-items-center">
                    <div class="time-display me-3" id="currentTime"></div>
                    <a href="{{ url_for('gate_logout') }}" class="btn logout-btn">
                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="container-fluid py-4">
        <!-- Statistics Row -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="stats-card">
                    <h3>{{ total_entries_today }}</h3>
                    <p><i class="fas fa-calendar-day me-2"></i>Today's Entries</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card">
                    <h3>{{ currently_inside }}</h3>
                    <p><i class="fas fa-users me-2"></i>Currently Inside</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card">
                    <h3 id="liveTime">--:--</h3>
                    <p><i class="fas fa-clock me-2"></i>Current Time</p>
                </div>
            </div>
        </div>
        
        <!-- Scanner Section -->
        <div class="row">
            <div class="col-md-6">
                <div class="scanner-section">
                    <div class="text-center">
                        <div class="scanner-icon">
                            <i class="fas fa-qrcode"></i>
                        </div>
                        <h4>Barcode Scanner</h4>
                        <p class="text-muted mb-4">Scan student ID barcode or enter manually</p>
                        
                        <div class="input-group mb-3">
                            <input type="text" class="form-control scanner-input" id="barcodeInput" 
                                   placeholder="Scan barcode or type User ID" autofocus>
                            <button class="btn manual-entry-btn" type="button" onclick="processEntry()">
                                <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                        
                        <div class="scan-result" id="scanResult"></div>
                    </div>
                </div>
            </div>
            
            <!-- Recent Entries -->
            <div class="col-md-6">
                <div class="entry-log">
                    <div class="entry-log-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>Recent Entries
                            <button class="btn btn-sm btn-outline-light float-end" onclick="refreshEntries()">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </h5>
                    </div>
                    <div class="entry-log-body" style="max-height: 400px; overflow-y: auto;">
                        {% if today_entries %}
                            {% for entry in today_entries %}
                            <div class="entry-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>{{ entry.student.name if entry.student else entry.user_id }}</strong>
                                        <br>
                                        <small class="text-muted">{{ entry.user_id }}</small>
                                    </div>
                                    <div class="text-end">
                                        <span class="entry-badge entry-{{ entry.entry_type.lower() }}">
                                            {{ entry.entry_type }}
                                        </span>
                                        <br>
                                        <small class="text-muted">
                                            {{ entry.entry_time.strftime('%H:%M:%S') }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                        <div class="entry-item text-center text-muted">
                            <i class="fas fa-info-circle me-2"></i>No entries today
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            updateTime();
            setInterval(updateTime, 1000);
            
            // Auto-focus on barcode input
            $('#barcodeInput').focus();
            
            // Handle Enter key press
            $('#barcodeInput').on('keypress', function(e) {
                if (e.which === 13) {
                    processEntry();
                }
            });
            
            // Auto-refresh entries every 30 seconds
            setInterval(refreshEntries, 30000);
        });
        
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString();
            const dateString = now.toLocaleDateString();
            
            $('#currentTime').text(dateString + ' ' + timeString);
            $('#liveTime').text(timeString);
        }
        
        function processEntry() {
            const userId = $('#barcodeInput').val().trim();
            
            if (!userId) {
                showResult('Please scan a barcode or enter User ID', 'error');
                return;
            }
            
            // Show processing
            showResult('Processing...', 'info');
            
            $.ajax({
                url: '/gate-scan',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ user_id: userId }),
                success: function(response) {
                    if (response.success) {
                        showResult(
                            `<strong>${response.message}</strong><br>
                             Student: ${response.student_name}<br>
                             Time: ${response.time}<br>
                             Type: ${response.entry_type}`,
                            'success'
                        );
                        
                        // Clear input and refresh entries
                        $('#barcodeInput').val('').focus();
                        setTimeout(refreshEntries, 1000);
                        
                    } else {
                        showResult(response.message, 'error');
                    }
                },
                error: function(xhr) {
                    const response = JSON.parse(xhr.responseText);
                    showResult('Error: ' + response.error, 'error');
                }
            });
        }
        
        function showResult(message, type) {
            const resultDiv = $('#scanResult');
            resultDiv.removeClass('scan-success scan-error');
            
            if (type === 'success') {
                resultDiv.addClass('scan-success');
            } else if (type === 'error') {
                resultDiv.addClass('scan-error');
            }
            
            resultDiv.html(message).show();
            
            // Auto-hide after 5 seconds for success messages
            if (type === 'success') {
                setTimeout(() => {
                    resultDiv.fadeOut();
                }, 5000);
            }
        }
        
        function refreshEntries() {
            location.reload();
        }
    </script>
</body>
</html>
