{% extends "base.html" %}

{% block title %}Bulk Operations - Librarian{% endblock %}

{% block sidebar %}
<!-- Dashboard -->
<a class="nav-link" href="{{ url_for('librarian_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>

<!-- Management Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-info" data-bs-toggle="collapse" href="#managementSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-cogs me-2"></i>Management
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="managementSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('librarian_books') }}">
                <i class="fas fa-book me-2"></i>Manage Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_ebooks') }}">
                <i class="fas fa-tablet-alt me-2"></i>Manage E-Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_students') }}">
                <i class="fas fa-graduation-cap me-2"></i>View Students
            </a>
        </div>
    </div>
</div>

<!-- Circulation Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-primary" data-bs-toggle="collapse" href="#circulationSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-exchange-alt me-2"></i>Circulation
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="circulationSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('librarian_circulation_counter') }}">
                <i class="fas fa-desktop me-2"></i>Counter Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_issue_history') }}">
                <i class="fas fa-history me-2"></i>Issue History
            </a>
            <a class="nav-link py-1 active" href="{{ url_for('librarian_bulk_operations') }}">
                <i class="fas fa-layer-group me-2"></i>Bulk Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_payment_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Payment Management
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_binding_management') }}">
                <i class="fas fa-tools me-2"></i>Binding & Maintenance
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_fine_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Fine Management
            </a>
        </div>
    </div>
</div>

<!-- Reports Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-success" data-bs-toggle="collapse" href="#reportsSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-chart-bar me-2"></i>Reports
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="reportsSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('librarian_counter_report') }}">
                <i class="fas fa-calculator me-2"></i>Counter Report
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-layer-group me-3"></i>Bulk Operations</h2>
    <div>
        <button class="btn btn-outline-secondary btn-custom" onclick="clearAll()">
            <i class="fas fa-refresh me-2"></i>Clear All
        </button>
    </div>
</div>

<!-- Operation Type Tabs -->
<ul class="nav nav-tabs mb-4" id="bulkTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="bulk-issue-tab" data-bs-toggle="tab" data-bs-target="#bulk-issue" type="button" role="tab">
            <i class="fas fa-arrow-up me-2"></i>Bulk Issue
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="bulk-return-tab" data-bs-toggle="tab" data-bs-target="#bulk-return" type="button" role="tab">
            <i class="fas fa-arrow-down me-2"></i>Bulk Return
        </button>
    </li>
</ul>

<div class="tab-content" id="bulkTabContent">
    <!-- Bulk Issue Tab -->
    <div class="tab-pane fade show active" id="bulk-issue" role="tabpanel">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-arrow-up me-2"></i>Bulk Issue Books</h5>
                    </div>
                    <div class="card-body">
                        <form id="bulkIssueForm">
                            <!-- Student Selection -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="bulkStudentSearch" class="form-label">Student ID/Name <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                                        <input type="text" class="form-control" id="bulkStudentSearch" placeholder="Scan or type student ID" required>
                                        <button type="button" class="btn btn-outline-secondary" onclick="searchBulkStudent()">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="bulkDueDate" class="form-label">Due Date</label>
                                    <input type="date" class="form-control" id="bulkDueDate">
                                </div>
                            </div>
                            
                            <div id="bulkStudentInfo" class="alert alert-info" style="display: none;">
                                <h6><i class="fas fa-user me-2"></i>Student Information</h6>
                                <div id="bulkStudentDetails"></div>
                            </div>
                            
                            <!-- Book Scanning Area -->
                            <div class="mb-3">
                                <label for="bookScanner" class="form-label">Scan Books <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-barcode"></i></span>
                                    <input type="text" class="form-control" id="bookScanner" placeholder="Scan book access numbers one by one">
                                    <button type="button" class="btn btn-primary" onclick="addBookToBulk()">
                                        <i class="fas fa-plus"></i> Add
                                    </button>
                                </div>
                                <small class="text-muted">Scan or type book access numbers and click Add for each book</small>
                            </div>
                            
                            <!-- Scanned Books List -->
                            <div class="card mb-3">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0"><i class="fas fa-list me-2"></i>Books to Issue (<span id="bookCount">0</span>)</h6>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearBookList()">
                                        <i class="fas fa-trash"></i> Clear All
                                    </button>
                                </div>
                                <div class="card-body">
                                    <div id="scannedBooksList" class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Access No.</th>
                                                    <th>Title</th>
                                                    <th>Author</th>
                                                    <th>Status</th>
                                                    <th>Action</th>
                                                </tr>
                                            </thead>
                                            <tbody id="bookListBody">
                                                <tr id="emptyRow">
                                                    <td colspan="5" class="text-center text-muted">No books scanned yet</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg" id="bulkIssueBtn" disabled>
                                    <i class="fas fa-check me-2"></i>Issue All Books
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Bulk Issue Info</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <small class="text-muted">Maximum Books per Student</small>
                            <div class="fw-bold">5 Books</div>
                        </div>
                        <div class="mb-3">
                            <small class="text-muted">Default Loan Period</small>
                            <div class="fw-bold">14 Days</div>
                        </div>
                        <div class="mb-3">
                            <small class="text-muted">Books in Queue</small>
                            <div class="fw-bold text-primary" id="queueCount">0</div>
                        </div>
                        <div class="mb-3">
                            <small class="text-muted">Valid Books</small>
                            <div class="fw-bold text-success" id="validCount">0</div>
                        </div>
                        <div class="mb-3">
                            <small class="text-muted">Invalid Books</small>
                            <div class="fw-bold text-danger" id="invalidCount">0</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bulk Return Tab -->
    <div class="tab-pane fade" id="bulk-return" role="tabpanel">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-arrow-down me-2"></i>Bulk Return Books</h5>
                    </div>
                    <div class="card-body">
                        <form id="bulkReturnForm">
                            <!-- Book Scanning Area -->
                            <div class="mb-3">
                                <label for="returnBookScanner" class="form-label">Scan Books to Return</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-barcode"></i></span>
                                    <input type="text" class="form-control" id="returnBookScanner" placeholder="Scan book access numbers one by one">
                                    <button type="button" class="btn btn-success" onclick="addBookToReturn()">
                                        <i class="fas fa-plus"></i> Add
                                    </button>
                                </div>
                                <small class="text-muted">Scan or type book access numbers and click Add for each book</small>
                            </div>
                            
                            <!-- Scanned Returns List -->
                            <div class="card mb-3">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0"><i class="fas fa-list me-2"></i>Books to Return (<span id="returnBookCount">0</span>)</h6>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearReturnList()">
                                        <i class="fas fa-trash"></i> Clear All
                                    </button>
                                </div>
                                <div class="card-body">
                                    <div id="scannedReturnsList" class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Access No.</th>
                                                    <th>Title</th>
                                                    <th>Student</th>
                                                    <th>Due Date</th>
                                                    <th>Fine</th>
                                                    <th>Action</th>
                                                </tr>
                                            </thead>
                                            <tbody id="returnListBody">
                                                <tr id="emptyReturnRow">
                                                    <td colspan="6" class="text-center text-muted">No books scanned yet</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Fine Summary -->
                            <div id="fineSummary" class="alert alert-warning" style="display: none;">
                                <h6><i class="fas fa-money-bill-wave me-2"></i>Fine Summary</h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <strong>Total Fine: ₹<span id="totalFine">0.00</span></strong>
                                    </div>
                                    <div class="col-md-4">
                                        <strong>Overdue Books: <span id="overdueCount">0</span></strong>
                                    </div>
                                    <div class="col-md-4">
                                        <strong>On Time: <span id="onTimeCount">0</span></strong>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="text-center">
                                <button type="submit" class="btn btn-success btn-lg" id="bulkReturnBtn" disabled>
                                    <i class="fas fa-check me-2"></i>Return All Books
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Return Summary</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <small class="text-muted">Books in Queue</small>
                            <div class="fw-bold text-primary" id="returnQueueCount">0</div>
                        </div>
                        <div class="mb-3">
                            <small class="text-muted">Valid Returns</small>
                            <div class="fw-bold text-success" id="validReturnCount">0</div>
                        </div>
                        <div class="mb-3">
                            <small class="text-muted">Invalid Returns</small>
                            <div class="fw-bold text-danger" id="invalidReturnCount">0</div>
                        </div>
                        <div class="mb-3">
                            <small class="text-muted">Total Fine Amount</small>
                            <div class="fw-bold text-warning">₹<span id="totalFineAmount">0.00</span></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Set default due date (14 days from now)
    const defaultDueDate = new Date();
    defaultDueDate.setDate(defaultDueDate.getDate() + 14);
    $('#bulkDueDate').val(defaultDueDate.toISOString().split('T')[0]);
    
    // Auto-focus on scanner inputs
    $('#bookScanner').focus();
    
    // Handle Enter key for scanners
    $('#bookScanner').on('keypress', function(e) {
        if (e.which === 13) {
            e.preventDefault();
            addBookToBulk();
        }
    });
    
    $('#returnBookScanner').on('keypress', function(e) {
        if (e.which === 13) {
            e.preventDefault();
            addBookToReturn();
        }
    });
});

let scannedBooks = [];
let scannedReturns = [];

function searchBulkStudent() {
    const query = $('#bulkStudentSearch').val();
    if (query) {
        $('#bulkStudentInfo').show();
        $('#bulkStudentDetails').html('<strong>John Doe</strong><br>ID: ' + query + '<br>Department: CSE<br>Current Books: 2/5');
    }
}

function addBookToBulk() {
    const accessNo = $('#bookScanner').val().trim();
    if (!accessNo) return;
    
    if (scannedBooks.find(book => book.accessNo === accessNo)) {
        alert('Book already scanned!');
        $('#bookScanner').val('').focus();
        return;
    }
    
    const book = {
        accessNo: accessNo,
        title: 'Sample Book Title',
        author: 'Sample Author',
        status: 'Available',
        valid: true
    };
    
    scannedBooks.push(book);
    updateBookList();
    $('#bookScanner').val('').focus();
}

function updateBookList() {
    const tbody = $('#bookListBody');
    tbody.empty();
    
    if (scannedBooks.length === 0) {
        tbody.append('<tr id="emptyRow"><td colspan="5" class="text-center text-muted">No books scanned yet</td></tr>');
    } else {
        scannedBooks.forEach((book, index) => {
            const statusBadge = book.valid ? 
                '<span class="badge bg-success">Available</span>' : 
                '<span class="badge bg-danger">Not Available</span>';
            
            tbody.append(`
                <tr>
                    <td>${book.accessNo}</td>
                    <td>${book.title}</td>
                    <td>${book.author}</td>
                    <td>${statusBadge}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-danger" onclick="removeBook(${index})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `);
        });
    }
    
    $('#bookCount').text(scannedBooks.length);
    $('#queueCount').text(scannedBooks.length);
    $('#validCount').text(scannedBooks.filter(b => b.valid).length);
    $('#invalidCount').text(scannedBooks.filter(b => !b.valid).length);
    
    $('#bulkIssueBtn').prop('disabled', scannedBooks.length === 0 || !$('#bulkStudentSearch').val());
}

function addBookToReturn() {
    const accessNo = $('#returnBookScanner').val().trim();
    if (!accessNo) return;
    
    if (scannedReturns.find(book => book.accessNo === accessNo)) {
        alert('Book already scanned!');
        $('#returnBookScanner').val('').focus();
        return;
    }
    
    const dueDate = new Date('2024-01-15');
    const today = new Date();
    const isOverdue = today > dueDate;
    const daysOverdue = isOverdue ? Math.ceil((today - dueDate) / (1000 * 60 * 60 * 24)) : 0;
    const fine = daysOverdue * 2;
    
    const book = {
        accessNo: accessNo,
        title: 'Sample Book Title',
        student: 'John Doe',
        dueDate: dueDate.toLocaleDateString(),
        fine: fine,
        isOverdue: isOverdue,
        valid: true
    };
    
    scannedReturns.push(book);
    updateReturnList();
    $('#returnBookScanner').val('').focus();
}

function updateReturnList() {
    const tbody = $('#returnListBody');
    tbody.empty();
    
    if (scannedReturns.length === 0) {
        tbody.append('<tr id="emptyReturnRow"><td colspan="6" class="text-center text-muted">No books scanned yet</td></tr>');
    } else {
        scannedReturns.forEach((book, index) => {
            const fineBadge = book.fine > 0 ? 
                `<span class="badge bg-danger">₹${book.fine}</span>` : 
                '<span class="badge bg-success">₹0</span>';
            
            tbody.append(`
                <tr>
                    <td>${book.accessNo}</td>
                    <td>${book.title}</td>
                    <td>${book.student}</td>
                    <td>${book.dueDate}</td>
                    <td>${fineBadge}</td>
                    <td>
                        <button class="btn btn-sm btn-outline-danger" onclick="removeReturn(${index})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `);
        });
    }
    
    const totalFine = scannedReturns.reduce((sum, book) => sum + book.fine, 0);
    const overdueCount = scannedReturns.filter(b => b.isOverdue).length;
    const onTimeCount = scannedReturns.length - overdueCount;
    
    $('#returnBookCount').text(scannedReturns.length);
    $('#returnQueueCount').text(scannedReturns.length);
    $('#validReturnCount').text(scannedReturns.filter(b => b.valid).length);
    $('#invalidReturnCount').text(scannedReturns.filter(b => !b.valid).length);
    $('#totalFineAmount').text(totalFine.toFixed(2));
    
    if (scannedReturns.length > 0) {
        $('#fineSummary').show();
        $('#totalFine').text(totalFine.toFixed(2));
        $('#overdueCount').text(overdueCount);
        $('#onTimeCount').text(onTimeCount);
    } else {
        $('#fineSummary').hide();
    }
    
    $('#bulkReturnBtn').prop('disabled', scannedReturns.length === 0);
}

function removeBook(index) {
    scannedBooks.splice(index, 1);
    updateBookList();
}

function removeReturn(index) {
    scannedReturns.splice(index, 1);
    updateReturnList();
}

function clearBookList() {
    scannedBooks = [];
    updateBookList();
}

function clearReturnList() {
    scannedReturns = [];
    updateReturnList();
}

function clearAll() {
    scannedBooks = [];
    scannedReturns = [];
    updateBookList();
    updateReturnList();
    $('#bulkStudentSearch').val('');
    $('#bulkStudentInfo').hide();
    $('#fineSummary').hide();
}

$('#bulkIssueForm').on('submit', function(e) {
    e.preventDefault();
    if (scannedBooks.length > 0) {
        alert(`Bulk issue processed successfully! ${scannedBooks.length} books issued to ${$('#bulkStudentSearch').val()}.`);
        clearAll();
    }
});

$('#bulkReturnForm').on('submit', function(e) {
    e.preventDefault();
    if (scannedReturns.length > 0) {
        const totalFine = scannedReturns.reduce((sum, book) => sum + book.fine, 0);
        alert(`Bulk return processed successfully! ${scannedReturns.length} books returned with total fine of ₹${totalFine.toFixed(2)}.`);
        clearAll();
    }
});
</script>
{% endblock %}
