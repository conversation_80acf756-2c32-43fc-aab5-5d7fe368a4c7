<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            border: none;
        }
        .btn-custom {
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
        }
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        .role-card {
            transition: transform 0.3s ease;
            cursor: pointer;
        }
        .role-card:hover {
            transform: translateY(-5px);
        }
        .hero-section {
            padding: 80px 0;
        }
        .display-4 {
            font-weight: 700;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .lead {
            color: rgba(255,255,255,0.9);
            font-size: 1.2rem;
        }
    </style>
</head>
<body>
    <div class="hero-section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8 text-center">
                    <h1 class="display-4 mb-4">
                        <i class="fas fa-book-open me-3"></i>
                        Smart Library Management System
                    </h1>
                    <p class="lead mb-5">Manage your library efficiently with role-based access control</p>
                </div>
            </div>
            
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="row g-4">
                        <!-- Unified Login -->
                        <div class="col-md-8 col-lg-6 mx-auto">
                            <div class="card role-card h-100" onclick="location.href='{{ url_for('login') }}'">
                                <div class="card-body text-center p-5">
                                    <div class="mb-4">
                                        <i class="fas fa-sign-in-alt fa-4x text-primary"></i>
                                    </div>
                                    <h4 class="card-title mb-3">Login to Library System</h4>
                                    <p class="card-text text-muted mb-4">
                                        Access the library management system with your credentials. 
                                        All user types (Admin, Librarian, Student) can login here.
                                    </p>
                                    <a href="{{ url_for('login') }}" class="btn btn-primary btn-custom btn-lg">
                                        <i class="fas fa-sign-in-alt me-2"></i>Login Now
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Section -->
    <div class="container-fluid py-5" style="background: rgba(255,255,255,0.1);">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="text-white mb-4">System Features</h2>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-md-3 text-center">
                    <i class="fas fa-books fa-3x text-white mb-3"></i>
                    <h5 class="text-white">Book Management</h5>
                    <p class="text-white-50">Complete CRUD operations for book inventory</p>
                </div>
                <div class="col-md-3 text-center">
                    <i class="fas fa-users fa-3x text-white mb-3"></i>
                    <h5 class="text-white">User Management</h5>
                    <p class="text-white-50">Role-based access for different user types</p>
                </div>
                <div class="col-md-3 text-center">
                    <i class="fas fa-exchange-alt fa-3x text-white mb-3"></i>
                    <h5 class="text-white">Issue & Return</h5>
                    <p class="text-white-50">Streamlined book issuing and return process</p>
                </div>
                <div class="col-md-3 text-center">
                    <i class="fas fa-chart-bar fa-3x text-white mb-3"></i>
                    <h5 class="text-white">Reports & Analytics</h5>
                    <p class="text-white-50">Comprehensive reporting and fine calculation</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
