{% extends "base.html" %}

{% block title %}Bulk Add E-Books - Admin{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>
<a class="nav-link" href="{{ url_for('admin_books') }}">
    <i class="fas fa-book me-2"></i>Manage Books
</a>
<a class="nav-link active" href="{{ url_for('admin_ebooks') }}">
    <i class="fas fa-tablet-alt me-2"></i>Manage E-Books
</a>
<a class="nav-link" href="{{ url_for('admin_librarians') }}">
    <i class="fas fa-user-tie me-2"></i>Manage Librarians
</a>
<a class="nav-link" href="{{ url_for('admin_students') }}">
    <i class="fas fa-graduation-cap me-2"></i>Manage Students
</a>
<a class="nav-link" href="{{ url_for('admin_reports') }}">
    <i class="fas fa-chart-bar me-2"></i>Reports
</a>
<a class="nav-link" href="{{ url_for('admin_issue_history') }}">
    <i class="fas fa-history me-2"></i>Issue History
</a>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-file-upload me-3"></i>Bulk Add E-Books</h2>
    <div>
        <a href="{{ url_for('admin_add_ebook') }}" class="btn btn-primary btn-custom me-2">
            <i class="fas fa-plus me-2"></i>Add Single E-Book
        </a>
        <a href="{{ url_for('admin_ebooks') }}" class="btn btn-secondary btn-custom">
            <i class="fas fa-arrow-left me-2"></i>Back to E-Books
        </a>
    </div>
</div>

<!-- Upload Form -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-upload me-2"></i>Upload E-Books Excel File</h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    <div class="mb-4">
                        <label for="file" class="form-label">Select Excel File <span class="text-danger">*</span></label>
                        <input type="file" class="form-control" id="file" name="file" accept=".xlsx,.xls" required>
                        <div class="form-text">Upload an Excel file (.xlsx or .xls) containing e-book information</div>
                    </div>
                    
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>File Format Requirements:</h6>
                        <ul class="mb-0">
                            <li>File must be in Excel format (.xlsx or .xls)</li>
                            <li>First row should contain column headers</li>
                            <li>Required columns: access_no, title, author, publisher, subject, department, category, file_format</li>
                            <li>Optional columns: file_size, download_url, isbn, pages, language, description</li>
                        </ul>
                    </div>
                    
                    <div class="text-center">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-upload me-2"></i>Upload E-Books
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-download me-2"></i>Download Template</h5>
            </div>
            <div class="card-body">
                <p>Download the Excel template to ensure your file has the correct format:</p>
                <a href="{{ url_for('download_ebook_template') }}" class="btn btn-success w-100 mb-3">
                    <i class="fas fa-download me-2"></i>Download Template
                </a>
                
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Important Notes:</h6>
                    <ul class="mb-0 small">
                        <li>Access numbers must be unique</li>
                        <li>Department codes: CSE, IT, ECE, EEE, MECH, CIVIL</li>
                        <li>File formats: PDF, EPUB, MOBI, DOC, DOCX, TXT</li>
                        <li>Language defaults to English if not specified</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-list me-2"></i>Column Reference</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>Column</th>
                            <th>Required</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr><td>access_no</td><td><span class="badge bg-danger">Yes</span></td></tr>
                        <tr><td>title</td><td><span class="badge bg-danger">Yes</span></td></tr>
                        <tr><td>author</td><td><span class="badge bg-danger">Yes</span></td></tr>
                        <tr><td>publisher</td><td><span class="badge bg-danger">Yes</span></td></tr>
                        <tr><td>subject</td><td><span class="badge bg-danger">Yes</span></td></tr>
                        <tr><td>department</td><td><span class="badge bg-danger">Yes</span></td></tr>
                        <tr><td>category</td><td><span class="badge bg-danger">Yes</span></td></tr>
                        <tr><td>file_format</td><td><span class="badge bg-danger">Yes</span></td></tr>
                        <tr><td>file_size</td><td><span class="badge bg-secondary">No</span></td></tr>
                        <tr><td>download_url</td><td><span class="badge bg-secondary">No</span></td></tr>
                        <tr><td>isbn</td><td><span class="badge bg-secondary">No</span></td></tr>
                        <tr><td>pages</td><td><span class="badge bg-secondary">No</span></td></tr>
                        <tr><td>language</td><td><span class="badge bg-secondary">No</span></td></tr>
                        <tr><td>description</td><td><span class="badge bg-secondary">No</span></td></tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // File validation
    $('#file').change(function() {
        const file = this.files[0];
        if (file) {
            const fileName = file.name;
            const fileExtension = fileName.split('.').pop().toLowerCase();
            
            if (!['xlsx', 'xls'].includes(fileExtension)) {
                alert('Please select a valid Excel file (.xlsx or .xls)');
                this.value = '';
                return;
            }
            
            // Check file size (max 10MB)
            if (file.size > 10 * 1024 * 1024) {
                alert('File size should not exceed 10MB');
                this.value = '';
                return;
            }
        }
    });
});
</script>
{% endblock %}
