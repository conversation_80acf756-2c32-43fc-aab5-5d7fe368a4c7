{% extends "base.html" %}

{% block title %}Manage Books - Admin{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>
<a class="nav-link active" href="{{ url_for('admin_books') }}">
    <i class="fas fa-book me-2"></i>Manage Books
</a>
<a class="nav-link" href="{{ url_for('admin_librarians') }}">
    <i class="fas fa-user-tie me-2"></i>Manage Librarians
</a>
<a class="nav-link" href="{{ url_for('admin_students') }}">
    <i class="fas fa-graduation-cap me-2"></i>Manage Students
</a>
<a class="nav-link" href="{{ url_for('admin_bulk_users') }}">
    <i class="fas fa-upload me-2"></i>Bulk Create Users
</a>
<a class="nav-link" href="{{ url_for('admin_bulk_books') }}">
    <i class="fas fa-file-upload me-2"></i>Bulk Add Books
</a>
<a class="nav-link" href="{{ url_for('admin_settings') }}">
    <i class="fas fa-cog me-2"></i>Library Settings
</a>
<a class="nav-link" href="{{ url_for('admin_issue_history') }}">
    <i class="fas fa-history me-2"></i>Issue History
</a>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-book me-3"></i>Manage Books</h2>
    <div>
        <a href="{{ url_for('admin_bulk_books') }}" class="btn btn-info btn-custom me-2">
            <i class="fas fa-file-upload me-2"></i>Bulk Add Books
        </a>
        <a href="{{ url_for('admin_add_book') }}" class="btn btn-primary btn-custom">
            <i class="fas fa-plus me-2"></i>Add New Book
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">Book Inventory</h5>
    </div>
    <div class="card-body">
        {% if books %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>ID</th>
                        <th>Title</th>
                        <th>Author</th>
                        <th>Category</th>
                        <th>Total Qty</th>
                        <th>Available</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for book in books %}
                    <tr>
                        <td>{{ book.book_id }}</td>
                        <td><strong>{{ book.title }}</strong></td>
                        <td>{{ book.author }}</td>
                        <td>
                            <span class="badge bg-secondary">{{ book.category }}</span>
                        </td>
                        <td>{{ book.quantity }}</td>
                        <td>
                            <span class="badge {% if book.available_count > 0 %}bg-success{% else %}bg-danger{% endif %}">
                                {{ book.available_count }}
                            </span>
                        </td>
                        <td>
                            {% if book.available_count > 0 %}
                                <span class="badge bg-success">Available</span>
                            {% else %}
                                <span class="badge bg-warning">All Issued</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('admin_edit_book', book_id=book.book_id) }}" 
                                   class="btn btn-sm btn-outline-primary" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ url_for('admin_delete_book', book_id=book.book_id) }}" 
                                   class="btn btn-sm btn-outline-danger" 
                                   onclick="return confirm('Are you sure you want to delete this book?')" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-book fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No books found</h5>
            <p class="text-muted">Start by adding your first book to the library.</p>
            <a href="{{ url_for('admin_add_book') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Add First Book
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Book Statistics -->
<div class="row mt-4">
    <div class="col-md-4">
        <div class="card stat-card stat-card-primary">
            <div class="card-body text-center">
                <i class="fas fa-book fa-2x text-primary mb-2"></i>
                <h4>{{ books|length }}</h4>
                <p class="text-muted mb-0">Total Books</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card stat-card stat-card-success">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                <h4>{{ books|selectattr('available_count', 'greaterthan', 0)|list|length }}</h4>
                <p class="text-muted mb-0">Available Books</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card stat-card stat-card-warning">
            <div class="card-body text-center">
                <i class="fas fa-exclamation-circle fa-2x text-warning mb-2"></i>
                <h4>{{ books|selectattr('available_count', 'equalto', 0)|list|length }}</h4>
                <p class="text-muted mb-0">Out of Stock</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Add search functionality
    $('#bookSearch').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
        });
    });
});
</script>
{% endblock %}
