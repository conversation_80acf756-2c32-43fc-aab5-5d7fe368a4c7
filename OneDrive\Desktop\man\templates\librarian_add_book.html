{% extends "base.html" %}

{% block title %}Add Book - Librarian{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('librarian_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>
<a class="nav-link active" href="{{ url_for('librarian_books') }}">
    <i class="fas fa-book me-2"></i>Manage Books
</a>
<a class="nav-link" href="{{ url_for('librarian_students') }}">
    <i class="fas fa-graduation-cap me-2"></i>Manage Students
</a>
<a class="nav-link" href="{{ url_for('librarian_bulk_users') }}">
    <i class="fas fa-upload me-2"></i>Bulk Create Users
</a>
<a class="nav-link" href="{{ url_for('librarian_bulk_books') }}">
    <i class="fas fa-file-upload me-2"></i>Bulk Add Books
</a>
<a class="nav-link" href="{{ url_for('librarian_issue_return') }}">
    <i class="fas fa-exchange-alt me-2"></i>Issue/Return Books
</a>
<a class="nav-link" href="{{ url_for('librarian_issue_history') }}">
    <i class="fas fa-history me-2"></i>Issue History
</a>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-plus me-3"></i>Add New Book</h2>
    <div>
        <a href="{{ url_for('librarian_bulk_books') }}" class="btn btn-info btn-custom me-2">
            <i class="fas fa-file-upload me-2"></i>Bulk Add Books
        </a>
        <a href="{{ url_for('librarian_books') }}" class="btn btn-secondary btn-custom">
            <i class="fas fa-arrow-left me-2"></i>Back to Books
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-book me-2"></i>Book Information</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <!-- Access Number and Title -->
                        <div class="col-md-6 mb-3">
                            <label for="access_no" class="form-label">
                                <i class="fas fa-hashtag me-2"></i>Access Number <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="access_no" name="access_no" required placeholder="e.g., ACC00001">
                            <div class="form-text">Unique identifier for the book</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="title" class="form-label">
                                <i class="fas fa-heading me-2"></i>Book Title <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="title" name="title" required placeholder="Enter book title">
                        </div>
                    </div>

                    <div class="row">
                        <!-- Author and Publisher -->
                        <div class="col-md-6 mb-3">
                            <label for="author" class="form-label">
                                <i class="fas fa-user-edit me-2"></i>Author <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="author" name="author" required placeholder="Enter author name">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="publisher" class="form-label">
                                <i class="fas fa-building me-2"></i>Publisher <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="publisher" name="publisher" required placeholder="Enter publisher name">
                        </div>
                    </div>

                    <div class="row">
                        <!-- Subject and Department -->
                        <div class="col-md-6 mb-3">
                            <label for="subject" class="form-label">
                                <i class="fas fa-bookmark me-2"></i>Subject <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="subject" name="subject" required placeholder="Enter subject">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="college" class="form-label">
                                <i class="fas fa-university me-2"></i>College <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="college" name="college" required>
                                <option value="">Select College</option>
                                {% for college_id, college_name in colleges %}
                                <option value="{{ college_id }}">{{ college_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Department -->
                        <div class="col-md-6 mb-3">
                            <label for="department" class="form-label">
                                <i class="fas fa-building me-2"></i>Department <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="department" name="department" required disabled>
                                <option value="">Select College First</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <!-- Empty column for spacing -->
                        </div>
                    </div>

                    <div class="row">
                        <!-- Category and Location -->
                        <div class="col-md-6 mb-3">
                            <label for="category" class="form-label">
                                <i class="fas fa-tags me-2"></i>Category <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="category" name="category" required>
                                <option value="">Select Category</option>
                                <option value="Textbook">Textbook</option>
                                <option value="Reference">Reference Book</option>
                                <option value="Laboratory Manual">Laboratory Manual</option>
                                <option value="Journal">Journal</option>
                                <option value="Magazine">Magazine</option>
                                <option value="Thesis">Thesis</option>
                                <option value="Project Report">Project Report</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="location" class="form-label">
                                <i class="fas fa-map-marker-alt me-2"></i>Location <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="location" name="location" required placeholder="e.g., Section A, Shelf 1">
                            <div class="form-text">Physical location in the library</div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Copies -->
                        <div class="col-md-6 mb-3">
                            <label for="copies" class="form-label">
                                <i class="fas fa-copy me-2"></i>Number of Copies <span class="text-danger">*</span>
                            </label>
                            <input type="number" class="form-control" id="copies" name="copies" required min="1" placeholder="Enter number of copies">
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('librarian_books') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Add Book
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Guidelines</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-lightbulb me-2"></i>Quick Tips:</h6>
                    <ul class="mb-0 small">
                        <li>Access number must be unique</li>
                        <li>Use standard format: ACC00001, ACC00002</li>
                        <li>Location helps in easy book retrieval</li>
                        <li>Department categorizes books by field</li>
                        <li>Subject helps in search functionality</li>
                    </ul>
                </div>

                <div class="alert alert-success">
                    <h6><i class="fas fa-rocket me-2"></i>Bulk Upload:</h6>
                    <p class="mb-2 small">Need to add multiple books?</p>
                    <a href="{{ url_for('librarian_bulk_books') }}" class="btn btn-sm btn-success">
                        <i class="fas fa-file-upload me-2"></i>Try Bulk Upload
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Load departments when college is selected
    $('#college').change(function() {
        const collegeId = $(this).val();
        const departmentSelect = $('#department');

        if (collegeId) {
            // Enable department dropdown and show loading
            departmentSelect.prop('disabled', false);
            departmentSelect.html('<option value="">Loading departments...</option>');

            // Fetch departments for selected college
            $.ajax({
                url: `/api/departments/${collegeId}`,
                method: 'GET',
                success: function(departments) {
                    departmentSelect.html('<option value="">Select Department</option>');
                    departments.forEach(function(dept) {
                        departmentSelect.append(`<option value="${dept.code}">${dept.code} - ${dept.name}</option>`);
                    });
                },
                error: function() {
                    departmentSelect.html('<option value="">Error loading departments</option>');
                }
            });
        } else {
            // Disable department dropdown if no college selected
            departmentSelect.prop('disabled', true);
            departmentSelect.html('<option value="">Select College First</option>');
        }
    });

    // Auto-generate subject based on department
    $('#department').change(function() {
        const department = $(this).val();
        const departmentText = $(this).find('option:selected').text();

        if (department && !$('#subject').val()) {
            const subjectName = departmentText.split(' - ')[1];
            $('#subject').val(subjectName);
        }
    });
    
    // Auto-generate access number
    function loadNextAccessNumber() {
        $.get('/api/next_access_number', function(data) {
            if (data.success) {
                $('#access_no').val(data.next_access_number);
            }
        });
    }

    // Load next access number on page load
    loadNextAccessNumber();

    // Also load when access number field is focused and empty
    $('#access_no').on('focus', function() {
        if (!$(this).val()) {
            loadNextAccessNumber();
        }
    });
});
</script>
{% endblock %}
