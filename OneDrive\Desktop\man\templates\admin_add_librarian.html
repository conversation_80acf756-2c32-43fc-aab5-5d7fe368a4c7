{% extends "base.html" %}

{% block title %}Add Librarian - Admin{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>
<a class="nav-link" href="{{ url_for('admin_books') }}">
    <i class="fas fa-book me-2"></i>Manage Books
</a>
<a class="nav-link active" href="{{ url_for('admin_librarians') }}">
    <i class="fas fa-user-tie me-2"></i>Manage Librarians
</a>
<a class="nav-link" href="{{ url_for('admin_students') }}">
    <i class="fas fa-graduation-cap me-2"></i>Manage Students
</a>
<a class="nav-link" href="{{ url_for('admin_issue_history') }}">
    <i class="fas fa-history me-2"></i>Issue History
</a>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-user-plus me-3"></i>Add New Librarian</h2>
    <a href="{{ url_for('admin_librarians') }}" class="btn btn-secondary btn-custom">
        <i class="fas fa-arrow-left me-2"></i>Back to Librarians
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user-tie me-2"></i>Librarian Information</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">
                                <i class="fas fa-user me-2"></i>Full Name
                            </label>
                            <input type="text" class="form-control" id="name" name="name" required 
                                   placeholder="Enter full name">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope me-2"></i>Email Address
                            </label>
                            <input type="email" class="form-control" id="email" name="email" required 
                                   placeholder="Enter email address">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock me-2"></i>Password
                            </label>
                            <input type="password" class="form-control" id="password" name="password" required 
                                   placeholder="Enter password" minlength="6">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="confirm_password" class="form-label">
                                <i class="fas fa-lock me-2"></i>Confirm Password
                            </label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required 
                                   placeholder="Confirm password" minlength="6">
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Note:</strong> The librarian will be able to issue and return books, 
                        add new books to the library, and view student information.
                    </div>
                    
                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-primary btn-custom me-3">
                            <i class="fas fa-save me-2"></i>Add Librarian
                        </button>
                        <a href="{{ url_for('admin_librarians') }}" class="btn btn-secondary btn-custom">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Password confirmation validation
    $('form').on('submit', function(e) {
        var password = $('#password').val();
        var confirmPassword = $('#confirm_password').val();
        
        if (password !== confirmPassword) {
            e.preventDefault();
            alert('Passwords do not match!');
            return false;
        }
        
        if (password.length < 6) {
            e.preventDefault();
            alert('Password must be at least 6 characters long!');
            return false;
        }
    });
    
    // Real-time password matching
    $('#confirm_password').on('input', function() {
        var password = $('#password').val();
        var confirmPassword = $(this).val();
        
        if (confirmPassword && password !== confirmPassword) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });
});
</script>
{% endblock %}
