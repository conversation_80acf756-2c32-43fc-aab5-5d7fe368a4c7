{% extends "base.html" %}

{% block title %}Counter Report - Admin{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>

<!-- Fixed Circulation Menu -->
<div class="nav-item">
    <a class="nav-link fw-bold text-primary" data-bs-toggle="collapse" href="#circulationSubmenu" role="button" aria-expanded="false">
        <i class="fas fa-exchange-alt me-2"></i>Circulation
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse" id="circulationSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_circulation_counter') }}">
                <i class="fas fa-desktop me-2"></i>Counter
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bulk_operations') }}">
                <i class="fas fa-layer-group me-2"></i>Bulk Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_payment_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Payment Management
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_management') }}">
                <i class="fas fa-tools me-2"></i>Binding & Maintenance
            </a>
        </div>
    </div>
</div>

<!-- Fixed Reports Menu -->
<div class="nav-item">
    <a class="nav-link fw-bold text-success" data-bs-toggle="collapse" href="#reportsSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-chart-bar me-2"></i>Reports
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="reportsSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_library_connection_report') }}">
                <i class="fas fa-wifi me-2"></i>Library Connection
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_access_register_report') }}">
                <i class="fas fa-sign-in-alt me-2"></i>Access Register
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bibliography_report') }}">
                <i class="fas fa-book-open me-2"></i>Bibliography
            </a>
            <a class="nav-link py-1 active" href="{{ url_for('admin_counter_report') }}">
                <i class="fas fa-calculator me-2"></i>Counter Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_statistics_report') }}">
                <i class="fas fa-chart-line me-2"></i>Statistics
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_report') }}">
                <i class="fas fa-tools me-2"></i>Binding Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_database_report') }}">
                <i class="fas fa-database me-2"></i>Database Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_member_report') }}">
                <i class="fas fa-users me-2"></i>Member Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_resource_report') }}">
                <i class="fas fa-archive me-2"></i>Resource Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_no_dues_report') }}">
                <i class="fas fa-check-circle me-2"></i>No Dues Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_qb_report') }}">
                <i class="fas fa-money-check-alt me-2"></i>QB Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_transfer_report') }}">
                <i class="fas fa-exchange-alt me-2"></i>Transfer Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_missing_report') }}">
                <i class="fas fa-exclamation-triangle me-2"></i>Missing Report
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-calculator me-3 text-primary"></i>Counter Report</h2>
        <p class="text-muted mb-0">Daily circulation statistics and staff performance metrics</p>
    </div>
    <div>
        <button class="btn btn-success" onclick="exportReport('excel')">
            <i class="fas fa-file-excel me-2"></i>Export Excel
        </button>
        <button class="btn btn-danger ms-2" onclick="exportReport('pdf')">
            <i class="fas fa-file-pdf me-2"></i>Export PDF
        </button>
    </div>
</div>

<!-- Filter Section -->
<div class="card autolib-card mb-4">
    <div class="card-header bg-gradient-primary text-white">
        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Counter Report Filters</h5>
    </div>
    <div class="card-body">
        <form id="filterForm">
            <div class="row">
                <div class="col-md-3">
                    <label for="dateFrom" class="form-label">From Date</label>
                    <input type="date" class="form-control" id="dateFrom" name="dateFrom">
                </div>
                <div class="col-md-3">
                    <label for="dateTo" class="form-label">To Date</label>
                    <input type="date" class="form-control" id="dateTo" name="dateTo">
                </div>
                <div class="col-md-3">
                    <label for="staffFilter" class="form-label">Staff Member</label>
                    <select class="form-select" id="staffFilter" name="staffFilter">
                        <option value="">All Staff</option>
                        <option value="admin">Admin</option>
                        <option value="librarian">Librarians</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="operationType" class="form-label">Operation Type</label>
                    <select class="form-select" id="operationType" name="operationType">
                        <option value="">All Operations</option>
                        <option value="issue">Book Issues</option>
                        <option value="return">Book Returns</option>
                        <option value="renewal">Renewals</option>
                        <option value="fine">Fine Collection</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button type="button" class="btn btn-primary" onclick="generateReport()">
                        <i class="fas fa-search me-2"></i>Generate Report
                    </button>
                    <button type="button" class="btn btn-outline-secondary ms-2" onclick="resetFilters()">
                        <i class="fas fa-refresh me-2"></i>Reset
                    </button>
                    <button type="button" class="btn btn-info ms-2" onclick="showLiveCounter()">
                        <i class="fas fa-eye me-2"></i>Live Counter
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Counter Statistics -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-success">
                    <i class="fas fa-arrow-up fa-2x mb-2"></i>
                </div>
                <h4 class="text-success" id="totalIssues">0</h4>
                <p class="text-muted mb-0 small">Total Issues</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-primary">
                    <i class="fas fa-arrow-down fa-2x mb-2"></i>
                </div>
                <h4 class="text-primary" id="totalReturns">0</h4>
                <p class="text-muted mb-0 small">Total Returns</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-warning">
                    <i class="fas fa-sync fa-2x mb-2"></i>
                </div>
                <h4 class="text-warning" id="totalRenewals">0</h4>
                <p class="text-muted mb-0 small">Renewals</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-danger">
                    <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                </div>
                <h4 class="text-danger" id="totalFines">₹0</h4>
                <p class="text-muted mb-0 small">Fines Collected</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-info">
                    <i class="fas fa-users fa-2x mb-2"></i>
                </div>
                <h4 class="text-info" id="uniqueUsers">0</h4>
                <p class="text-muted mb-0 small">Unique Users</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-dark">
                    <i class="fas fa-calculator fa-2x mb-2"></i>
                </div>
                <h4 class="text-dark" id="totalTransactions">0</h4>
                <p class="text-muted mb-0 small">Total Transactions</p>
            </div>
        </div>
    </div>
</div>

<!-- Counter Activity Tabs -->
<ul class="nav nav-tabs mb-4" id="counterTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="daily-activity-tab" data-bs-toggle="tab" data-bs-target="#daily-activity" type="button" role="tab">
            <i class="fas fa-calendar-day me-2"></i>Daily Activity
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="staff-performance-tab" data-bs-toggle="tab" data-bs-target="#staff-performance" type="button" role="tab">
            <i class="fas fa-user-tie me-2"></i>Staff Performance
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="hourly-breakdown-tab" data-bs-toggle="tab" data-bs-target="#hourly-breakdown" type="button" role="tab">
            <i class="fas fa-clock me-2"></i>Hourly Breakdown
        </button>
    </li>
</ul>

<div class="tab-content" id="counterTabContent">
    <!-- Daily Activity Tab -->
    <div class="tab-pane fade show active" id="daily-activity" role="tabpanel">
        <div class="card autolib-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-calendar-day me-2"></i>Daily Counter Activity</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="dailyActivityTable">
                        <thead class="table-dark">
                            <tr>
                                <th>Date</th>
                                <th>Issues</th>
                                <th>Returns</th>
                                <th>Renewals</th>
                                <th>Fines (₹)</th>
                                <th>Unique Users</th>
                                <th>Total Transactions</th>
                                <th>Peak Hour</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="8" class="text-center text-muted">
                                    <i class="fas fa-info-circle me-2"></i>No counter activity data available. Start using the system to see reports.
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Staff Performance Tab -->
    <div class="tab-pane fade" id="staff-performance" role="tabpanel">
        <div class="card autolib-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user-tie me-2"></i>Staff Performance Metrics</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="staffPerformanceTable">
                        <thead class="table-dark">
                            <tr>
                                <th>Staff Member</th>
                                <th>Role</th>
                                <th>Issues Processed</th>
                                <th>Returns Processed</th>
                                <th>Fines Collected (₹)</th>
                                <th>Avg. Processing Time</th>
                                <th>Performance Score</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="7" class="text-center text-muted">
                                    <i class="fas fa-info-circle me-2"></i>No staff performance data available.
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Hourly Breakdown Tab -->
    <div class="tab-pane fade" id="hourly-breakdown" role="tabpanel">
        <div class="card autolib-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Hourly Activity Breakdown</h5>
            </div>
            <div class="card-body">
                <canvas id="hourlyChart" height="300"></canvas>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // Set default dates (today)
    const today = new Date();
    $('#dateTo').val(today.toISOString().split('T')[0]);
    $('#dateFrom').val(today.toISOString().split('T')[0]);
    
    // Load initial data
    generateReport();
    
    // Initialize hourly chart
    initializeHourlyChart();
});

function generateReport() {
    const filters = {
        dateFrom: $('#dateFrom').val(),
        dateTo: $('#dateTo').val(),
        staffFilter: $('#staffFilter').val(),
        operationType: $('#operationType').val()
    };
    
    // Show loading
    $('#dailyActivityTable tbody').html('<tr><td colspan="8" class="text-center"><i class="fas fa-spinner fa-spin me-2"></i>Loading counter data...</td></tr>');
    
    // Simulate API call - replace with actual implementation
    setTimeout(() => {
        loadCounterData(filters);
    }, 1000);
}

function loadCounterData(filters) {
    // Since database is clean, show zero data
    const dailyData = []; // Empty array for clean database
    
    // Update statistics - all zeros for clean database
    $('#totalIssues').text('0');
    $('#totalReturns').text('0');
    $('#totalRenewals').text('0');
    $('#totalFines').text('₹0');
    $('#uniqueUsers').text('0');
    $('#totalTransactions').text('0');
    
    // Update daily activity table
    if (dailyData.length === 0) {
        $('#dailyActivityTable tbody').html(`
            <tr>
                <td colspan="8" class="text-center text-info">
                    <i class="fas fa-info-circle me-2"></i>No counter activity yet. Start issuing/returning books to see data.
                </td>
            </tr>
        `);
        
        $('#staffPerformanceTable tbody').html(`
            <tr>
                <td colspan="7" class="text-center text-info">
                    <i class="fas fa-info-circle me-2"></i>No staff performance data available.
                </td>
            </tr>
        `);
    }
    
    // Update hourly chart with zero data
    updateHourlyChart([]);
}

function initializeHourlyChart() {
    const ctx = document.getElementById('hourlyChart').getContext('2d');
    window.hourlyChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['9 AM', '10 AM', '11 AM', '12 PM', '1 PM', '2 PM', '3 PM', '4 PM', '5 PM'],
            datasets: [{
                label: 'Transactions',
                data: [0, 0, 0, 0, 0, 0, 0, 0, 0],
                backgroundColor: 'rgba(54, 162, 235, 0.8)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Number of Transactions'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Hour of Day'
                    }
                }
            },
            plugins: {
                title: {
                    display: true,
                    text: 'Hourly Transaction Activity'
                }
            }
        }
    });
}

function updateHourlyChart(data) {
    if (window.hourlyChart) {
        // Update with real data when available
        window.hourlyChart.data.datasets[0].data = data.length > 0 ? data : [0, 0, 0, 0, 0, 0, 0, 0, 0];
        window.hourlyChart.update();
    }
}

function resetFilters() {
    $('#filterForm')[0].reset();
    const today = new Date();
    $('#dateTo').val(today.toISOString().split('T')[0]);
    $('#dateFrom').val(today.toISOString().split('T')[0]);
    generateReport();
}

function showLiveCounter() {
    alert('Live counter monitoring functionality would be implemented here.');
}

function exportReport(format) {
    alert(`Exporting Counter Report as ${format.toUpperCase()}...`);
}
</script>
{% endblock %}
