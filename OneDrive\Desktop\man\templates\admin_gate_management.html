{% extends "base.html" %}

{% block title %}Gate Management - Admin{% endblock %}

{% block extra_css %}
<style>
    .card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border-radius: 0.5rem;
    }
    
    .card-header {
        background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
        color: white;
        border-radius: 0.5rem 0.5rem 0 0 !important;
        border: none;
    }
    
    .btn {
        border-radius: 0.375rem;
        font-weight: 500;
        transition: all 0.2s ease-in-out;
    }
    
    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
    
    .table {
        border-radius: 0.5rem;
        overflow: hidden;
    }
    
    .table thead th {
        background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
        color: white;
        border: none;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.85rem;
        letter-spacing: 0.5px;
    }
    
    .table tbody tr {
        transition: all 0.2s ease;
    }
    
    .table tbody tr:hover {
        background-color: #f8f9fa;
        transform: scale(1.01);
    }
    
    .badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
        border-radius: 0.375rem;
    }
    
    .form-control, .form-select {
        border-radius: 0.375rem;
        border: 1px solid #e0e6ed;
        transition: all 0.2s ease;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #6f42c1;
        box-shadow: 0 0 0 0.2rem rgba(111, 66, 193, 0.25);
    }
    
    .stats-card {
        background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
        color: white;
        border-radius: 0.5rem;
        padding: 1.5rem;
        text-align: center;
        margin-bottom: 1rem;
    }
    
    .stats-card h4 {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    
    .stats-card p {
        font-size: 0.9rem;
        opacity: 0.9;
        margin: 0;
    }
    
    .gate-user-card {
        border: 1px solid #e0e6ed;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
        transition: all 0.2s ease;
    }
    
    .gate-user-card:hover {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }
    
    .role-badge {
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
</style>
{% endblock %}

{% block sidebar %}
<!-- Dashboard -->
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>

<!-- Management Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-info" data-bs-toggle="collapse" href="#managementSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-cogs me-2"></i>Management
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="managementSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_books') }}">
                <i class="fas fa-book me-2"></i>Manage Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_ebooks') }}">
                <i class="fas fa-tablet-alt me-2"></i>Manage E-Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_students') }}">
                <i class="fas fa-graduation-cap me-2"></i>Manage Students
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_librarians') }}">
                <i class="fas fa-user-tie me-2"></i>Manage Librarians
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_colleges') }}">
                <i class="fas fa-university me-2"></i>Manage Colleges
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_departments') }}">
                <i class="fas fa-building me-2"></i>Manage Departments
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_news_clippings') }}">
                <i class="fas fa-newspaper me-2"></i>News Clippings
            </a>
            <a class="nav-link py-1 active" href="{{ url_for('admin_gate_management') }}">
                <i class="fas fa-door-open me-2"></i>Gate Management
            </a>
        </div>
    </div>
</div>

<!-- Circulation Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-primary" data-bs-toggle="collapse" href="#circulationSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-exchange-alt me-2"></i>Circulation
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="circulationSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_circulation_counter') }}">
                <i class="fas fa-desktop me-2"></i>Counter Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_issue_history') }}">
                <i class="fas fa-history me-2"></i>Issue History
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bulk_operations') }}">
                <i class="fas fa-layer-group me-2"></i>Bulk Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_payment_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Payment Management
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_management') }}">
                <i class="fas fa-tools me-2"></i>Binding & Maintenance
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_fine_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Fine Management
            </a>
        </div>
    </div>
</div>

<!-- Reports Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-success" data-bs-toggle="collapse" href="#reportsSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-chart-bar me-2"></i>Reports
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="reportsSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_library_connection_report') }}">
                <i class="fas fa-wifi me-2"></i>Library Connection
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_access_register_report') }}">
                <i class="fas fa-sign-in-alt me-2"></i>Access Register
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bibliography_report') }}">
                <i class="fas fa-book-open me-2"></i>Bibliography
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_counter_report') }}">
                <i class="fas fa-calculator me-2"></i>Counter Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_statistics_report') }}">
                <i class="fas fa-chart-line me-2"></i>Statistics
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_report') }}">
                <i class="fas fa-tools me-2"></i>Binding Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_database_report') }}">
                <i class="fas fa-database me-2"></i>Database Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_member_report') }}">
                <i class="fas fa-users me-2"></i>Member Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_resource_report') }}">
                <i class="fas fa-archive me-2"></i>Resource Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_no_dues_report') }}">
                <i class="fas fa-check-circle me-2"></i>No Dues Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_qb_report') }}">
                <i class="fas fa-money-check-alt me-2"></i>QB Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_transfer_report') }}">
                <i class="fas fa-exchange-alt me-2"></i>Transfer Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_missing_report') }}">
                <i class="fas fa-exclamation-triangle me-2"></i>Missing Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_news_clipping_report') }}">
                <i class="fas fa-newspaper me-2"></i>News Clipping Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_gate_report') }}">
                <i class="fas fa-door-open me-2"></i>Gate Report
            </a>
        </div>
    </div>
</div>

<!-- System Administration -->
<div class="nav-item">
    <a class="nav-link fw-bold text-warning" data-bs-toggle="collapse" href="#systemSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-server me-2"></i>System
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="systemSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_settings') }}">
                <i class="fas fa-cog me-2"></i>System Settings
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-door-open me-3"></i>Gate Management</h2>
    <div>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addGateUserModal">
            <i class="fas fa-plus me-2"></i>Add Gate User
        </button>
        <a href="{{ url_for('gate_login') }}" class="btn btn-success" target="_blank">
            <i class="fas fa-external-link-alt me-2"></i>Gate Dashboard
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="stats-card">
            <h4>{{ gate_users|length }}</h4>
            <p>Active Gate Users</p>
        </div>
    </div>
    <div class="col-md-4">
        <div class="stats-card">
            <h4 id="todayEntries">-</h4>
            <p>Today's Entries</p>
        </div>
    </div>
    <div class="col-md-4">
        <div class="stats-card">
            <h4 id="currentlyInside">-</h4>
            <p>Currently Inside</p>
        </div>
    </div>
</div>

<!-- Gate Users List -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-users me-2"></i>Gate Users</h5>
    </div>
    <div class="card-body">
        {% if gate_users %}
        <div class="row">
            {% for user in gate_users %}
            <div class="col-md-6 col-lg-4">
                <div class="gate-user-card">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h6 class="mb-0">{{ user.username }}</h6>
                        <span class="badge bg-primary role-badge">Gate Operator</span>
                    </div>
                    <p class="text-muted mb-2">
                        <i class="fas fa-calendar me-1"></i>Created: {{ user.created_at.strftime('%Y-%m-%d') }}
                    </p>
                    {% if user.last_login %}
                    <p class="text-muted mb-2">
                        <i class="fas fa-clock me-1"></i>Last Login: {{ user.last_login.strftime('%Y-%m-%d %H:%M') }}
                    </p>
                    {% endif %}
                    <div class="d-flex gap-2">
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteGateUser({{ user.gate_user_id }}, '{{ user.username }}')">
                            <i class="fas fa-trash"></i> Deactivate
                        </button>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-4">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No Gate Users Found</h5>
            <p class="text-muted">Create gate users to manage entry/exit operations</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Add Gate User Modal -->
<div class="modal fade" id="addGateUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Gate User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addGateUserForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="username" class="form-label">Username <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <div class="mb-3">
                        <label for="full_name" class="form-label">Full Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="full_name" name="full_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="role" class="form-label">Role</label>
                        <select class="form-select" id="role" name="role">
                            <option value="gate_operator">Gate Operator</option>
                            <option value="security_head">Security Head</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create User</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    loadStatistics();

    // Add gate user form submission
    $('#addGateUserForm').on('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);

        $.ajax({
            url: '/admin/gate-management/add-user',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    $('#addGateUserModal').modal('hide');
                    location.reload();
                } else {
                    alert('Error: ' + response.error);
                }
            },
            error: function(xhr) {
                const response = JSON.parse(xhr.responseText);
                alert('Error: ' + response.error);
            }
        });
    });
});

function loadStatistics() {
    // Load today's statistics
    const today = new Date().toISOString().split('T')[0];

    $.ajax({
        url: `/api/admin/gate-entries?start_date=${today}&end_date=${today}`,
        method: 'GET',
        success: function(response) {
            $('#todayEntries').text(response.total_count);

            // Count currently inside (entries without exit time)
            const currentlyInside = response.entries.filter(entry => entry.exit_time === 'Still Inside').length;
            $('#currentlyInside').text(currentlyInside);
        },
        error: function() {
            $('#todayEntries').text('Error');
            $('#currentlyInside').text('Error');
        }
    });
}

function deleteGateUser(userId, username) {
    if (confirm(`Are you sure you want to deactivate gate user "${username}"?`)) {
        $.ajax({
            url: `/admin/gate-management/delete-user/${userId}`,
            method: 'POST',
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('Error: ' + response.error);
                }
            },
            error: function(xhr) {
                const response = JSON.parse(xhr.responseText);
                alert('Error: ' + response.error);
            }
        });
    }
}
</script>
{% endblock %}
