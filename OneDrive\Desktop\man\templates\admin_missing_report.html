{% extends "base.html" %}

{% block title %}Missing Report - Admin{% endblock %}

{% block sidebar %}
<!-- Dashboard -->
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>

<!-- Management Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-info" data-bs-toggle="collapse" href="#managementSubmenu" role="button" aria-expanded="false">
        <i class="fas fa-cogs me-2"></i>Management
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse" id="managementSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_books') }}">
                <i class="fas fa-book me-2"></i>Manage Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_ebooks') }}">
                <i class="fas fa-tablet-alt me-2"></i>Manage E-Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_students') }}">
                <i class="fas fa-graduation-cap me-2"></i>Manage Students
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_librarians') }}">
                <i class="fas fa-user-tie me-2"></i>Manage Librarians
            </a>

        </div>
    </div>
</div>

<!-- Circulation Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-primary" data-bs-toggle="collapse" href="#circulationSubmenu" role="button" aria-expanded="false">
        <i class="fas fa-exchange-alt me-2"></i>Circulation
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse" id="circulationSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_circulation_counter') }}">
                <i class="fas fa-desktop me-2"></i>Counter Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_issue_history') }}">
                <i class="fas fa-history me-2"></i>Issue History
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bulk_operations') }}">
                <i class="fas fa-layer-group me-2"></i>Bulk Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_payment_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Payment Management
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_management') }}">
                <i class="fas fa-tools me-2"></i>Binding & Maintenance
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_fine_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Fine Management
            </a>

        </div>
    </div>
</div>

<!-- Reports Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-success" data-bs-toggle="collapse" href="#reportsSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-chart-bar me-2"></i>Reports
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="reportsSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_library_connection_report') }}">
                <i class="fas fa-wifi me-2"></i>Library Connection
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_access_register_report') }}">
                <i class="fas fa-sign-in-alt me-2"></i>Access Register
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bibliography_report') }}">
                <i class="fas fa-book-open me-2"></i>Bibliography
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_counter_report') }}">
                <i class="fas fa-calculator me-2"></i>Counter Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_statistics_report') }}">
                <i class="fas fa-chart-line me-2"></i>Statistics
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_report') }}">
                <i class="fas fa-tools me-2"></i>Binding Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_database_report') }}">
                <i class="fas fa-database me-2"></i>Database Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_member_report') }}">
                <i class="fas fa-users me-2"></i>Member Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_resource_report') }}">
                <i class="fas fa-archive me-2"></i>Resource Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_no_dues_report') }}">
                <i class="fas fa-check-circle me-2"></i>No Dues Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_qb_report') }}">
                <i class="fas fa-money-check-alt me-2"></i>QB Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_transfer_report') }}">
                <i class="fas fa-exchange-alt me-2"></i>Transfer Report
            </a>
            <a class="nav-link py-1 active" href="{{ url_for('admin_missing_report') }}">
                <i class="fas fa-exclamation-triangle me-2"></i>Missing Report
            </a>
        </div>
    </div>
</div>

<!-- System Administration -->
<div class="nav-item">
    <a class="nav-link fw-bold text-warning" data-bs-toggle="collapse" href="#systemSubmenu" role="button" aria-expanded="false">
        <i class="fas fa-server me-2"></i>System
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse" id="systemSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_settings') }}">
                <i class="fas fa-cog me-2"></i>System Settings
            </a>

        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-exclamation-triangle me-3 text-danger"></i>Missing & Lost Books Report</h2>
        <p class="text-muted mb-0">Track missing, lost, damaged books and recovery status</p>
    </div>
    <div>
        <button class="btn btn-success" onclick="exportReport('excel')">
            <i class="fas fa-file-excel me-2"></i>Export Excel
        </button>
        <button class="btn btn-danger ms-2" onclick="exportReport('pdf')">
            <i class="fas fa-file-pdf me-2"></i>Export PDF
        </button>
    </div>
</div>

<!-- Filter Section -->
<div class="card autolib-card mb-4">
    <div class="card-header bg-gradient-warning text-white">
        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Missing Book Filters</h5>
    </div>
    <div class="card-body">
        <form id="filterForm">
            <div class="row">
                <div class="col-md-3">
                    <label for="dateFrom" class="form-label">From Date</label>
                    <input type="date" class="form-control" id="dateFrom" name="dateFrom">
                </div>
                <div class="col-md-3">
                    <label for="dateTo" class="form-label">To Date</label>
                    <input type="date" class="form-control" id="dateTo" name="dateTo">
                </div>
                <div class="col-md-3">
                    <label for="statusFilter" class="form-label">Status</label>
                    <select class="form-select" id="statusFilter" name="statusFilter">
                        <option value="">All Status</option>
                        <option value="missing">Missing</option>
                        <option value="lost">Lost</option>
                        <option value="damaged">Damaged</option>
                        <option value="recovered">Recovered</option>
                        <option value="replaced">Replaced</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="department" class="form-label">Department</label>
                    <select class="form-select" id="department" name="department">
                        <option value="">All Departments</option>
                        <option value="CSE">Computer Science</option>
                        <option value="IT">Information Technology</option>
                        <option value="ECE">Electronics & Communication</option>
                        <option value="EEE">Electrical & Electronics</option>
                        <option value="MECH">Mechanical</option>
                        <option value="CIVIL">Civil</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-6">
                    <label for="searchBook" class="form-label">Search Book</label>
                    <input type="text" class="form-control" id="searchBook" placeholder="Enter title, author, or access number">
                </div>
                <div class="col-md-3">
                    <label for="costRange" class="form-label">Cost Range</label>
                    <select class="form-select" id="costRange" name="costRange">
                        <option value="">All Costs</option>
                        <option value="0-500">₹0 - ₹500</option>
                        <option value="500-1000">₹500 - ₹1000</option>
                        <option value="1000-2000">₹1000 - ₹2000</option>
                        <option value="2000+">₹2000+</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="priority" class="form-label">Priority</label>
                    <select class="form-select" id="priority" name="priority">
                        <option value="">All Priority</option>
                        <option value="high">High</option>
                        <option value="medium">Medium</option>
                        <option value="low">Low</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button type="button" class="btn btn-warning" onclick="generateReport()">
                        <i class="fas fa-search me-2"></i>Generate Report
                    </button>
                    <button type="button" class="btn btn-outline-secondary ms-2" onclick="resetFilters()">
                        <i class="fas fa-refresh me-2"></i>Reset
                    </button>
                    <button type="button" class="btn btn-danger ms-2" onclick="markAsLost()">
                        <i class="fas fa-times me-2"></i>Mark as Lost
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Summary Statistics -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-danger">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                </div>
                <h4 class="text-danger" id="totalMissing">0</h4>
                <p class="text-muted mb-0 small">Total Missing</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-warning">
                    <i class="fas fa-question-circle fa-2x mb-2"></i>
                </div>
                <h4 class="text-warning" id="totalLost">0</h4>
                <p class="text-muted mb-0 small">Lost Books</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-info">
                    <i class="fas fa-tools fa-2x mb-2"></i>
                </div>
                <h4 class="text-info" id="totalDamaged">0</h4>
                <p class="text-muted mb-0 small">Damaged</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-success">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                </div>
                <h4 class="text-success" id="totalRecovered">0</h4>
                <p class="text-muted mb-0 small">Recovered</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-primary">
                    <i class="fas fa-sync fa-2x mb-2"></i>
                </div>
                <h4 class="text-primary" id="totalReplaced">0</h4>
                <p class="text-muted mb-0 small">Replaced</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-dark">
                    <i class="fas fa-rupee-sign fa-2x mb-2"></i>
                </div>
                <h4 class="text-dark" id="totalCost">₹0</h4>
                <p class="text-muted mb-0 small">Total Cost</p>
            </div>
        </div>
    </div>
</div>

<!-- Missing Books Table -->
<div class="card autolib-card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="fas fa-table me-2"></i>Missing & Lost Books</h5>
        <div>
            <span class="badge bg-danger" id="criticalCount">0 Critical</span>
            <span class="badge bg-warning ms-2" id="pendingCount">0 Pending</span>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="missingTable">
                <thead class="table-dark">
                    <tr>
                        <th>
                            <input type="checkbox" class="form-check-input" id="selectAll">
                        </th>
                        <th>Access No.</th>
                        <th>Title</th>
                        <th>Author</th>
                        <th>Last Issued To</th>
                        <th>Missing Date</th>
                        <th>Status</th>
                        <th>Cost</th>
                        <th>Priority</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="10" class="text-center text-muted">
                            <i class="fas fa-info-circle me-2"></i>No missing books found. This is good news!
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- Bulk Actions -->
        <div class="mt-3" id="bulkActions" style="display: none;">
            <div class="d-flex gap-2">
                <button class="btn btn-success btn-sm" onclick="bulkAction('recovered')">
                    <i class="fas fa-check me-2"></i>Mark as Recovered
                </button>
                <button class="btn btn-primary btn-sm" onclick="bulkAction('replaced')">
                    <i class="fas fa-sync me-2"></i>Mark as Replaced
                </button>
                <button class="btn btn-danger btn-sm" onclick="bulkAction('lost')">
                    <i class="fas fa-times me-2"></i>Mark as Lost
                </button>
                <button class="btn btn-warning btn-sm" onclick="generateInsuranceClaim()">
                    <i class="fas fa-file-invoice me-2"></i>Insurance Claim
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Set default dates (last 30 days)
    const today = new Date();
    const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    $('#dateTo').val(today.toISOString().split('T')[0]);
    $('#dateFrom').val(monthAgo.toISOString().split('T')[0]);
    
    // Load initial data
    generateReport();
    
    // Handle select all checkbox
    $('#selectAll').on('change', function() {
        $('.book-checkbox').prop('checked', this.checked);
        toggleBulkActions();
    });
    
    // Handle individual checkboxes
    $(document).on('change', '.book-checkbox', function() {
        toggleBulkActions();
    });
});

function generateReport() {
    const filters = {
        dateFrom: $('#dateFrom').val(),
        dateTo: $('#dateTo').val(),
        statusFilter: $('#statusFilter').val(),
        department: $('#department').val(),
        searchBook: $('#searchBook').val(),
        costRange: $('#costRange').val(),
        priority: $('#priority').val()
    };
    
    // Show loading
    $('#missingTable tbody').html('<tr><td colspan="10" class="text-center"><i class="fas fa-spinner fa-spin me-2"></i>Loading missing books data...</td></tr>');
    
    // Simulate API call - replace with actual implementation
    setTimeout(() => {
        loadMissingData(filters);
    }, 1000);
}

function loadMissingData(filters) {
    // Since database is clean, show zero data
    const sampleData = []; // Empty array for clean database
    
    // Update statistics - all zeros for clean database
    $('#totalMissing').text('0');
    $('#totalLost').text('0');
    $('#totalDamaged').text('0');
    $('#totalRecovered').text('0');
    $('#totalReplaced').text('0');
    $('#totalCost').text('₹0');
    $('#criticalCount').text('0 Critical');
    $('#pendingCount').text('0 Pending');
    
    // Update table
    if (sampleData.length === 0) {
        $('#missingTable tbody').html(`
            <tr>
                <td colspan="10" class="text-center text-success">
                    <i class="fas fa-check-circle me-2"></i>No missing books found. All books are accounted for!
                </td>
            </tr>
        `);
    } else {
        let tableHTML = '';
        sampleData.forEach((book, index) => {
            const statusBadge = getStatusBadge(book.status);
            const priorityBadge = getPriorityBadge(book.priority);
            
            tableHTML += `
                <tr>
                    <td>
                        <input type="checkbox" class="form-check-input book-checkbox" value="${book.accessNo}">
                    </td>
                    <td>${book.accessNo}</td>
                    <td>${book.title}</td>
                    <td>${book.author}</td>
                    <td>${book.lastIssuedTo}</td>
                    <td>${book.missingDate}</td>
                    <td>${statusBadge}</td>
                    <td>₹${book.cost}</td>
                    <td>${priorityBadge}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-success" onclick="markRecovered('${book.accessNo}')" title="Mark as Recovered">
                                <i class="fas fa-check"></i>
                            </button>
                            <button class="btn btn-outline-primary" onclick="markReplaced('${book.accessNo}')" title="Mark as Replaced">
                                <i class="fas fa-sync"></i>
                            </button>
                            <button class="btn btn-outline-info" onclick="viewDetails('${book.accessNo}')" title="View Details">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });
        
        $('#missingTable tbody').html(tableHTML);
    }
}

function getStatusBadge(status) {
    const badges = {
        'missing': '<span class="badge bg-danger">Missing</span>',
        'lost': '<span class="badge bg-warning">Lost</span>',
        'damaged': '<span class="badge bg-info">Damaged</span>',
        'recovered': '<span class="badge bg-success">Recovered</span>',
        'replaced': '<span class="badge bg-primary">Replaced</span>'
    };
    return badges[status] || '<span class="badge bg-secondary">Unknown</span>';
}

function getPriorityBadge(priority) {
    const badges = {
        'high': '<span class="badge bg-danger">High</span>',
        'medium': '<span class="badge bg-warning">Medium</span>',
        'low': '<span class="badge bg-success">Low</span>'
    };
    return badges[priority] || '<span class="badge bg-secondary">-</span>';
}

function toggleBulkActions() {
    const checkedBoxes = $('.book-checkbox:checked').length;
    if (checkedBoxes > 0) {
        $('#bulkActions').show();
    } else {
        $('#bulkActions').hide();
    }
}

function resetFilters() {
    $('#filterForm')[0].reset();
    const today = new Date();
    const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    $('#dateTo').val(today.toISOString().split('T')[0]);
    $('#dateFrom').val(monthAgo.toISOString().split('T')[0]);
    
    generateReport();
}

function markAsLost() {
    const selectedBooks = $('.book-checkbox:checked').map(function() {
        return this.value;
    }).get();
    
    if (selectedBooks.length === 0) {
        alert('Please select books to mark as lost.');
        return;
    }
    
    if (confirm(`Mark ${selectedBooks.length} book(s) as lost?`)) {
        alert('Books marked as lost successfully.');
        generateReport();
    }
}

function bulkAction(action) {
    const selectedBooks = $('.book-checkbox:checked').map(function() {
        return this.value;
    }).get();
    
    if (selectedBooks.length === 0) {
        alert('Please select books for bulk action.');
        return;
    }
    
    const actionText = action.charAt(0).toUpperCase() + action.slice(1);
    if (confirm(`Mark ${selectedBooks.length} book(s) as ${actionText}?`)) {
        alert(`Books marked as ${actionText} successfully.`);
        generateReport();
    }
}

function markRecovered(accessNo) {
    if (confirm('Mark this book as recovered?')) {
        alert('Book marked as recovered successfully.');
        generateReport();
    }
}

function markReplaced(accessNo) {
    if (confirm('Mark this book as replaced?')) {
        alert('Book marked as replaced successfully.');
        generateReport();
    }
}

function viewDetails(accessNo) {
    alert(`Viewing details for book: ${accessNo}`);
}

function generateInsuranceClaim() {
    const selectedBooks = $('.book-checkbox:checked').map(function() {
        return this.value;
    }).get();
    
    if (selectedBooks.length === 0) {
        alert('Please select books for insurance claim.');
        return;
    }
    
    alert(`Generating insurance claim for ${selectedBooks.length} book(s)...`);
}

function exportReport(format) {
    alert(`Exporting Missing Books Report as ${format.toUpperCase()}...`);
}
</script>
{% endblock %}
