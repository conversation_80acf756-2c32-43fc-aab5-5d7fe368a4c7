{% extends "base.html" %}

{% block title %}Admin Dashboard - Library Management{% endblock %}

{% block sidebar %}
<a class="nav-link active" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>

<!-- Fixed Circulation Menu -->
<div class="nav-item">
    <a class="nav-link fw-bold text-primary" data-bs-toggle="collapse" href="#circulationSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-exchange-alt me-2"></i>Circulation
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="circulationSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_circulation_counter') }}">
                <i class="fas fa-desktop me-2"></i>Counter
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bulk_operations') }}">
                <i class="fas fa-layer-group me-2"></i>Bulk Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_payment_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Payment Management
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_management') }}">
                <i class="fas fa-tools me-2"></i>Binding & Maintenance
            </a>
        </div>
    </div>
</div>

<!-- Fixed Reports Menu -->
<div class="nav-item">
    <a class="nav-link fw-bold text-success" data-bs-toggle="collapse" href="#reportsSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-chart-bar me-2"></i>Reports
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="reportsSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_library_connection_report') }}">
                <i class="fas fa-wifi me-2"></i>Library Connection
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_access_register_report') }}">
                <i class="fas fa-sign-in-alt me-2"></i>Access Register
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bibliography_report') }}">
                <i class="fas fa-book-open me-2"></i>Bibliography
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_counter_report') }}">
                <i class="fas fa-calculator me-2"></i>Counter Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_statistics_report') }}">
                <i class="fas fa-chart-line me-2"></i>Statistics
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_report') }}">
                <i class="fas fa-tools me-2"></i>Binding Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_database_report') }}">
                <i class="fas fa-database me-2"></i>Database Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_member_report') }}">
                <i class="fas fa-users me-2"></i>Member Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_resource_report') }}">
                <i class="fas fa-archive me-2"></i>Resource Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_no_dues_report') }}">
                <i class="fas fa-check-circle me-2"></i>No Dues Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_qb_report') }}">
                <i class="fas fa-money-check-alt me-2"></i>QB Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_transfer_report') }}">
                <i class="fas fa-exchange-alt me-2"></i>Transfer Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_missing_report') }}">
                <i class="fas fa-exclamation-triangle me-2"></i>Missing Report
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-tachometer-alt me-3"></i>Admin Dashboard</h2>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-6 col-lg-3 mb-3">
        <div class="card stat-card stat-card-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0 text-primary">{{ stats.total_books or 0 }}</h3>
                        <p class="text-muted mb-0">Total Books</p>
                        <small class="text-success">
                            <i class="fas fa-sync me-1"></i>Real-time data
                        </small>
                    </div>
                    <div class="text-primary">
                        <i class="fas fa-book fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 col-lg-3 mb-3">
        <div class="card stat-card stat-card-info">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0 text-info">{{ stats.total_ebooks or 0 }}</h3>
                        <p class="text-muted mb-0">Total E-Books</p>
                        <small class="text-success">
                            <i class="fas fa-sync me-1"></i>Real-time data
                        </small>
                    </div>
                    <div class="text-info">
                        <i class="fas fa-tablet-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-lg-3 mb-3">
        <div class="card stat-card stat-card-success">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0 text-success">{{ stats.total_students or 0 }}</h3>
                        <p class="text-muted mb-0">Total Students</p>
                        <small class="text-success">
                            <i class="fas fa-sync me-1"></i>Real-time data
                        </small>
                    </div>
                    <div class="text-success">
                        <i class="fas fa-graduation-cap fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 col-lg-3 mb-3">
        <div class="card stat-card stat-card-info">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0 text-info">{{ stats.total_librarians or 0 }}</h3>
                        <p class="text-muted mb-0">Total Librarians</p>
                        <small class="text-success">
                            <i class="fas fa-sync me-1"></i>Real-time data
                        </small>
                    </div>
                    <div class="text-info">
                        <i class="fas fa-user-tie fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 col-lg-3 mb-3">
        <div class="card stat-card stat-card-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0 text-warning">{{ stats.issued_books or 0 }}</h3>
                        <p class="text-muted mb-0">Currently Issued</p>
                        <small class="text-success">
                            <i class="fas fa-sync me-1"></i>Live count
                        </small>
                    </div>
                    <div class="text-warning">
                        <i class="fas fa-exchange-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Overdue Books Alert -->
{% if stats.overdue_books > 0 %}
<div class="alert alert-danger" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <strong>{{ stats.overdue_books }}</strong> books are overdue and need immediate attention!
</div>
{% endif %}

<!-- Quick Actions -->
<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('admin_add_book') }}" class="btn btn-primary btn-custom">
                        <i class="fas fa-plus me-2"></i>Add New Book
                    </a>
                    <a href="{{ url_for('admin_add_ebook') }}" class="btn btn-info btn-custom">
                        <i class="fas fa-tablet-alt me-2"></i>Add New E-Book
                    </a>
                    <a href="{{ url_for('admin_add_librarian') }}" class="btn btn-success btn-custom">
                        <i class="fas fa-user-plus me-2"></i>Add New Librarian
                    </a>
                    <a href="{{ url_for('admin_add_student') }}" class="btn btn-info btn-custom">
                        <i class="fas fa-user-graduate me-2"></i>Add New Student
                    </a>
                    <a href="{{ url_for('admin_bulk_users') }}" class="btn btn-warning btn-custom">
                        <i class="fas fa-upload me-2"></i>Bulk Create Users
                    </a>
                    <a href="{{ url_for('admin_settings') }}" class="btn btn-dark btn-custom">
                        <i class="fas fa-cog me-2"></i>Library Settings
                    </a>
                    <a href="{{ url_for('admin_circulation') }}" class="btn btn-warning btn-custom">
                        <i class="fas fa-exchange-alt me-2"></i>Circulation Tracking
                    </a>
                    <a href="{{ url_for('admin_reports') }}" class="btn btn-secondary btn-custom">
                        <i class="fas fa-chart-bar me-2"></i>Generate Reports
                    </a>
                    <a href="{{ url_for('admin_issue_history') }}" class="btn btn-outline-secondary btn-custom">
                        <i class="fas fa-history me-2"></i>View Issue History
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>System Overview</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Books Available</span>
                        <span class="text-success">{{ stats.total_books - stats.issued_books }}</span>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar bg-success" style="width: {{ ((stats.total_books - stats.issued_books) / stats.total_books * 100) if stats.total_books > 0 else 0 }}%"></div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Books Issued</span>
                        <span class="text-warning">{{ stats.issued_books }}</span>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar bg-warning" style="width: {{ (stats.issued_books / stats.total_books * 100) if stats.total_books > 0 else 0 }}%"></div>
                    </div>
                </div>
                
                {% if stats.overdue_books > 0 %}
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Overdue Books</span>
                        <span class="text-danger">{{ stats.overdue_books }}</span>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar bg-danger" style="width: {{ (stats.overdue_books / stats.issued_books * 100) if stats.issued_books > 0 else 0 }}%"></div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<!-- <div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-clock me-2"></i>System Status</h5>
    </div>
    <div class="card-body">
        <div class="row text-center">
            <div class="col-md-3">
                <i class="fas fa-database fa-2x text-primary mb-2"></i>
                <h6>Database</h6>
                <span class="badge bg-success">Online</span>
            </div>
            <div class="col-md-3">
                <i class="fas fa-users fa-2x text-success mb-2"></i>
                <h6>User Management</h6>
                <span class="badge bg-success">Active</span>
            </div>
            <div class="col-md-3">
                <i class="fas fa-book-open fa-2x text-info mb-2"></i>
                <h6>Book System</h6>
                <span class="badge bg-success">Operational</span>
            </div>
            <div class="col-md-3">
                <i class="fas fa-shield-alt fa-2x text-warning mb-2"></i>
                <h6>Security</h6>
                <span class="badge bg-success">Secure</span>
            </div>
        </div>
    </div>
</div> -->
{% endblock %}
