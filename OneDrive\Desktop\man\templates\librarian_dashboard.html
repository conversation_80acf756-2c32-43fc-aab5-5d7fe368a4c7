{% extends "base.html" %}

{% block title %}Librarian Dashboard{% endblock %}

{% block sidebar %}
<a class="nav-link active" href="{{ url_for('librarian_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>
<a class="nav-link" href="{{ url_for('librarian_books') }}">
    <i class="fas fa-book me-2"></i>Manage Books
</a>
<a class="nav-link" href="{{ url_for('librarian_ebooks') }}">
    <i class="fas fa-tablet-alt me-2"></i>Manage E-Books
</a>
<a class="nav-link" href="{{ url_for('librarian_students') }}">
    <i class="fas fa-graduation-cap me-2"></i>View Students
</a>

<!-- Circulation Submenu -->
<div class="nav-item">
    <a class="nav-link" data-bs-toggle="collapse" href="#librarianCirculationSubmenu" role="button" aria-expanded="false">
        <i class="fas fa-exchange-alt me-2"></i>Circulation
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse" id="librarianCirculationSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('librarian_circulation_counter') }}">
                <i class="fas fa-desktop me-2"></i>Counter
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_bulk_operations') }}">
                <i class="fas fa-layer-group me-2"></i>Bulk Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_payment_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Payment Management
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_binding_management') }}">
                <i class="fas fa-tools me-2"></i>Binding & Maintenance
            </a>
        </div>
    </div>
</div>

<a class="nav-link" href="{{ url_for('librarian_bulk_books') }}">
    <i class="fas fa-file-upload me-2"></i>Bulk Add Books
</a>
<a class="nav-link" href="{{ url_for('librarian_issue_history') }}">
    <i class="fas fa-history me-2"></i>Issue History
</a>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-tachometer-alt me-3 text-primary"></i>Librarian Dashboard</h2>
        <p class="text-muted mb-0">Welcome back, {{ session.user_name }}! Manage your library operations efficiently.</p>
    </div>
    <div>
        <span class="badge bg-success fs-6 px-3 py-2">
            <i class="fas fa-circle me-2" style="font-size: 8px;"></i>Online
        </span>
    </div>
</div>

<!-- Quick Actions Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
            </div>
            <div class="card-body p-4">
                <div class="row g-3">
                    <div class="col-md-2 col-sm-4 col-6">
                        <a href="{{ url_for('librarian_circulation_counter') }}" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3 quick-action-btn">
                            <i class="fas fa-desktop fa-2x mb-2"></i>
                            <span class="fw-bold">Counter</span>
                            <small class="text-muted">Issue/Return</small>
                        </a>
                    </div>
                    <div class="col-md-2 col-sm-4 col-6">
                        <a href="{{ url_for('librarian_bulk_operations') }}" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3 quick-action-btn">
                            <i class="fas fa-layer-group fa-2x mb-2"></i>
                            <span class="fw-bold">Bulk Ops</span>
                            <small class="text-muted">Multiple Books</small>
                        </a>
                    </div>
                    <div class="col-md-2 col-sm-4 col-6">
                        <a href="{{ url_for('librarian_payment_management') }}" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3 quick-action-btn">
                            <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                            <span class="fw-bold">Payments</span>
                            <small class="text-muted">Collect Fines</small>
                        </a>
                    </div>
                    <div class="col-md-2 col-sm-4 col-6">
                        <a href="{{ url_for('librarian_books') }}" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3 quick-action-btn">
                            <i class="fas fa-book fa-2x mb-2"></i>
                            <span class="fw-bold">Books</span>
                            <small class="text-muted">Manage Catalog</small>
                        </a>
                    </div>
                    <div class="col-md-2 col-sm-4 col-6">
                        <a href="{{ url_for('librarian_students') }}" class="btn btn-outline-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3 quick-action-btn">
                            <i class="fas fa-users fa-2x mb-2"></i>
                            <span class="fw-bold">Students</span>
                            <small class="text-muted">View Members</small>
                        </a>
                    </div>
                    <div class="col-md-2 col-sm-4 col-6">
                        <a href="{{ url_for('librarian_binding_management') }}" class="btn btn-outline-dark w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3 quick-action-btn">
                            <i class="fas fa-tools fa-2x mb-2"></i>
                            <span class="fw-bold">Binding</span>
                            <small class="text-muted">Maintenance</small>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-6 col-lg-3 mb-3">
        <div class="card stat-card stat-card-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ stats.total_books }}</h3>
                        <p class="text-muted mb-0">Total Books</p>
                    </div>
                    <div class="text-primary">
                        <i class="fas fa-book fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 col-lg-3 mb-3">
        <div class="card stat-card stat-card-success">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ stats.available_books }}</h3>
                        <p class="text-muted mb-0">Available Books</p>
                    </div>
                    <div class="text-success">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 col-lg-3 mb-3">
        <div class="card stat-card stat-card-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ stats.issued_books }}</h3>
                        <p class="text-muted mb-0">Books Issued</p>
                    </div>
                    <div class="text-warning">
                        <i class="fas fa-exchange-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add more content here, like quick actions, alerts, etc. -->

{% endblock %}