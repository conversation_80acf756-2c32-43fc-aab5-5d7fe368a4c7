{% extends "base.html" %}

{% block title %}Bibliography Report - Admin{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>

<!-- Fixed Circulation Menu -->
<div class="nav-item">
    <a class="nav-link fw-bold text-primary" data-bs-toggle="collapse" href="#circulationSubmenu" role="button" aria-expanded="false">
        <i class="fas fa-exchange-alt me-2"></i>Circulation
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse" id="circulationSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_circulation_counter') }}">
                <i class="fas fa-desktop me-2"></i>Counter
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bulk_operations') }}">
                <i class="fas fa-layer-group me-2"></i>Bulk Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_payment_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Payment Management
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_management') }}">
                <i class="fas fa-tools me-2"></i>Binding & Maintenance
            </a>
        </div>
    </div>
</div>

<!-- Fixed Reports Menu -->
<div class="nav-item">
    <a class="nav-link fw-bold text-success" data-bs-toggle="collapse" href="#reportsSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-chart-bar me-2"></i>Reports
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="reportsSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_library_connection_report') }}">
                <i class="fas fa-wifi me-2"></i>Library Connection
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_access_register_report') }}">
                <i class="fas fa-sign-in-alt me-2"></i>Access Register
            </a>
            <a class="nav-link py-1 active" href="{{ url_for('admin_bibliography_report') }}">
                <i class="fas fa-book-open me-2"></i>Bibliography
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_counter_report') }}">
                <i class="fas fa-calculator me-2"></i>Counter Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_statistics_report') }}">
                <i class="fas fa-chart-line me-2"></i>Statistics
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_report') }}">
                <i class="fas fa-tools me-2"></i>Binding Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_database_report') }}">
                <i class="fas fa-database me-2"></i>Database Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_member_report') }}">
                <i class="fas fa-users me-2"></i>Member Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_resource_report') }}">
                <i class="fas fa-archive me-2"></i>Resource Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_no_dues_report') }}">
                <i class="fas fa-check-circle me-2"></i>No Dues Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_qb_report') }}">
                <i class="fas fa-money-check-alt me-2"></i>QB Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_transfer_report') }}">
                <i class="fas fa-exchange-alt me-2"></i>Transfer Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_missing_report') }}">
                <i class="fas fa-exclamation-triangle me-2"></i>Missing Report
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-book-open me-3 text-info"></i>Bibliography Report</h2>
        <p class="text-muted mb-0">Complete catalog listing with subject-wise and author-wise bibliography</p>
    </div>
    <div>
        <button class="btn btn-success" onclick="exportReport('excel')">
            <i class="fas fa-file-excel me-2"></i>Export Excel
        </button>
        <button class="btn btn-danger ms-2" onclick="exportReport('pdf')">
            <i class="fas fa-file-pdf me-2"></i>Export PDF
        </button>
    </div>
</div>

<!-- Filter Section -->
<div class="card autolib-card mb-4">
    <div class="card-header bg-gradient-info text-white">
        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Bibliography Filters</h5>
    </div>
    <div class="card-body">
        <form id="filterForm">
            <div class="row">
                <div class="col-md-3">
                    <label for="sortBy" class="form-label">Sort By</label>
                    <select class="form-select" id="sortBy" name="sortBy">
                        <option value="title">Title (A-Z)</option>
                        <option value="author">Author (A-Z)</option>
                        <option value="subject">Subject</option>
                        <option value="publisher">Publisher</option>
                        <option value="year">Publication Year</option>
                        <option value="access_no">Access Number</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="subjectFilter" class="form-label">Subject</label>
                    <select class="form-select" id="subjectFilter" name="subjectFilter">
                        <option value="">All Subjects</option>
                        <option value="Computer Science">Computer Science</option>
                        <option value="Mathematics">Mathematics</option>
                        <option value="Physics">Physics</option>
                        <option value="Chemistry">Chemistry</option>
                        <option value="Biology">Biology</option>
                        <option value="Engineering">Engineering</option>
                        <option value="Literature">Literature</option>
                        <option value="History">History</option>
                        <option value="Economics">Economics</option>
                        <option value="Management">Management</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="languageFilter" class="form-label">Language</label>
                    <select class="form-select" id="languageFilter" name="languageFilter">
                        <option value="">All Languages</option>
                        <option value="English">English</option>
                        <option value="Hindi">Hindi</option>
                        <option value="Tamil">Tamil</option>
                        <option value="Telugu">Telugu</option>
                        <option value="Malayalam">Malayalam</option>
                        <option value="Kannada">Kannada</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="yearRange" class="form-label">Publication Year</label>
                    <select class="form-select" id="yearRange" name="yearRange">
                        <option value="">All Years</option>
                        <option value="2020-2024">2020-2024</option>
                        <option value="2015-2019">2015-2019</option>
                        <option value="2010-2014">2010-2014</option>
                        <option value="2000-2009">2000-2009</option>
                        <option value="before-2000">Before 2000</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-4">
                    <label for="searchQuery" class="form-label">Search</label>
                    <input type="text" class="form-control" id="searchQuery" placeholder="Search title, author, ISBN...">
                </div>
                <div class="col-md-4">
                    <label for="publisherFilter" class="form-label">Publisher</label>
                    <input type="text" class="form-control" id="publisherFilter" placeholder="Enter publisher name">
                </div>
                <div class="col-md-4">
                    <label for="formatFilter" class="form-label">Format</label>
                    <select class="form-select" id="formatFilter" name="formatFilter">
                        <option value="">All Formats</option>
                        <option value="physical">Physical Books</option>
                        <option value="ebook">E-Books</option>
                        <option value="reference">Reference Books</option>
                        <option value="journal">Journals</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button type="button" class="btn btn-info" onclick="generateBibliography()">
                        <i class="fas fa-search me-2"></i>Generate Bibliography
                    </button>
                    <button type="button" class="btn btn-outline-secondary ms-2" onclick="resetFilters()">
                        <i class="fas fa-refresh me-2"></i>Reset
                    </button>
                    <button type="button" class="btn btn-primary ms-2" onclick="generateCitation()">
                        <i class="fas fa-quote-right me-2"></i>Generate Citations
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Summary Statistics -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-primary">
                    <i class="fas fa-book fa-2x mb-2"></i>
                </div>
                <h4 class="text-primary" id="totalBooks">0</h4>
                <p class="text-muted mb-0 small">Total Books</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-success">
                    <i class="fas fa-user-edit fa-2x mb-2"></i>
                </div>
                <h4 class="text-success" id="totalAuthors">0</h4>
                <p class="text-muted mb-0 small">Authors</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-warning">
                    <i class="fas fa-building fa-2x mb-2"></i>
                </div>
                <h4 class="text-warning" id="totalPublishers">0</h4>
                <p class="text-muted mb-0 small">Publishers</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-info">
                    <i class="fas fa-tags fa-2x mb-2"></i>
                </div>
                <h4 class="text-info" id="totalSubjects">0</h4>
                <p class="text-muted mb-0 small">Subjects</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-danger">
                    <i class="fas fa-language fa-2x mb-2"></i>
                </div>
                <h4 class="text-danger" id="totalLanguages">0</h4>
                <p class="text-muted mb-0 small">Languages</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-dark">
                    <i class="fas fa-calendar fa-2x mb-2"></i>
                </div>
                <h4 class="text-dark" id="latestYear">0</h4>
                <p class="text-muted mb-0 small">Latest Year</p>
            </div>
        </div>
    </div>
</div>

<!-- Bibliography View Tabs -->
<ul class="nav nav-tabs mb-4" id="bibliographyTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="catalog-tab" data-bs-toggle="tab" data-bs-target="#catalog" type="button" role="tab">
            <i class="fas fa-list me-2"></i>Complete Catalog
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="subject-tab" data-bs-toggle="tab" data-bs-target="#subject" type="button" role="tab">
            <i class="fas fa-tags me-2"></i>By Subject
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="author-tab" data-bs-toggle="tab" data-bs-target="#author" type="button" role="tab">
            <i class="fas fa-user-edit me-2"></i>By Author
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="publisher-tab" data-bs-toggle="tab" data-bs-target="#publisher" type="button" role="tab">
            <i class="fas fa-building me-2"></i>By Publisher
        </button>
    </li>
</ul>

<div class="tab-content" id="bibliographyTabContent">
    <!-- Complete Catalog Tab -->
    <div class="tab-pane fade show active" id="catalog" role="tabpanel">
        <div class="card autolib-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-list me-2"></i>Complete Library Catalog</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="catalogTable">
                        <thead class="table-dark">
                            <tr>
                                <th>Access No.</th>
                                <th>Title</th>
                                <th>Author(s)</th>
                                <th>Publisher</th>
                                <th>Year</th>
                                <th>Subject</th>
                                <th>Language</th>
                                <th>ISBN</th>
                                <th>Copies</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="9" class="text-center text-muted">
                                    <i class="fas fa-info-circle me-2"></i>No books in catalog. Add books to see bibliography.
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- By Subject Tab -->
    <div class="tab-pane fade" id="subject" role="tabpanel">
        <div class="card autolib-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-tags me-2"></i>Subject-wise Bibliography</h5>
            </div>
            <div class="card-body" id="subjectBibliography">
                <div class="text-center text-muted">
                    <i class="fas fa-info-circle me-2"></i>No subjects found. Add books to generate subject-wise bibliography.
                </div>
            </div>
        </div>
    </div>
    
    <!-- By Author Tab -->
    <div class="tab-pane fade" id="author" role="tabpanel">
        <div class="card autolib-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user-edit me-2"></i>Author-wise Bibliography</h5>
            </div>
            <div class="card-body" id="authorBibliography">
                <div class="text-center text-muted">
                    <i class="fas fa-info-circle me-2"></i>No authors found. Add books to generate author-wise bibliography.
                </div>
            </div>
        </div>
    </div>
    
    <!-- By Publisher Tab -->
    <div class="tab-pane fade" id="publisher" role="tabpanel">
        <div class="card autolib-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-building me-2"></i>Publisher-wise Bibliography</h5>
            </div>
            <div class="card-body" id="publisherBibliography">
                <div class="text-center text-muted">
                    <i class="fas fa-info-circle me-2"></i>No publishers found. Add books to generate publisher-wise bibliography.
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Load initial bibliography
    generateBibliography();
});

function generateBibliography() {
    const filters = {
        sortBy: $('#sortBy').val(),
        subjectFilter: $('#subjectFilter').val(),
        languageFilter: $('#languageFilter').val(),
        yearRange: $('#yearRange').val(),
        searchQuery: $('#searchQuery').val(),
        publisherFilter: $('#publisherFilter').val(),
        formatFilter: $('#formatFilter').val()
    };
    
    // Show loading
    $('#catalogTable tbody').html('<tr><td colspan="9" class="text-center"><i class="fas fa-spinner fa-spin me-2"></i>Loading bibliography...</td></tr>');
    
    // Simulate API call - replace with actual implementation
    setTimeout(() => {
        loadBibliographyData(filters);
    }, 1000);
}

function loadBibliographyData(filters) {
    // Since database is clean, show zero data
    const catalogData = []; // Empty array for clean database
    
    // Update statistics - all zeros for clean database
    $('#totalBooks').text('0');
    $('#totalAuthors').text('0');
    $('#totalPublishers').text('0');
    $('#totalSubjects').text('0');
    $('#totalLanguages').text('0');
    $('#latestYear').text('N/A');
    
    // Update catalog table
    if (catalogData.length === 0) {
        $('#catalogTable tbody').html(`
            <tr>
                <td colspan="9" class="text-center text-info">
                    <i class="fas fa-info-circle me-2"></i>No books in catalog. Add books to generate bibliography.
                </td>
            </tr>
        `);
    } else {
        let tableHTML = '';
        catalogData.forEach(book => {
            tableHTML += `
                <tr>
                    <td>${book.accessNo}</td>
                    <td>${book.title}</td>
                    <td>${book.author}</td>
                    <td>${book.publisher}</td>
                    <td>${book.year}</td>
                    <td>${book.subject}</td>
                    <td>${book.language}</td>
                    <td>${book.isbn}</td>
                    <td>${book.copies}</td>
                </tr>
            `;
        });
        $('#catalogTable tbody').html(tableHTML);
    }
    
    // Update other tabs
    updateSubjectBibliography(catalogData);
    updateAuthorBibliography(catalogData);
    updatePublisherBibliography(catalogData);
}

function updateSubjectBibliography(data) {
    if (data.length === 0) {
        $('#subjectBibliography').html(`
            <div class="text-center text-info">
                <i class="fas fa-info-circle me-2"></i>No subjects found. Add books to generate subject-wise bibliography.
            </div>
        `);
        return;
    }
    
    // Group by subject and generate bibliography
    const subjects = {};
    data.forEach(book => {
        if (!subjects[book.subject]) {
            subjects[book.subject] = [];
        }
        subjects[book.subject].push(book);
    });
    
    let html = '';
    Object.keys(subjects).sort().forEach(subject => {
        html += `
            <div class="mb-4">
                <h6 class="text-primary border-bottom pb-2">${subject}</h6>
                <ul class="list-unstyled ms-3">
        `;
        
        subjects[subject].forEach(book => {
            html += `
                <li class="mb-2">
                    <strong>${book.title}</strong> by ${book.author}. 
                    ${book.publisher}, ${book.year}. 
                    <small class="text-muted">(${book.accessNo})</small>
                </li>
            `;
        });
        
        html += '</ul></div>';
    });
    
    $('#subjectBibliography').html(html);
}

function updateAuthorBibliography(data) {
    if (data.length === 0) {
        $('#authorBibliography').html(`
            <div class="text-center text-info">
                <i class="fas fa-info-circle me-2"></i>No authors found. Add books to generate author-wise bibliography.
            </div>
        `);
        return;
    }
    
    // Group by author and generate bibliography
    const authors = {};
    data.forEach(book => {
        if (!authors[book.author]) {
            authors[book.author] = [];
        }
        authors[book.author].push(book);
    });
    
    let html = '';
    Object.keys(authors).sort().forEach(author => {
        html += `
            <div class="mb-4">
                <h6 class="text-success border-bottom pb-2">${author}</h6>
                <ul class="list-unstyled ms-3">
        `;
        
        authors[author].forEach(book => {
            html += `
                <li class="mb-2">
                    <strong>${book.title}</strong>. 
                    ${book.publisher}, ${book.year}. 
                    <small class="text-muted">(${book.subject})</small>
                </li>
            `;
        });
        
        html += '</ul></div>';
    });
    
    $('#authorBibliography').html(html);
}

function updatePublisherBibliography(data) {
    if (data.length === 0) {
        $('#publisherBibliography').html(`
            <div class="text-center text-info">
                <i class="fas fa-info-circle me-2"></i>No publishers found. Add books to generate publisher-wise bibliography.
            </div>
        `);
        return;
    }
    
    // Group by publisher and generate bibliography
    const publishers = {};
    data.forEach(book => {
        if (!publishers[book.publisher]) {
            publishers[book.publisher] = [];
        }
        publishers[book.publisher].push(book);
    });
    
    let html = '';
    Object.keys(publishers).sort().forEach(publisher => {
        html += `
            <div class="mb-4">
                <h6 class="text-warning border-bottom pb-2">${publisher}</h6>
                <ul class="list-unstyled ms-3">
        `;
        
        publishers[publisher].forEach(book => {
            html += `
                <li class="mb-2">
                    <strong>${book.title}</strong> by ${book.author}, ${book.year}. 
                    <small class="text-muted">(${book.subject})</small>
                </li>
            `;
        });
        
        html += '</ul></div>';
    });
    
    $('#publisherBibliography').html(html);
}

function resetFilters() {
    $('#filterForm')[0].reset();
    $('#sortBy').val('title');
    generateBibliography();
}

function generateCitation() {
    alert('Citation generation functionality would be implemented here.');
}

function exportReport(format) {
    alert(`Exporting Bibliography Report as ${format.toUpperCase()}...`);
}
</script>
{% endblock %}
