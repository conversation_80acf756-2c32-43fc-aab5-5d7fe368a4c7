{% extends "base.html" %}

{% block title %}No Dues Report - Admin{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>

<!-- Fixed Circulation Menu -->
<div class="nav-item">
    <a class="nav-link fw-bold text-primary" data-bs-toggle="collapse" href="#circulationSubmenu" role="button" aria-expanded="false">
        <i class="fas fa-exchange-alt me-2"></i>Circulation
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse" id="circulationSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_circulation_counter') }}">
                <i class="fas fa-desktop me-2"></i>Counter
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bulk_operations') }}">
                <i class="fas fa-layer-group me-2"></i>Bulk Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_payment_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Payment Management
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_management') }}">
                <i class="fas fa-tools me-2"></i>Binding & Maintenance
            </a>
        </div>
    </div>
</div>

<!-- Fixed Reports Menu -->
<div class="nav-item">
    <a class="nav-link fw-bold text-success" data-bs-toggle="collapse" href="#reportsSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-chart-bar me-2"></i>Reports
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="reportsSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_library_connection_report') }}">
                <i class="fas fa-wifi me-2"></i>Library Connection
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_access_register_report') }}">
                <i class="fas fa-sign-in-alt me-2"></i>Access Register
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bibliography_report') }}">
                <i class="fas fa-book-open me-2"></i>Bibliography
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_counter_report') }}">
                <i class="fas fa-calculator me-2"></i>Counter Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_statistics_report') }}">
                <i class="fas fa-chart-line me-2"></i>Statistics
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_report') }}">
                <i class="fas fa-tools me-2"></i>Binding Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_database_report') }}">
                <i class="fas fa-database me-2"></i>Database Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_member_report') }}">
                <i class="fas fa-users me-2"></i>Member Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_resource_report') }}">
                <i class="fas fa-archive me-2"></i>Resource Report
            </a>
            <a class="nav-link py-1 active" href="{{ url_for('admin_no_dues_report') }}">
                <i class="fas fa-check-circle me-2"></i>No Dues Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_qb_report') }}">
                <i class="fas fa-money-check-alt me-2"></i>QB Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_transfer_report') }}">
                <i class="fas fa-exchange-alt me-2"></i>Transfer Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_missing_report') }}">
                <i class="fas fa-exclamation-triangle me-2"></i>Missing Report
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-check-circle me-3 text-success"></i>No Dues Certificate Report</h2>
        <p class="text-muted mb-0">Students with clear records, graduation clearance, and pending dues tracking</p>
    </div>
    <div>
        <button class="btn btn-success" onclick="exportReport('excel')">
            <i class="fas fa-file-excel me-2"></i>Export Excel
        </button>
        <button class="btn btn-danger ms-2" onclick="exportReport('pdf')">
            <i class="fas fa-file-pdf me-2"></i>Export PDF
        </button>
    </div>
</div>

<!-- Filter Section -->
<div class="card autolib-card mb-4">
    <div class="card-header bg-gradient-success text-white">
        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>No Dues Filters</h5>
    </div>
    <div class="card-body">
        <form id="filterForm">
            <div class="row">
                <div class="col-md-3">
                    <label for="clearanceType" class="form-label">Clearance Type</label>
                    <select class="form-select" id="clearanceType" name="clearanceType">
                        <option value="">All Types</option>
                        <option value="graduation">Graduation Clearance</option>
                        <option value="semester">Semester Clearance</option>
                        <option value="transfer">Transfer Clearance</option>
                        <option value="general">General No Dues</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="department" class="form-label">Department</label>
                    <select class="form-select" id="department" name="department">
                        <option value="">All Departments</option>
                        <option value="CSE">Computer Science</option>
                        <option value="IT">Information Technology</option>
                        <option value="ECE">Electronics & Communication</option>
                        <option value="EEE">Electrical & Electronics</option>
                        <option value="MECH">Mechanical</option>
                        <option value="CIVIL">Civil</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="yearOfStudy" class="form-label">Year of Study</label>
                    <select class="form-select" id="yearOfStudy" name="yearOfStudy">
                        <option value="">All Years</option>
                        <option value="1">First Year</option>
                        <option value="2">Second Year</option>
                        <option value="3">Third Year</option>
                        <option value="4">Fourth Year</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="statusFilter" class="form-label">Dues Status</label>
                    <select class="form-select" id="statusFilter" name="statusFilter">
                        <option value="">All Status</option>
                        <option value="clear">No Dues (Clear)</option>
                        <option value="pending">Pending Dues</option>
                        <option value="overdue">Overdue Books</option>
                        <option value="fine">Pending Fines</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-6">
                    <label for="searchStudent" class="form-label">Search Student</label>
                    <input type="text" class="form-control" id="searchStudent" placeholder="Enter name, ID, or roll number">
                </div>
                <div class="col-md-3">
                    <label for="dateFrom" class="form-label">From Date</label>
                    <input type="date" class="form-control" id="dateFrom" name="dateFrom">
                </div>
                <div class="col-md-3">
                    <label for="dateTo" class="form-label">To Date</label>
                    <input type="date" class="form-control" id="dateTo" name="dateTo">
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button type="button" class="btn btn-success" onclick="generateReport()">
                        <i class="fas fa-search me-2"></i>Generate Report
                    </button>
                    <button type="button" class="btn btn-outline-secondary ms-2" onclick="resetFilters()">
                        <i class="fas fa-refresh me-2"></i>Reset
                    </button>
                    <button type="button" class="btn btn-primary ms-2" onclick="generateCertificates()">
                        <i class="fas fa-certificate me-2"></i>Generate Certificates
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- No Dues Statistics -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-success">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                </div>
                <h4 class="text-success" id="clearStudents">0</h4>
                <p class="text-muted mb-0 small">Clear Students</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-warning">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                </div>
                <h4 class="text-warning" id="pendingDues">0</h4>
                <p class="text-muted mb-0 small">Pending Dues</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-danger">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                </div>
                <h4 class="text-danger" id="overdueBooks">0</h4>
                <p class="text-muted mb-0 small">Overdue Books</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-info">
                    <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                </div>
                <h4 class="text-info" id="pendingFines">₹0</h4>
                <p class="text-muted mb-0 small">Pending Fines</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-primary">
                    <i class="fas fa-graduation-cap fa-2x mb-2"></i>
                </div>
                <h4 class="text-primary" id="graduationReady">0</h4>
                <p class="text-muted mb-0 small">Graduation Ready</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-dark">
                    <i class="fas fa-certificate fa-2x mb-2"></i>
                </div>
                <h4 class="text-dark" id="certificatesIssued">0</h4>
                <p class="text-muted mb-0 small">Certificates Issued</p>
            </div>
        </div>
    </div>
</div>

<!-- No Dues Tabs -->
<ul class="nav nav-tabs mb-4" id="noDuesTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="clear-students-tab" data-bs-toggle="tab" data-bs-target="#clear-students" type="button" role="tab">
            <i class="fas fa-check-circle me-2"></i>Clear Students
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="pending-dues-tab" data-bs-toggle="tab" data-bs-target="#pending-dues" type="button" role="tab">
            <i class="fas fa-clock me-2"></i>Pending Dues
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="graduation-clearance-tab" data-bs-toggle="tab" data-bs-target="#graduation-clearance" type="button" role="tab">
            <i class="fas fa-graduation-cap me-2"></i>Graduation Clearance
        </button>
    </li>
</ul>

<div class="tab-content" id="noDuesTabContent">
    <!-- Clear Students Tab -->
    <div class="tab-pane fade show active" id="clear-students" role="tabpanel">
        <div class="card autolib-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-check-circle me-2"></i>Students with No Dues</h5>
                <div>
                    <button class="btn btn-success btn-sm" onclick="bulkGenerateCertificates()">
                        <i class="fas fa-certificate me-2"></i>Bulk Generate Certificates
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="clearStudentsTable">
                        <thead class="table-dark">
                            <tr>
                                <th>
                                    <input type="checkbox" class="form-check-input" id="selectAllClear">
                                </th>
                                <th>Student ID</th>
                                <th>Name</th>
                                <th>Department</th>
                                <th>Year</th>
                                <th>Last Activity</th>
                                <th>Clearance Type</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="9" class="text-center text-success">
                                    <i class="fas fa-check-circle me-2"></i>No students registered yet. All registered students will appear here when they have no dues.
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Pending Dues Tab -->
    <div class="tab-pane fade" id="pending-dues" role="tabpanel">
        <div class="card autolib-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Students with Pending Dues</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="pendingDuesTable">
                        <thead class="table-dark">
                            <tr>
                                <th>Student ID</th>
                                <th>Name</th>
                                <th>Department</th>
                                <th>Overdue Books</th>
                                <th>Pending Fine (₹)</th>
                                <th>Days Overdue</th>
                                <th>Last Reminder</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="8" class="text-center text-success">
                                    <i class="fas fa-check-circle me-2"></i>No pending dues found. All students are clear!
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Graduation Clearance Tab -->
    <div class="tab-pane fade" id="graduation-clearance" role="tabpanel">
        <div class="card autolib-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-graduation-cap me-2"></i>Graduation Clearance Status</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Graduation Clearance Process:</strong> Students must return all books and clear all fines before receiving their no dues certificate for graduation.
                </div>
                
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="graduationTable">
                        <thead class="table-dark">
                            <tr>
                                <th>Student ID</th>
                                <th>Name</th>
                                <th>Department</th>
                                <th>Year</th>
                                <th>Books Status</th>
                                <th>Fine Status</th>
                                <th>Clearance Status</th>
                                <th>Certificate</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="8" class="text-center text-muted">
                                    <i class="fas fa-info-circle me-2"></i>No final year students found.
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Set default dates (current month)
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    
    $('#dateTo').val(today.toISOString().split('T')[0]);
    $('#dateFrom').val(firstDay.toISOString().split('T')[0]);
    
    // Load initial data
    generateReport();
    
    // Handle select all checkbox
    $('#selectAllClear').on('change', function() {
        $('.student-checkbox').prop('checked', this.checked);
    });
});

function generateReport() {
    const filters = {
        clearanceType: $('#clearanceType').val(),
        department: $('#department').val(),
        yearOfStudy: $('#yearOfStudy').val(),
        statusFilter: $('#statusFilter').val(),
        searchStudent: $('#searchStudent').val(),
        dateFrom: $('#dateFrom').val(),
        dateTo: $('#dateTo').val()
    };
    
    // Show loading
    $('#clearStudentsTable tbody').html('<tr><td colspan="9" class="text-center"><i class="fas fa-spinner fa-spin me-2"></i>Loading no dues data...</td></tr>');
    
    // Simulate API call - replace with actual implementation
    setTimeout(() => {
        loadNoDuesData(filters);
    }, 1000);
}

function loadNoDuesData(filters) {
    // Since database is clean, show zero data
    const clearStudents = []; // Empty array for clean database
    const pendingDues = []; // Empty array for clean database
    const graduationData = []; // Empty array for clean database
    
    // Update statistics - all zeros for clean database
    $('#clearStudents').text('0');
    $('#pendingDues').text('0');
    $('#overdueBooks').text('0');
    $('#pendingFines').text('₹0');
    $('#graduationReady').text('0');
    $('#certificatesIssued').text('0');
    
    // Update clear students table
    if (clearStudents.length === 0) {
        $('#clearStudentsTable tbody').html(`
            <tr>
                <td colspan="9" class="text-center text-success">
                    <i class="fas fa-check-circle me-2"></i>No students registered yet. All registered students will appear here when they have no dues.
                </td>
            </tr>
        `);
    }
    
    // Update pending dues table
    if (pendingDues.length === 0) {
        $('#pendingDuesTable tbody').html(`
            <tr>
                <td colspan="8" class="text-center text-success">
                    <i class="fas fa-check-circle me-2"></i>No pending dues found. All students are clear!
                </td>
            </tr>
        `);
    }
    
    // Update graduation table
    if (graduationData.length === 0) {
        $('#graduationTable tbody').html(`
            <tr>
                <td colspan="8" class="text-center text-muted">
                    <i class="fas fa-info-circle me-2"></i>No final year students found.
                </td>
            </tr>
        `);
    }
}

function resetFilters() {
    $('#filterForm')[0].reset();
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    
    $('#dateTo').val(today.toISOString().split('T')[0]);
    $('#dateFrom').val(firstDay.toISOString().split('T')[0]);
    
    generateReport();
}

function generateCertificates() {
    const selectedStudents = $('.student-checkbox:checked').map(function() {
        return this.value;
    }).get();
    
    if (selectedStudents.length === 0) {
        alert('Please select students to generate certificates for.');
        return;
    }
    
    if (confirm(`Generate no dues certificates for ${selectedStudents.length} student(s)?`)) {
        alert('No dues certificates generated successfully!');
        generateReport();
    }
}

function bulkGenerateCertificates() {
    if (confirm('Generate no dues certificates for all clear students?')) {
        alert('Bulk certificates generated successfully!');
        generateReport();
    }
}

function generateSingleCertificate(studentId) {
    if (confirm(`Generate no dues certificate for student ${studentId}?`)) {
        alert('No dues certificate generated successfully!');
        generateReport();
    }
}

function sendReminder(studentId) {
    if (confirm(`Send dues reminder to student ${studentId}?`)) {
        alert('Reminder sent successfully!');
    }
}

function exportReport(format) {
    alert(`Exporting No Dues Report as ${format.toUpperCase()}...`);
}
</script>
{% endblock %}
